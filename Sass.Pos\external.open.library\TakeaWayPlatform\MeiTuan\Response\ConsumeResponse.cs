﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace external.open.library.TakeaWayPlatform.MeiTuan.Response
{
    public class ConsumeResponse
    {
        /// <summary>
        /// 订单id
        /// </summary>
        public string order_id { get; set; }
        /// <summary>
        /// 验证券码
        /// </summary>
        public string receipt_code { get; set; }
        /// <summary>
        /// 第三方的店铺id，不提倡使用，app_shop_id和open_shop_uuid二选一，必须填写一个
        /// </summary>
        public string app_shop_id { get; set; }
        /// <summary>
        /// 美团点评店铺id，建议使用，app_shop_id和open_shop_uuid二选一，必须填写一个
        /// </summary>
        public string open_shop_uuid { get; set; }
        /// <summary>
        /// 套餐id（若验证的券所对应的商品为团购时，该字段必返回）
        /// </summary>
        public Nullable<long> deal_id { get; set; }
        /// <summary>
        /// 团购id,团购id与套餐id是一对多的关系（若验证的券所对应的商品为团购时，该字段必返回）
        /// </summary>
        public Nullable<long> dealgroup_id { get; set; }
        /// <summary>
        /// 商品id（若验证的券所对应的商品非团购时，该字段必返回，product_item_id含义参考商品管理API）
        /// </summary>
        public Nullable<long> product_item_id { get; set; }
        /// <summary>
        /// 商品类型 1、泛商品如丽人派样活动商品等（若验证的券所对应的商品非团购时，该字段必返回）
        /// </summary>
        public Nullable<int> product_type { get; set; }
        /// <summary>
        /// 商品名称
        /// </summary>
        public string deal_title { get; set; }
        /// <summary>
        /// 商品售卖价格
        /// </summary>
        public double deal_price { get; set; }
        /// <summary>
        /// 商品市场价
        /// </summary>
        public double deal_marketprice { get; set; }
        /// <summary>
        /// 业务类型 0:普通团购  203:拼团 205:次卡 217:通兑标品
        /// </summary>
        public Nullable<int> biz_type { get; set; }
        /// <summary>
        /// 该券码所在的订单的支付明细，如果一笔订单包含两个券码a、b，在核销a、b券码时返回信息一致，都是该订单的支付明细
        /// </summary>
        public List<PaymentDetailDTO> payment_detail { get; set; }
        /// <summary>
        /// 券过期时间
        /// </summary>
        public long receiptEndDate { get; set; }
        /// <summary>
        /// 用户手机号，形如：185****1212
        /// </summary>
        public string mobile { get; set; }
        /// <summary>
        /// 是否展示完整号码,1:展示；0或null：隐藏
        /// </summary>
        public Nullable<int> disclose_mobile_no { get; set; }
    }

    public class PaymentDetailDTO
    {
        /// <summary>
        /// 支付详情id	
        /// </summary>
        public string payment_detail_id { get; set; }
        /// <summary>
        /// 支付金额	
        /// </summary>
        public decimal amount { get; set; }
        /// <summary>
        /// 类型说明：
        /// 2：抵用券
        /// 5：积分
        /// 6：立减
        /// 8：商户抵用券
        /// 10：C端美团支付
        /// 12：优惠代码
        /// 15：美团立减
        /// 17：商家立减
        /// 18：美团商家立减
        /// 21：次卡
        /// 22：打折卡
        /// 23：B端美团支付
        /// 24：全渠道会员券
        /// 25：pos支付
        /// 26：线下认款平台
        /// 28：商家折上折
        /// 29：美团分销支付
        /// </summary>
        public long amount_type { get; set; }
    }
}
