﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Collaboration.Context;
using Saas.Pos.Model.Collaboration.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Collaboration
{
    /// <summary>
    /// 系统内部服务驱动
    /// </summary>
    public class InternalCollaborationDrive : DriveBase
    {
        /// <summary>
        /// 新增计量类型项目表数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public virtual ResponseContext<int> CreateCategory(CreateCategoryContext context)
        {
            return ActionFun.Run(context, () =>
            {
                //判断同样类型是否重复添加
                if (app.SaasPos.Energy_Metering_Category.FindEntity(w => w.CateName == context.CateName && w.Type == context.Type && w.ShopId == context.ShopId) != null)
                    throw new ExMessage("加入的计量类型已存在！");
                //插入数据
                return app.SaasPos.Energy_Metering_Category.Insert(new Model.SaasPos.Energy_Metering_Category
                {
                    ShopId = context.ShopId,
                    CateName = context.CateName,
                    Type = context.Type
                });
            });
        }
        /// <summary>
        /// 修改计量类型项目表数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public virtual ResponseContext<int> UpdateCategory(UpdateCategoryContext context)
        {
            return ActionFun.Run(context, () =>
            {
                //判断同样类型是否重复添加
                if (app.SaasPos.Energy_Metering_Category.FindEntity(w => w.CateName == context.CateName && w.Type == context.Type && w.ShopId == context.ShopId) != null)
                    throw new ExMessage("需要修改的计量类型已存在！");
                var entity = app.SaasPos.Energy_Metering_Category.FindEntity(w => w.Id == context.Id);
                if (entity == null)
                    throw new ExMessage("计量类型不存在！");

                //修改实体数据
                entity.ShopId = context.ShopId;
                entity.Type = context.Type;
                entity.CateName = context.CateName;

                //修改数据
                return app.SaasPos.Energy_Metering_Category.Update(entity);
            });
        }
        /// <summary>
        /// 删除计量类型项目表数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public virtual ResponseContext<int> DeleteCategory(DeleteCategoryContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var entity = app.SaasPos.Energy_Metering_Category.FindEntity(w => w.Id == context.Id);
                if (entity == null)
                    throw new ExMessage("计量类型不存在！");

                //删除数据
                return app.SaasPos.Energy_Metering_Category.Delete(entity);
            });
        }
        /// <summary>
        /// 查询计量类型项目表列表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public virtual ResponseContext<List<Model.SaasPos.Energy_Metering_Category>> GetCateList(GetCategoryListContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var query = app.SaasPos.Energy_Metering_Category.IQueryable(w => w.ShopId == context.ShopId);
                return query.ToList();
            });
        }
        /// <summary>
        /// 创建计量类型明细数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public virtual ResponseContext<int> CreateItem(CreateCategoryItemContext context)
        {
            return ActionFun.Run(context, () =>
            {
                if (app.SaasPos.Energy_Metering_CategoryItem.FindEntity(w => w.ItemName == context.ItemName && w.CateId == context.CateId) != null)
                    throw new ExMessage("加入的计量类型明细已存在！");

                return app.SaasPos.Energy_Metering_CategoryItem.Insert(new Model.SaasPos.Energy_Metering_CategoryItem()
                {
                    CateId = context.CateId,
                    ItemName = context.ItemName
                });
            });
        }
        /// <summary>
        /// 修改计量类型明细数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public virtual ResponseContext<int> UpdateItem(UpdateCategoryItemContext context)
        {
            return ActionFun.Run(context, () =>
            {
                if (app.SaasPos.Energy_Metering_CategoryItem.FindEntity(w => w.ItemName == context.ItemName && w.CateId == context.CateId) != null)
                    throw new ExMessage("需要修改的计量类型明细已存在！");

                var entity = app.SaasPos.Energy_Metering_CategoryItem.FindEntity(w => w.Id == context.Id);
                if (entity == null)
                    throw new ExMessage("计量类型明细不存在！");

                entity.CateId = context.CateId;
                entity.ItemName = context.ItemName;

                return app.SaasPos.Energy_Metering_CategoryItem.Update(entity);
            });
        }
        /// <summary>
        /// 删除计量类型明细数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public virtual ResponseContext<int> DeleteItem(DeleteCategoryItemContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var entity = app.SaasPos.Energy_Metering_CategoryItem.FindEntity(w => w.Id == context.Id);
                if (entity == null)
                    throw new ExMessage("计量类型不存在！");

                //删除数据
                return app.SaasPos.Energy_Metering_CategoryItem.Delete(entity);
            });
        }
        /// <summary>
        /// 查询计量类型明细列表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public virtual ResponseContext<List<CategoryItemListModel>> GetItemList(GetCategoryItemListContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var query = app.SaasPos.Energy_Metering_Category.IQueryable(w => w.ShopId == context.ShopId).AsEnumerable();
                var itemQuery = app.SaasPos.Energy_Metering_CategoryItem.IQueryable().AsEnumerable();

                var list = (from qu in query
                            join item in itemQuery on qu.Id equals item.CateId
                            select new CategoryItemListModel()
                            {
                                CateName = qu.CateName,
                                ItemName = item.ItemName,
                                Type = qu.Type,
                                ItemId = item.Id
                            }).ToList();

                return list;
            });
        }
        /// <summary>
        /// 添加操作记录
        /// </summary>
        /// <returns></returns>
        public virtual ResponseContext<int> CreateRecord(CreateRecordContext context)
        {
            return ActionFun.Run(context, () =>
            {
                if (context.Data.Count <= 0)
                    throw new ExMessage("保存的记录不可为空！");

                var record = new Model.SaasPos.Energy_Metering_Record()
                {
                    CreateTime = DateTime.Now,
                    ShopId = context.ShopId
                };
                app.SaasPos.Energy_Metering_Record.Insert(record);

                var recordItems = context.Data.Select(w => new Model.SaasPos.Energy_Metering_RecordItem()
                {
                    RecordId = record.Id,
                    ItemId = w.CateItemId,
                    Val = w.Value
                }).ToList();

                return app.SaasPos.Energy_Metering_RecordItem.Insert(recordItems);
            });
        }
        /// <summary>
        /// 获取最后一次添加的操作记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public virtual ResponseContext<List<GetRecordItemModel>> GetLatest(GetRecordItemContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var result = new List<GetRecordItemModel>();
                //取出当前门店插入数据库中的最后一条
                var record = app.SaasPos.Energy_Metering_Record.IQueryable(w => w.ShopId == context.ShopId).OrderByDescending(w => w.CreateTime).FirstOrDefault();

                //如果是第一次插入数据，就需要调整一下，需要返回配置所有列表，所有的Val都为null
                if (record == null)
                {
                    var cateList = app.SaasPos.Energy_Metering_Category.IQueryable(w => w.ShopId == context.ShopId);
                    var cateIds = cateList.Select(w => w.Id).ToList();
                    var cateItemList = app.SaasPos.Energy_Metering_CategoryItem.IQueryable(w => cateIds.Contains(w.CateId)).ToList();

                    result = (from cate in cateList
                              join item in cateItemList on cate.Id equals item.CateId
                              select new GetRecordItemModel()
                              {
                                  CateName = cate.CateName,
                                  CateItemId = item.Id,
                                  Type = cate.Type,
                                  CateItemName = item.ItemName,
                                  Val = string.Empty
                              }).ToList();
                }
                else
                {
                    var recordItems = app.SaasPos.Energy_Metering_RecordItem.IQueryable(w => w.RecordId == record.Id).ToList();
                    var cateList = app.SaasPos.Energy_Metering_Category.IQueryable(w => w.ShopId == context.ShopId).ToList();
                    var cateIds = cateList.Select(w => w.Id).ToList();
                    var cateItemList = app.SaasPos.Energy_Metering_CategoryItem.IQueryable(w => cateIds.Contains(w.CateId)).ToList();

                    result = (from rItem in recordItems
                              join cItem in cateItemList on rItem.ItemId equals cItem.Id
                              join cate in cateList on cItem.CateId equals cate.Id
                              select new GetRecordItemModel()
                              {
                                  CateName = cate.CateName,
                                  Type = cate.Type,
                                  CateItemId = cItem.Id,
                                  CateItemName = cItem.ItemName,
                                  Val = rItem.Val
                              }).ToList();
                }

                return result;
            });
        }
        /// <summary>
        /// 获取添加记录列表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public virtual ResponseContext<List<GetRecordItemListModel>> GetRecordItemList(GetRecordItemListContext context)
        {
            return ActionFun.Run(context, () =>
            {
                return app.SaasPos.Energy_Metering_Record.GetRecordItemList(context);
            });
        }
    }
}