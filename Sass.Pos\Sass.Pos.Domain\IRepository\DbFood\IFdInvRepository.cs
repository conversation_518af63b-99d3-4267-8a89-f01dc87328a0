﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.DbFood
{
    public partial interface IFdInvRepository : IRepositoryBase<FdInv>
    {
        bool PlaceOrder(PlaceOrderModel model);

        PaymentInfo GetPayInfo(GetPaymentBillInfoContext context);

        List<GetConsumeBillDataModel> GetConsumeBillData(GetConsumeBillDataDalContext context);

        List<GetRoomCloseLabelRecordDataDalModel> GetRoomCloseLabelRecordData(GetRoomCloseLabelRecordDataContext context);

        List<GetConsumeBillInvNoDataExModel> GetConsumeBillInvNoData(GetConsumeBillInvNoDataContext context);

        List<GetRoomCloseLabelRecordDataDalModel> GetConsumeBillData(GetConsumeBillInvNoDataContext context);

        List<DbFoodBusinessReportData> GetBusinessData(DbFoodBusinessReportContext context);

        List<GetFdInvListModel> GetFdList(GetFdInvListContext context);

        int ChangeFdInvStatus(string invNo, int returnStatus);
    }
}
