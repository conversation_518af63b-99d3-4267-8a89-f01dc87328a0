﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    /// <summary>
    /// 天王功能快捷打单接口
    /// </summary>
    public interface ITimeFrameService
    {
        /// <summary>
        /// 获取时段商品数据
        /// </summary>
        [OperationContract]
        ResponseContext<List<GetTimeFrameModel>> GetTimeFrameData(GetTimeFrameContext context);

        /// <summary>
        /// 修改商品信息
        /// </summary>
        [OperationContract]
        ResponseContext<int> EditTimeFrame(EditTimeFrameContext context);

        /// <summary>
        /// 获取下单页面信息
        /// </summary>
        [OperationContract]
        ResponseContext<GetOrderData> GetOrderData(GetOrderDataContext context);

        /// <summary>
        /// 批量保存商品信息
        /// </summary>
        [OperationContract]
        ResponseContext<int> InsertCashierData(List<InsertCashier> context);
    }
}
