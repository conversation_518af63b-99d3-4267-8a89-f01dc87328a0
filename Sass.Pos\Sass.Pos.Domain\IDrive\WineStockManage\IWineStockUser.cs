﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.WineStockManage
{
    public interface IWineStockUser
    {
        ResponseContext<List<UserCustWineData>> GetUserDrinksData(UserCustDataContext context);

        ResponseContext<GetFdTypeDatasModel> GetFdTypeData(GetFdTypeDataContext context);

        ResponseContext<List<GetFdDataModel>> GetFdData(GetFdDataContext context);

        ResponseContext<GetWineDataByBarIdModel> GetWineDataByBarId(GetWineDataByBarIdContext context);

        ResponseContext<RespPaginationModel<GetWineMainDataOnVueModel>> GetWineMainDataOnVue(GetUserWineDataOnVueContext context);

        ResponseContext<List<GetWineDetailDataOnVueModel>> GetWineDetailDataOnVue(GetWineDetailDataOnVueContext context);

        ResponseContext<WineStorkAuthModel> WineStorkAuth(WineStorkAuthContext context);
    }
}
