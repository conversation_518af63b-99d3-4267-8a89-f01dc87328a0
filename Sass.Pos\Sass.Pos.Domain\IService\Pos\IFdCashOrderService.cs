﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    public interface IFdCashOrderService
    {
        [OperationContract]
        ResponseContext<List<GetFdCashOrderInfoModel>> GetOrderInfo(GetFdCashOrderInfoContext context);

        [OperationContract]
        ResponseContext<List<GetFdCashOrderInfoModel>> GetOrderInfoByStore(GetFdCashOrderInfoContext context);

        [OperationContract]
        ResponseContext<List<GetFdCashOrderDataModel>> GetFdCashOrderData(GetFdCashOrderDataContext context);

        [OperationContract]
        ResponseContext<List<GetFdCashOrderDataModel>> GetFdCashOrderDataByStore(GetFdCashOrderDataContext context);
    }
}
