﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace external.open.library.TakeaWayPlatform.Model
{
    public class ConsumeResponseModel
    {
        /// <summary>
        /// 第三方平台的订单编号
        /// </summary>
        public string OrderId { get; set; }
        /// <summary>
        /// 验券号码
        /// </summary>
        public string ReceiptCode { get; set; }
        /// <summary>
        /// 第三方平台的门店号码
        /// </summary>
        public string OpenShopId { get; set; }
        /// <summary>
        /// 美团平台的GroupId（美团商品ID）
        /// </summary>
        public string DealGroupId { get; set; }
        /// <summary>
        /// 商品明细ID
        /// </summary>
        public string ProductItemId { get; set; }
        /// <summary>
        /// 抖音代表一张券码的标识(撤销时需要)
        /// </summary>
        public string CertificateId { get; set; }
        /// <summary>
        /// 代表券码一次核销的标识(抖音撤销时需要)/美团平台的套餐id（团购必返回）
        /// </summary>
        public string VerifyId { get; set; }
        /// <summary>
        /// 0代表校验成功，非0表示校验失败
        /// </summary>
        public int Result { get; set; }
        /// <summary>
        /// 加密后的券码（抖音）
        /// </summary>
        public string EncryptedCode { get; set; }

        public int Count { get; set; }
        /// <summary>
        /// 原价
        /// </summary>
        public decimal OriginalAmount { get; set; }
        /// <summary>
        /// 售卖价格
        /// </summary>
        public decimal MerchantAmount { get; set; }
        /// <summary>
        /// 用户实付金额
        /// </summary>
        public decimal PayAmount { get; set; }
        /// <summary>
        /// 商家优惠
        /// </summary>
        public decimal BusDis { get; set; }
        /// <summary>
        /// 平台优惠
        /// </summary>
        public decimal PlatformDis { get; set; }
        /// <summary>
        /// 卡券名称
        /// </summary>
        public string Title { get; set; }
    }

    public class ConsumeResponseModel1
    {
        /// <summary>
        /// 本次核销卡券信息
        /// </summary>
        public List<ConsumeCertificateModel> Certificate { get; set; }

        /// <summary>
        /// 该订单维度所有卡券信息
        /// </summary>
        public List<TotalCertificateModel> TotalCertificate { get; set; }
    }

    public class ConsumeCertificateModel 
    {
        /// <summary>
        /// 第三方平台的订单编号
        /// </summary>
        public string OrderId { get; set; }
        /// <summary>
        /// 验券号码
        /// </summary>
        public string ReceiptCode { get; set; }
        /// <summary>
        /// 第三方平台的门店号码
        /// </summary>
        public string OpenShopId { get; set; }
        /// <summary>
        /// 美团平台的GroupId（美团商品ID）
        /// </summary>
        public string DealGroupId { get; set; }
        /// <summary>
        /// 商品明细ID
        /// </summary>
        public string ProductItemId { get; set; }
        /// <summary>
        /// 抖音代表一张券码的标识(撤销时需要)
        /// </summary>
        public string CertificateId { get; set; }
        /// <summary>
        /// 代表券码一次核销的标识(抖音撤销时需要)/美团平台的套餐id（团购必返回）
        /// </summary>
        public string VerifyId { get; set; }
        /// <summary>
        /// 0代表校验成功，非0表示校验失败
        /// </summary>
        public int Result { get; set; }
        /// <summary>
        /// 加密后的券码（抖音）
        /// </summary>
        public string EncryptedCode { get; set; }

        public int Count { get; set; }
        /// <summary>
        /// 原价
        /// </summary>
        public decimal OriginalAmount { get; set; }
        /// <summary>
        /// 售卖价格
        /// </summary>
        public decimal MerchantAmount { get; set; }
        /// <summary>
        /// 用户实付金额
        /// </summary>
        public decimal PayAmount { get; set; }
        /// <summary>
        /// 卡券名称
        /// </summary>
        public string Title { get; set; }
    }

    public class TotalCertificateModel 
    {
        public string ReceiptCode { get; set; }
        /// <summary>
        /// 1：未使用2；已使用3：失效4:退款成功5：退款失败6：退款中
        /// </summary>
        public int Status { get; set; }
    }
}
