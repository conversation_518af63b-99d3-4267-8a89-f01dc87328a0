﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.DbFood
{
    public partial interface IGiftAccountRepository : IRepositoryBase<GiftAccount>
    {
        List<GetGiftAccSceDataModel> GetGiftAccSceData(GetGiftAccSceDataContext context);

        List<GetGiftAccOpenterRecordDataModel> GetGiftAccOpenterRecordData(GetGiftAccOpenterRecordDataContext context);

        List<GetAccountOperationRecordModel> GetAccountOperationRecord(GetAccountOperationRecordContext context);
    }
}
