﻿using Saas.Pos.Common.Log;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Exceptions
{
    /// <summary>
    /// 客户服务端异常处理类
    /// </summary>
    public class ClientException : ExMessage
    {
        public override string Message
        {
            get
            {
                var message = "请求发生错误，原消息：" + base.Message;
                message += "\r\n源：" + base.Source;
                message += "\r\n异常堆栈：" + base.StackTrace;
                LogHelper.Error(message + base.Message);
                if (base.Message.Contains("http"))
                    return "门店服务异常，请联系网络部检查后台服务！";

                return base.Message;
            }
        }

        public ClientException(string message) : base(message, ComponentApplicationServiceInterface.Context.Response.ResponseType.error)
        {

        }
    }
}
