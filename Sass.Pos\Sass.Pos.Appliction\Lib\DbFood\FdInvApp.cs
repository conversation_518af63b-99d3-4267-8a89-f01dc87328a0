﻿using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
    public partial class FdInvApp : AppBase<FdInv>
    {
        public bool PlaceOrder(PlaceOrderModel model)
        {
            if (model == null)
                return false;

            if (model.CashUserId == null && !string.IsNullOrEmpty(model.InputUserId))
                model.CashUserId = model.InputUserId;
            if (string.IsNullOrEmpty(model.ChechUserId))
                model.ChechUserId = string.Empty;

            return Repository.FdInv.PlaceOrder(model);
        }


        public PaymentInfo GetPayInfo(GetPaymentBillInfoContext context)
        {
            return Repository.FdInv.GetPayInfo(context);
        }

        /// <summary>
        /// 查询晚上20点后消费账单明细
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetConsumeBillDataModel> GetConsumeBillData(GetConsumeBillDataDalContext context)
        {
            var data = Repository.FdInv.GetConsumeBillData(context);

            data = data.Select(i => { i.WorkDate = DateTime.ParseExact(i.newWorkDate, "yyyyMMdd", CultureInfo.InvariantCulture); return i; }).ToList();

            return data;
        }

        /// <summary>
        /// 查询清单时消费人数等数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetRoomCloseLabelRecordDataModel> GetRoomCloseLabelRecordData(GetRoomCloseLabelRecordDataContext context)
        {
            var list = Repository.FdInv.GetRoomCloseLabelRecordData(context);

            var data = list.OrderBy(i => i.CashTime).GroupBy(i => i.InvNo)
                        .Select(i => new GetRoomCloseLabelRecordDataModel()
                        {
                            LabelData = new RoomCloseLabel()
                            {
                                InvNo = i.Key,
                                MainXFType = i.Where(j => !string.IsNullOrEmpty(j.Type))
                                 .FirstOrDefault()?.Type ?? string.Empty,
                                XFPeoNumber = i.Sum(j => j.PeoNumber),
                                WorkDate = i.FirstOrDefault().WorkDate,
                                XFCash = i.FirstOrDefault().Cash,
                                XFReturnAccount = i.FirstOrDefault().ReturnAccount,
                                XFTGPay = i.Sum(j => j.TGPay),
                            },
                            DetailData = i.Where(j => !string.IsNullOrEmpty(j.Type))
                                        .Select(j => new RoomCloseLabelTypeDetail()
                                        {
                                            FdNo = j.FdNo,
                                            XFType = j.Type,
                                            TGPay = j.TGPay,
                                        }).ToList()
                        }).ToList();
            data = data.Where(i => !string.IsNullOrEmpty(i.LabelData.MainXFType)).ToList();
            return data;
        }

        /// <summary>
        /// 查询清单时消费人数等数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetRoomCloseLabelRecordDataModel> GetRoomCloseLabelRecordExData(GetConsumeBillInvNoDataContext context)
        {
            var list = Repository.FdInv.GetConsumeBillData(context);

            var data = list.OrderBy(i => i.CashTime).GroupBy(i => i.InvNo)
                        .Select(i => new GetRoomCloseLabelRecordDataModel()
                        {
                            LabelData = new RoomCloseLabel()
                            {
                                InvNo = i.Key,
                                MainXFType = i.Where(j => !string.IsNullOrEmpty(j.Type))
                                 .FirstOrDefault()?.Type ?? string.Empty,
                                XFPeoNumber = i.Sum(j => j.PeoNumber),
                                WorkDate = i.FirstOrDefault().WorkDate,
                                XFCash = i.FirstOrDefault().Cash,
                                XFReturnAccount = i.FirstOrDefault().ReturnAccount,
                                XFTGPay = i.Sum(j => j.TGPay),
                            },
                            DetailData = i.Where(j => !string.IsNullOrEmpty(j.Type))
                                        .Select(j => new RoomCloseLabelTypeDetail()
                                        {
                                            FdNo = j.FdNo,
                                            XFType = j.Type,
                                            TGPay = j.TGPay,
                                        }).ToList()
                        }).ToList();
            data = data.Where(i => !string.IsNullOrEmpty(i.LabelData.MainXFType)).ToList();
            return data;
        }

        /// <summary>
        /// 根据日期范围查询需要记录的清单
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetConsumeBillInvNoDataExModel> GetConsumeBillInvNoData(GetConsumeBillInvNoDataContext context)
        {
            return Repository.FdInv.GetConsumeBillInvNoData(context);
        }

        public List<DbFoodBusinessReportData> GetBusinessData(DbFoodBusinessReportContext context)
        {
            return Repository.FdInv.GetBusinessData(context);
        }

        public List<GetFdInvListModel> GetFdList(GetFdInvListContext context)
        {
            return Repository.FdInv.GetFdList(context);
        }

        public int ChangeFdInvStatus(string invNo, int returnStatus)
        {
            return Repository.FdInv.ChangeFdInvStatus(invNo, returnStatus);
        }
    }
}
