﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Report.Context;
using Saas.Pos.Model.Report.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService
{
    /// <summary>
    /// 报表功能
    /// </summary>
    [ServiceContract]
    public interface ISaasPosReport
    {
        void TestReport();

        /// <summary>
        /// 卡券派发销售相关操作
        /// </summary>
        [OperationContract]
        ResponseContext<int> Test(string context);

        /// <summary>
        /// 查询预约人数与开房人数
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<List<GetBookNumberModel>> BookNumberReport(GetBookNumberContext context);

    }
}
