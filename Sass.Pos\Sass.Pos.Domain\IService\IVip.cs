﻿using ComponentApplicationServiceInterface.Context.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService
{
    [ServiceContract]
    public interface IVip
    {

        /// <summary>
        /// 卡券派发销售相关操作
        /// </summary>
        [OperationContract]
        ResponseContext<string> ApiV2(string context);
    }
}
