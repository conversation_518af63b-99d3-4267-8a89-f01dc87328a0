﻿using Saas.Pos.Common.Tools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Saas.Pos.Common.Attributes.Filter
{
    /// <summary>
    /// 授权验证
    /// </summary>
    public class AuthorizationAttribute : Attribute
    {
        public void ActionAuthorize(object[] arguments)
        {
            string token = string.Empty;
            PropertyInfo property = null;

            foreach (var item in arguments)
            {
                var type = item.GetType();
                property = type.GetProperty("Token");
                if (property != null)
                {
                    token = property.GetValue(item, null).ToString();
                    break;
                }
            }

            if (property == null)
                throw new Exception("请传入Token以进行校验！");
            if (string.IsNullOrEmpty(token))
                throw new Exception("Token为空，无法校验！");

            var obj = JwtAuthorizationHelper.DecodeToObject(token);
            if (obj == null)
                throw new Exception("Token校验失败！");
        }
    }
}
