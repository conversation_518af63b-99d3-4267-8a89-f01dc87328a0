﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Global.Configs.SaasPos
{
    public class OrderManageConfig
    {
        public string Operator = "9999";

        /// <summary>
        /// 新人首次下单优惠卷关联ID
        /// </summary>
        public string OneMiddleId = "1";

        /// <summary>
        /// 新人二次下单优惠卷关联ID
        /// </summary>
        public string TwoMiddleId = "2";

        /// <summary>
        /// 新人三次下单优惠卷关联ID
        /// </summary>
        public string ThreeMiddleId = "3";

        /// <summary>
        /// 订单模块命名空间
        /// </summary>
        public string OrderNameSpace = "Saas.Pos.Drive.SaasPos.OrderManage.OrderManageDrive";

        public string CommissionPosterPath = "\\Configs\\Template\\Images\\commissionposter.jpg";

        /// <summary>
        /// 名堂年卡背景图片路径
        /// </summary>
        public string YingDe = "\\Configs\\Template\\Images\\yingdenk.jpg";

        /// <summary>
        /// 堂会年卡背景图片路径
        /// </summary>
        public string THAnnualPassPath = "\\Configs\\Template\\Images\\musicbox.jpg";

        /// <summary>
        /// 在线预订提成小程序地址
        /// </summary>
        public string CommissionPoster = "pages/rms/onlinebooking/index&scene=userId_";

        /// <summary>
        /// 年卡小程序地址
        /// </summary>
        public string AnnualPassPath = "pages/CombinationPurchase/PageModel01/index&scene={0}";

        /// <summary>
        /// 网络图片地址
        /// </summary>
        public string HttpPath = @"http://r.tang-hui.com.cn/api/wechat/getwxacodeunlimit.ashx?token=th_miniapp_seasonscard_token&page=";
    }
}
