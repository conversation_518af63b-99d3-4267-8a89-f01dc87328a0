﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace external.open.library.TakeaWayPlatform.TikTok.Response
{
    public class TiktokQueryProductResponse
    {
        /// <summary>
        /// 用于查询下一页
        /// </summary>
        public string next_cursor { get; set; }

        /// <summary>
        /// 是否有下一页
        /// </summary>
        public bool has_more { get; set; }

        public List<ProductOnline> products { get; set; }

        public int error_code { get; set; }

        public string description { get; set; }
    }

    public class ProductOnline
    {
        public int online_status { get; set; }

        public Product product { get; set; }

        public SkuStruct sku { get; set; }
    }

    public class Product
    {
        public string product_id { get; set; }

        public string out_id { get; set; }

        public string product_name { get; set; }

        public string category_full_name { get; set; }

        public int category_id { get; set; }
        /// <summary>
        /// 商品类型 1-团购 11-代金券 15-次卡
        /// </summary>
        public int product_type { get; set; }

        public int biz_line { get; set; }

        public string account_name { get; set; }

        public int sold_start_time { get; set; }

        public int sold_end_time { get; set; }

        public int create_time { get; set; }

        public int update_time { get; set; }

        public string out_url { get; set; }

        public List<PoiStruct> pois { get; set; }
    }

    public class PoiStruct
    {
        public string supplier_ext_id { get; set; }

        public long poi_id { get; set; }

        public long supplier_id { get; set; }

        public KeyValuePair<string, string> attr_key_value_map { get; set; }
    }

    public class SkuStruct
    {
        public string sku_id { get; set; }

        public string sku_name { get; set; }

        public int origin_amount { get; set; }

        public int actual_amount { get; set; }

        public StockStruct stock { get; set; }
    }

    public class StockStruct
    {
        public int limit_type { get; set; }

        public long stock_qty { get; set; }

        public int avail_qty { get; set; }

        public int frozen_qty { get; set; }

        public int sold_qty { get; set; }

        public int sold_count { get; set; }

        public string out_sku_id { get; set; }

        public int status { get; set; }
    }
}
