﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace external.open.library.TakeaWayPlatform.Model
{
    public class ReverseConsumeModel
    {
        /// <summary>
        /// 套餐id，注意：对应deal_id，非dealgroup_id
        /// </summary>
        public string app_deal_id { get; set; }
        /// <summary>
        /// 团购券码
        /// </summary>
        public string receipt_code { get; set; }

        public string OpenShopId { get; set; }
        /// <summary>
        /// 当前平台操作人账号
        /// </summary>
        public string ShopAccount { get; set; }
        /// <summary>
        /// 当前平台操作人
        /// </summary>
        public string ShopName { get; set; }

        /// <summary>
        /// 抖音撤销核验需要的字段(代表一张券码的标识(验券时返回))
        /// </summary>
        public string verify_id { get; set; }
        /// <summary>
        /// 抖音撤销核验需要的字段(代表券码一次核销的唯一标识(验券时返回))
        /// </summary>
        public string certificate_id { get; set; }
    }
}
