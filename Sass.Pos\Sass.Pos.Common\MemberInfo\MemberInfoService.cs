﻿using Saas.Pos.Common.MemberInfo.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.MemberInfo
{
    public class MemberInfoService
    {
        static object lockObj = new object();
        static MemberInfoService _Service;

        public static MemberInfoService Service
        {
            get
            {
                if (_Service == null)
                {
                    lock (lockObj)
                    {
                        if (_Service == null) _Service = new MemberInfoService();
                    }
                }
                return _Service;
            }
        }

        MemberBenefitModel MemberBenefit;

        /// <summary>
        /// 初始化方法
        /// </summary>
        /// <param name="accounts">会员账户配置数据</param>
        /// <param name="conpons">会员卡券配置数据</param>
        public void Init(List<AccountBenefit> accounts, List<ConponBenefit> conpons)
        {
            //初始化数据
            MemberBenefit = new MemberBenefitModel();
        }

        /// <summary>
        /// 查询符合会员条件的所有权益
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public MemberBenefitModel GetMemberBenefit(GetMemberConfigContext context)
        {
            var accounts = MemberBenefit.Account.BenefitsList.Where(w => w.Check(context)).ToList();
            var coupons = MemberBenefit.Coupons.BenefitsList.Where(w => w.Check(context)).ToList();

            BenefitsHold<AccountBenefit> accountHold = new BenefitsHold<AccountBenefit>()
            {
                BenefitsList = accounts,
                Val = "Test"
            };
            BenefitsHold<ConponBenefit> couponsHold = new BenefitsHold<ConponBenefit>()
            {
                BenefitsList = coupons,
                Val = "Test"
            };

            var result = new MemberBenefitModel()
            {
                Account = accountHold,
                Coupons = couponsHold
            };

            return result;
        }
    }
}
