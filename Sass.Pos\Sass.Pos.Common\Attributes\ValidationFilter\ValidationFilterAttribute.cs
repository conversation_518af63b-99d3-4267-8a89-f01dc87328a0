﻿using Saas.Pos.Common.Attributes.ValidationFilter.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Attributes.ValidationFilter
{
    /// <summary>
    /// 参数校验基础类
    /// </summary>
    public abstract class ValidationFilterAttribute : Attribute
    {
        public string ErrorMessage = string.Empty;

        /// <summary>
        /// 校验当前参数合法性方法
        /// </summary>
        /// <param name="currentValue">当前参数值</param>
        /// <returns></returns>
        public abstract void Inspect(object currentValue, ValidationContext context);
    }
}
