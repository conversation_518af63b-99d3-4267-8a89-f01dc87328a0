﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Attributes.ValidationFilter.Context
{
    public class ValidationContext
    {
        /// <summary>
        /// 当前属性名称
        /// </summary>
        public string PropertyName { get; set; }

        /// <summary>
        /// 需要校验的整个类类型
        /// </summary>
        public Type ObjectType { get; set; }

        /// <summary>
        /// 需要校验的整个类
        /// </summary>
        public object ObjectInstance { get; set; }
    }
}
