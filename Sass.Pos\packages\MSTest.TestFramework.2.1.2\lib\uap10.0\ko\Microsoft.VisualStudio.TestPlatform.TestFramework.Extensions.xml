<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute">
            <summary>
            테스트 배포별 배포 항목(파일 또는 디렉터리)을 지정하는 데 사용됩니다.
            테스트 클래스 또는 테스트 메서드에서 지정할 수 있습니다.
            둘 이상의 항목을 지정하기 위한 여러 특성 인스턴스를 가질 수 있습니다.
            항목 경로는 절대 또는 상대 경로일 수 있으며, 상대 경로인 경우 RunConfig.RelativePathRoot가 기준입니다.
            </summary>
            <example>
            [DeploymentItem("file1.xml")]
            [DeploymentItem("file2.xml", "DataFiles")]
            [DeploymentItem("bin\Debug")]
            </example>
            <remarks>
            Putting this in here so that UWP discovery works. We still do not want users to be using DeploymentItem in the UWP world - Hence making it internal.
            We should separate out DeploymentItem logic in the adapter via a Framework extensiblity point.
            Filed https://github.com/Microsoft/testfx/issues/100 to track this.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="path">배포할 파일 또는 디렉터리. 경로는 빌드 출력 디렉터리에 대해 상대적입니다. 배포된 테스트 어셈블리와 동일한 디렉터리에 항목이 복사됩니다.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String,System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="path">배포할 파일 또는 디렉터리에 대한 상대 또는 절대 경로. 경로는 빌드 출력 디렉터리에 대해 상대적입니다. 배포된 테스트 어셈블리와 동일한 디렉터리에 항목이 복사됩니다.</param>
            <param name="outputDirectory">항목을 복사할 디렉터리의 경로. 배포 디렉터리에 대한 절대 경로 또는 상대 경로일 수 있습니다.<paramref name="path"/>에 의해 식별되는 모든 파일 및 디렉터리는 이 디렉터리에 복사됩니다.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.Path">
            <summary>
            복사할 소스 파일 또는 폴더의 경로를 가져옵니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.OutputDirectory">
            <summary>
            항목을 복사할 디렉터리의 경로를 가져옵니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AppContainer.UITestMethodAttribute">
            <summary>
            Windows 스토어 앱에 대한 UI 스레드에서 테스트 코드를 실행합니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AppContainer.UITestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            UI 스레드에서 테스트 메서드를 실행합니다.
            </summary>
            <param name="testMethod">
            테스트 메서드입니다.
            </param>
            <returns>
            배열 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>인스턴스.
         </returns>
            Throws <exception cref="T:System.NotSupportedException"> when run on an async test method.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext">
            <summary>
            TestContext 클래스. 이 클래스는 완전히 추상 클래스여야 하며 멤버를 포함할
            수 없습니다. 어댑터는 멤버를 구현합니다. 프레임워크의 사용자는
            잘 정의된 인터페이스를 통해서만 여기에 액세스할 수 있습니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.Properties">
            <summary>
            테스트에 대한 테스트 속성을 가져옵니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.FullyQualifiedTestClassName">
            <summary>
            현재 실행 중인 테스트 메서드를 포함하는 클래스의 정규화된 이름을 가져옵니다
            </summary>
            <remarks>
            This property can be useful in attributes derived from ExpectedExceptionBaseAttribute.
            Those attributes have access to the test context, and provide messages that are included
            in the test results. Users can benefit from messages that include the fully-qualified
            class name in addition to the name of the test method currently being executed.
            </remarks>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestName">
            <summary>
            현재 실행 중인 테스트 메서드의 이름을 가져옵니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.CurrentTestOutcome">
            <summary>
            현재 테스트 결과를 가져옵니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String)">
            <summary>
            Used to write trace messages while the test is running
            </summary>
            <param name="message">formatted message string</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String,System.Object[])">
            <summary>
            Used to write trace messages while the test is running
            </summary>
            <param name="format">format string</param>
            <param name="args">the arguments</param>
        </member>
    </members>
</doc>
