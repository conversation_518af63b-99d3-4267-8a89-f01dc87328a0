﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IRepository.SaasPos
{
    public partial interface IActivity_AttendRecordRepository : IRepositoryBase<Activity_AttendRecord>
    {
        List<GetActivityDataModelEx> GetActivityData(GetActivityDataContext context);
    }
}
