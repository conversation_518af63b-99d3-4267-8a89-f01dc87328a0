﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>

	<configSections>
		<!--spring.net-->
		<sectionGroup name="spring">
			<section name="context" type="Spring.Context.Support.ContextHandler,Spring.Core" />
			<section name="objects" type="Spring.Context.Support.DefaultSectionHandler,Spring.Core" />
		</sectionGroup>
	</configSections>
	<connectionStrings configSource="Configs\database.config" />
	<appSettings configSource="Configs\system.config"></appSettings>
	<system.web>
		<compilation debug="true" />
	</system.web>
	<system.serviceModel>
		<services>
			<service name="Saas.Pos.Service.SaasPos.SaasPosService" behaviorConfiguration="SaasPosServiceBehaviors">
				<endpoint address="" binding="basicHttpBinding" contract="Saas.Pos.Domain.IService.ISaasPos">
					<identity>
						<dns value="localhost" />
					</identity>
				</endpoint>
				<endpoint address="mex" binding="mexHttpBinding" contract="IMetadataExchange" />
				<host>
					<baseAddresses>
						<add baseAddress="http://localhost:9199/SaasPos" />
					</baseAddresses>
				</host>
			</service>
			<service name="Saas.Pos.Service.Pos.PosService" behaviorConfiguration="SaasPosServiceBehaviors">
				<endpoint address="" binding="basicHttpBinding" contract="Saas.Pos.Domain.IService.Pos.IPos">
					<identity>
						<dns value="localhost" />
					</identity>
				</endpoint>
				<endpoint address="mex" binding="mexHttpBinding" contract="IMetadataExchange" />
				<host>
					<baseAddresses>
						<add baseAddress="http://localhost:9108/PosService" />
					</baseAddresses>
				</host>
			</service>
			<service name="Saas.Pos.Service.VipService" behaviorConfiguration="SaasPosServiceBehaviors">
				<endpoint address="" binding="basicHttpBinding" contract="Saas.Pos.Domain.IService.IVip">
					<identity>
						<dns value="localhost" />
					</identity>
				</endpoint>
				<endpoint address="mex" binding="mexHttpBinding" contract="IMetadataExchange" />
				<host>
					<baseAddresses>
						<add baseAddress="http://localhost:9199/Vip" />
					</baseAddresses>
				</host>
			</service>
			<service name="Saas.Pos.Service.Rms.RmsService" behaviorConfiguration="SaasPosServiceBehaviors">
				<endpoint address="" binding="basicHttpBinding" contract="Saas.Pos.Domain.IService.IRmsService">
					<identity>
						<dns value="localhost" />
					</identity>
				</endpoint>
				<endpoint address="mex" binding="mexHttpBinding" contract="IMetadataExchange" />
				<host>
					<baseAddresses>
						<add baseAddress="http://localhost:9199/Rms" />
					</baseAddresses>
				</host>
			</service>
		</services>

		<client>
			<endpoint address="http://www.tang-hui.com.cn:9107/TradeService/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ITradeService" contract="TradService.ITradeService" name="BasicHttpBinding_ITradeService" />
		</client>
		<bindings>
			<basicHttpBinding>
				<binding name="BasicHttpBinding_ITradeService"></binding>
			</basicHttpBinding>
		</bindings>
		<behaviors>
			<serviceBehaviors>
				<behavior name="SaasPosServiceBehaviors">
					<!-- 为避免泄漏元数据信息，
          请在部署前将以下值设置为 false -->
					<serviceMetadata httpGetEnabled="True" />
					<!-- 要接收故障异常详细信息以进行调试，
          请将以下值设置为 true。在部署前设置为 false 
          以避免泄漏异常信息 -->
					<serviceDebug includeExceptionDetailInFaults="False" />
					<serviceThrottling maxConcurrentCalls="100" maxConcurrentInstances="100"></serviceThrottling>
				</behavior>
			</serviceBehaviors>
		</behaviors>
	</system.serviceModel>

	<!-- 部署服务库项目时，必须将配置文件的内容添加到
 主机的 app.config 文件中。System.Configuration 不支持库的配置文件。 -->

	<!--<system.serviceModel>
		<bindings>
			<basicHttpBinding>
				<binding name="BasicHttpBinding_ISaasPos" />
			</basicHttpBinding>
		</bindings>
		<client>
			<endpoint address="http://localhost:9199/SaasPos/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ISaasPos" contract="SaasPosService.ISaasPos" name="BasicHttpBinding_ISaasPos"></endpoint>
		</client>
	</system.serviceModel>-->

	<runtime>

		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">

			<dependentAssembly>

				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />

				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />

			</dependentAssembly>

			<dependentAssembly>

				<assemblyIdentity name="Common.Logging" publicKeyToken="af08829b84f0328e" culture="neutral" />

				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />

			</dependentAssembly>

			<dependentAssembly>
				<assemblyIdentity name="Common.Logging.Core" publicKeyToken="af08829b84f0328e" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>

			<dependentAssembly>

				<assemblyIdentity name="ICSharpCode.SharpZipLib" publicKeyToken="1b03e6acf1164f73" culture="neutral" />

				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />

			</dependentAssembly>

		</assemblyBinding>

	</runtime>
	<!--spring.net-->
	<spring>
		<context>
			<resource uri="file://~/Configs/Objects/account.config" />

		</context>
	</spring>

	<startup>
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5" />
	</startup>


</configuration>
