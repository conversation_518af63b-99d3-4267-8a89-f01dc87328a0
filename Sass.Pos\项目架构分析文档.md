# Sass.Pos 项目架构分析文档

## 项目概述

Sass.Pos 是一个基于 .NET Framework 4.5 的企业级 SaaS POS（销售点）系统，采用分层架构设计，主要服务于餐饮、娱乐等行业的销售管理、会员管理、订单处理等业务场景。

## 技术栈

### 核心技术
- **.NET Framework 4.5**
- **WCF (Windows Communication Foundation)** - 服务通信框架
- **Entity Framework** - ORM 数据访问框架
- **Spring.NET** - 依赖注入容器
- **Log4Net** - 日志记录框架
- **Castle.Core** - AOP 代理框架

### 数据库
- **SQL Server** - 主数据库
- 支持多数据库实例：
  - SaasPos - 主业务数据库
  - MIMS - 会员信息管理系统
  - RMS - 房间管理系统
  - DbFood - 餐饮管理系统

### 第三方集成
- **微信支付** - 支付集成
- **美团、抖音** - 外卖平台集成
- **企业微信** - 内部协作

## 项目结构

### 解决方案组织
```
Sass.Pos/
├── 01 基础结构层/
│   ├── Sass.Pos.Domain      # 领域层
│   └── Sass.Pos.Common      # 公共组件
├── 02 应用服务/
│   ├── 1 驱动服务/
│   │   ├── Sass.Pos.Service # WCF 服务层
│   │   └── Sass.Pos.Drive   # 业务驱动层
│   └── 2 数据访问/
│       ├── Sass.Pos.Application    # 应用层
│       ├── Sass.Pos.Repository     # 仓储层
│       └── Sass.Pos.RepositoryFactory # 仓储工厂
├── 03 外部服务库/
│   └── external.open.library # 外部平台集成
├── Sass.Pos.Model          # 数据模型层
└── Sass.Pos.UnitTest       # 单元测试
```

## 分层架构详解

### 1. 领域层 (Domain)
**位置**: `Sass.Pos.Domain`
**职责**: 定义业务接口和领域契约

#### 主要组件
- **服务接口定义**
  - `ISaasPos` - 主业务服务接口
  - `IPos` - POS 核心服务接口
  - `IRmsService` - 房间管理服务接口
  - `IVip` - 会员服务接口

#### 核心业务接口
```csharp
// 主要业务模块接口
- IMemBerManageService     # 会员管理
- IOrderManageService      # 订单管理
- IShopGoodService         # 商品管理
- ICouponManageService     # 优惠券管理
- IStoreManageService      # 门店管理
- IWeChatService           # 微信服务
- IOpenPlatformService     # 开放平台服务
```

### 2. 服务层 (Service)
**位置**: `Sass.Pos.Service`
**职责**: WCF 服务实现，对外提供 API 接口

#### 服务端点配置
- **SaasPos 服务**: `http://localhost:9199/SaasPos`
- **POS 服务**: `http://localhost:9108/PosService`
- **VIP 服务**: `http://localhost:9199/Vip`
- **RMS 服务**: `http://localhost:9199/Rms`

#### 主要服务模块
```
SaasPos/
├── OrderManageService.cs      # 订单管理服务
├── MemBerManageService.cs     # 会员管理服务
├── ShopGood/                  # 商品管理服务
├── CouponManageService.cs     # 优惠券管理服务
├── StoreManageService.cs      # 门店管理服务
└── WeChatService.cs           # 微信服务

Pos/
├── DbOrderService.cs          # 订单数据服务
├── DbFoodService.cs           # 餐饮数据服务
├── MemberInfoService.cs       # 会员信息服务
└── ConsumeBillService.cs      # 消费账单服务
```

### 3. 业务驱动层 (Drive)
**位置**: `Sass.Pos.Drive`
**职责**: 业务逻辑实现和流程控制

#### 核心特性
- **单例模式**: `AppSingle` 全局应用单例
- **多数据库会话管理**: `AppSession`
- **中间件代理**: `MiddlewareProxy`
- **缓存代理**: `StorageProxy`
- **任务管理**: `TaskManage`

#### 业务模块
```
SaasPos/
├── OrderManage/               # 订单管理驱动
├── ShopGood/                  # 商品管理驱动
├── MemBerManage/              # 会员管理驱动
└── Report/                    # 报表驱动

DbFood/
├── OrderMenuManage/           # 点餐管理驱动
└── MemberManage/              # 会员管理驱动
```

### 4. 应用层 (Application)
**位置**: `Sass.Pos.Application`
**职责**: 应用服务实现，连接业务逻辑和数据访问

#### T4 模板生成
- 使用 T4 模板自动生成应用层代码
- 每个实体对应一个 App 类
- 继承自 `AppBase<T>` 基类

#### 数据库分组
```
SaasPos/     # 主业务应用
MIMS/        # 会员信息管理应用
Rms/         # 房间管理应用
DbFood/      # 餐饮管理应用
GrouponBase/ # 团购基础应用
SongBase/    # 歌曲基础应用
```

### 5. 仓储层 (Repository)
**位置**: `Sass.Pos.Repository`
**职责**: 数据访问实现

#### 仓储模式
- 每个数据库对应独立的仓储实现
- 继承自 `RepositoryBase<T>` 基类
- 使用 Entity Framework 进行数据访问

#### 数据库上下文
```csharp
// 主要数据库上下文
- Saas_PosEntities      # 主业务数据库
- MIMSEntities          # 会员管理数据库
- rms2019Entities       # 房间管理数据库
- DbFoodEntities        # 餐饮管理数据库
```

### 6. 数据模型层 (Model)
**位置**: `Sass.Pos.Model`
**职责**: 数据实体定义和上下文模型

#### 核心实体模型
```
SaasPos/
├── Shop                    # 门店信息
├── Shop_Goods             # 商品信息
├── Shop_GoodsSku          # 商品SKU
├── User_Order             # 用户订单
├── User_Coupon            # 用户优惠券
└── Payment_Data           # 支付数据

MIMS/
├── MemberInfo             # 会员信息
├── MemberCardInfo         # 会员卡信息
└── Activity_AttendRecord  # 活动参与记录

DbFood/
├── Food                   # 餐饮商品
├── FdCash                 # 收银记录
├── Room                   # 房间信息
└── Member                 # 会员信息
```

## 核心业务模块

### 1. 会员管理系统
**功能范围**:
- 会员注册、信息管理
- 会员卡管理
- 积分管理
- 充值、消费记录
- 会员等级管理

**主要组件**:
- `MemBerManageService` - 会员管理服务
- `MemberInfoRepository` - 会员信息仓储
- `MemberInfo` - 会员实体模型

### 2. 订单管理系统
**功能范围**:
- 订单创建、支付
- 订单状态管理
- 退款处理
- 订单查询统计

**主要组件**:
- `OrderManageService` - 订单管理服务
- `OrderManageDrive` - 订单业务驱动
- `User_Order` - 订单实体模型

### 3. 商品管理系统
**功能范围**:
- 商品信息管理
- SKU 管理
- 价格管理
- 库存管理
- 商品分类

**主要组件**:
- `ShopGoodService` - 商品管理服务
- `ShopGoodsDrive` - 商品业务驱动
- `Shop_Goods` - 商品实体模型

### 4. POS 收银系统
**功能范围**:
- 收银结算
- 房间管理
- 消费记录
- 支付处理

**主要组件**:
- `PosService` - POS 服务
- `DbOrderService` - 订单数据服务
- `ConsumeBillService` - 消费账单服务

### 5. 报表系统
**功能范围**:
- 销售报表
- 会员消费统计
- 财务报表
- 运营分析

**主要组件**:
- `SaasPosReportService` - 报表服务
- `ReportServiceDrive` - 报表业务驱动

## 设计模式应用

### 1. 分层架构模式
- **表现层**: WCF 服务
- **业务层**: Drive 业务驱动
- **应用层**: Application 应用服务
- **数据层**: Repository 仓储

### 2. 仓储模式 (Repository Pattern)
- 抽象数据访问逻辑
- 支持多数据库切换
- 统一数据访问接口

### 3. 工厂模式 (Factory Pattern)
- `RepositoryFactory` - 仓储工厂
- `ObjectsFactory` - 对象工厂
- `DbContextFactory` - 数据库上下文工厂

### 4. 单例模式 (Singleton Pattern)
- `AppSingle` - 全局应用单例
- 管理系统级别的共享资源

### 5. 代理模式 (Proxy Pattern)
- `MiddlewareProxy` - 中间件代理
- `StorageProxy` - 缓存代理
- 使用 Castle.Core 实现 AOP

## 配置管理

### 数据库配置
- `database.config` - 数据库连接字符串
- 支持多数据库实例配置

### 系统配置
- `system.config` - 系统参数配置
- `log4net.config` - 日志配置
- `account.config` - 账户配置

### 业务配置
- `BookConfig.json` - 预订配置
- `storeUrlConfig.json` - 门店URL配置
- `numberPoolConfig.json` - 号码池配置

## 外部集成

### 支付集成
- **微信支付** - 移动支付
- **支付宝** - 移动支付

### 平台集成
- **美团外卖** - 外卖订单同步
- **抖音外卖** - 外卖订单同步
- **本地生活平台** - 多平台订单管理

### 通信集成
- **企业微信** - 内部协作
- **短信平台** - 消息通知

## 部署架构

### 服务部署
- **WCF 服务** - Windows 服务或 IIS 托管
- **多端口服务** - 不同业务模块独立端口

### 数据库部署
- **SQL Server** - 支持集群部署
- **多数据库实例** - 业务隔离

### 缓存策略
- **内存缓存** - 系统级缓存
- **分布式缓存** - 支持 Redis

## 开发指南

### 新增业务模块步骤
1. **定义领域接口** - 在 Domain 层定义服务接口
2. **实现服务层** - 在 Service 层实现 WCF 服务
3. **编写业务逻辑** - 在 Drive 层实现业务驱动
4. **创建应用服务** - 在 Application 层创建应用服务
5. **实现数据访问** - 在 Repository 层实现仓储
6. **定义数据模型** - 在 Model 层定义实体模型

### 代码生成
- 使用 **T4 模板** 自动生成重复代码
- 支持实体类、仓储类、应用类的自动生成

### 测试策略
- **单元测试** - Sass.Pos.UnitTest 项目
- **集成测试** - WCF 服务测试
- **性能测试** - 业务场景压力测试

## 性能优化

### 数据访问优化
- **连接池管理** - Entity Framework 连接池
- **查询优化** - SQL 查询性能优化
- **批量操作** - 减少数据库往返

### 缓存策略
- **应用级缓存** - 热点数据缓存
- **查询结果缓存** - 减少数据库查询
- **分布式缓存** - 多实例数据共享

### 并发处理
- **线程池管理** - 合理配置线程池
- **异步处理** - 长时间操作异步化
- **锁机制** - 避免并发冲突

## 安全考虑

### 数据安全
- **SQL 注入防护** - 参数化查询
- **数据加密** - 敏感数据加密存储
- **访问控制** - 基于角色的权限控制

### 通信安全
- **HTTPS** - 加密通信
- **身份验证** - 用户身份验证
- **授权机制** - 接口访问授权

## 监控和运维

### 日志管理
- **Log4Net** - 结构化日志记录
- **日志级别** - 分级日志管理
- **日志分析** - 问题诊断和性能分析

### 性能监控
- **系统监控** - CPU、内存、磁盘监控
- **应用监控** - 接口响应时间监控
- **数据库监控** - 数据库性能监控

### 故障处理
- **异常处理** - 统一异常处理机制
- **故障恢复** - 自动故障恢复
- **备份策略** - 数据备份和恢复

## 数据流图

### 订单处理流程
```mermaid
graph TD
    A[客户下单] --> B[订单验证]
    B --> C[库存检查]
    C --> D[价格计算]
    D --> E[创建订单]
    E --> F[支付处理]
    F --> G[订单确认]
    G --> H[库存扣减]
    H --> I[订单完成]
```

### 会员管理流程
```mermaid
graph TD
    A[会员注册] --> B[信息验证]
    B --> C[创建会员档案]
    C --> D[发放会员卡]
    D --> E[积分初始化]
    E --> F[会员激活]
    F --> G[消费记录]
    G --> H[积分累计]
    H --> I[等级升级]
```

## 系统架构图

```mermaid
graph TB
    subgraph "表现层"
        A[WCF 服务接口]
        B[RESTful API]
    end

    subgraph "业务层"
        C[订单管理]
        D[会员管理]
        E[商品管理]
        F[支付管理]
    end

    subgraph "应用层"
        G[应用服务]
        H[业务驱动]
    end

    subgraph "数据层"
        I[仓储层]
        J[数据访问]
    end

    subgraph "数据库"
        K[SaasPos DB]
        L[MIMS DB]
        M[RMS DB]
        N[DbFood DB]
    end

    A --> C
    A --> D
    A --> E
    A --> F

    C --> G
    D --> G
    E --> G
    F --> G

    G --> H
    H --> I
    I --> J

    J --> K
    J --> L
    J --> M
    J --> N
```

## 快速上手指南

### 环境准备
1. **开发环境**
   - Visual Studio 2019 或更高版本
   - .NET Framework 4.5
   - SQL Server 2012 或更高版本

2. **数据库配置**
   - 创建数据库实例
   - 配置连接字符串
   - 运行数据库脚本

3. **项目配置**
   - 克隆项目代码
   - 还原 NuGet 包
   - 配置数据库连接

### 运行项目
1. **启动服务**
   ```bash
   # 设置 Sass.Pos.Service 为启动项目
   # 按 F5 运行项目
   ```

2. **测试服务**
   - 访问 WCF 测试客户端
   - 测试各个服务接口
   - 验证数据库连接

### 常见问题
1. **数据库连接失败**
   - 检查连接字符串配置
   - 确认数据库服务状态
   - 验证用户权限

2. **服务启动失败**
   - 检查端口占用情况
   - 确认防火墙设置
   - 查看错误日志

## 总结

Sass.Pos 项目采用了成熟的企业级架构设计，具有以下特点：

1. **分层清晰** - 严格的分层架构，职责分离明确
2. **模块化设计** - 业务模块独立，便于维护和扩展
3. **多数据库支持** - 支持多个业务数据库，数据隔离
4. **丰富的集成** - 支持多种第三方平台集成
5. **可扩展性强** - 基于接口编程，便于功能扩展
6. **性能优化** - 多层次的缓存和优化策略

该架构适合中大型企业的 POS 系统需求，能够支撑复杂的业务场景和高并发访问。

---

**文档版本**: 1.0
**创建日期**: 2025-06-20
**最后更新**: 2025-06-20
**维护人员**: 系统架构师
