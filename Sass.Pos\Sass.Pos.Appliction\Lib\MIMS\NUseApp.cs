﻿using Saas.Pos.Model.MIMS;
using Saas.Pos.Model.MIMS.Context;
using Saas.Pos.Model.MIMS.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.MIMS
{
    public partial class NUseApp : AppBase<NUse>
    {
        public List<GetNUseInfoModel> GetUseList(GetNUseAndCouponInfoContext context)
        {
            return Repository.NUse.GetUseList(context);
        }

        public List<GetUseCouponInfoModel> GetUseCouponList(GetNUseAndCouponInfoContext context)
        {
            return Repository.NUse.GetUseCouponList(context);
        }
    }
}
