﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.DbFood
{
    public partial interface IEmpGift_RecordRepository : IRepositoryBase<EmpGift_Record>
    {
        List<GetEmpGiftRecordModel> GetGiftRecordList(GetEmpGiftRecordContext context);

        GetEmpGiftRecordInfoModel GetGiftRecordInfo(GetEmpGiftRecordInfoContext context);

        List<GetMonthEmpGiftRecordModel> GetGiftSummary(GetMonthGiftSummaryContext context);
    }
}
