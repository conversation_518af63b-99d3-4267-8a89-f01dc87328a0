﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Rms.NumberPool
{
    /// <summary>
    /// 顺序生成号码
    /// </summary>
    public class NumberPoolOrderServiceCopy : NumberPoolServiceBase
    {
        int Index = 0;
        SystemConfigModel config;

        public NumberPoolOrderServiceCopy(DateTime bookDate, string number, SystemConfigModel _config) : base(bookDate)
        {
            config = _config;
            if (!string.IsNullOrEmpty(_config.Initial) && !string.IsNullOrEmpty(number))
            {
                int index = number.IndexOf(config.Initial);
                number = number.Remove(index, 1);
                Index = int.Parse(number);
            }
        }

        public override string GenerateNumber()
        {
            Index++;
            string returnStr = string.Empty;
            //获取所有不允许出现的数字
            var existNumbers = new List<int>();
            if (!string.IsNullOrEmpty(config.existNumbers))
                existNumbers = config.existNumbers.Split(',').Select(x => Convert.ToInt32(x)).ToList();

            //如果不符合条件就重新排序Index
            if (!Inspect(existNumbers, Index))
                ProcessNumber(existNumbers);

            returnStr = Index.ToString().PadLeft(config.Length, '0');
            if (!string.IsNullOrEmpty(config.Initial))
                returnStr = config.Initial + returnStr;

            return returnStr;
        }

        protected override bool Inspect(List<int> existNumbers, int number)
        {
            if (existNumbers == null || existNumbers.Count <= 0)
                return true;

            foreach (var item in existNumbers)
            {
                if (number.ToString().Contains(item.ToString()))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 处理编号，保证编号符合要求（进入此方法说明一定存在了不该存在的数据）
        /// </summary>
        private void ProcessNumber(List<int> existNumbers)
        {
            var newNumberStr = string.Empty;
            var numberStr = Index.ToString();

            for (int i = 0; i < numberStr.Length; i++)
            {
                int charInt = int.Parse(numberStr[i].ToString());
                if (existNumbers.Any(w => w == charInt))
                {
                    //检测是否还存在不允许出现的数字
                    var next = true;
                    while (next)
                    {
                        charInt++;
                        next = existNumbers.Where(w => w == charInt).Count() > 0;
                    }
                }

                newNumberStr += charInt.ToString();
            }

            Index = int.Parse(newNumberStr);
        }
    }
}
