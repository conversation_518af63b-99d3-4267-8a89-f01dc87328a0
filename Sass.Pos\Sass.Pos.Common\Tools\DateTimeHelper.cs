﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Tools
{
    public static class DateTimeHelper
    {


        public static string GetTimeWeek(this DateTime date)
        {
            return ((int)date.DayOfWeek).ToString();
        }

        public static string GetWeek(DateTime date)
        {
            var currentDate = date;
            var week = (int)currentDate.DayOfWeek;

            return GlobalConfig.Global.ShopBookConfig.WeekDic[week];
        }
    }
}
