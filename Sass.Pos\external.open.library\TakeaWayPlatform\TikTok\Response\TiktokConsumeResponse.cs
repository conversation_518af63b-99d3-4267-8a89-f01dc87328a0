﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace external.open.library.TakeaWayPlatform.Tiktok.Response
{
    public class TiktokConsumeResponse
    {
        public List<VerifyResult> verify_results { get; set; }
    }

    public class VerifyResult
    {
        public string product_id { get; set; }
        /// <summary>
        /// 代表验券传入的code或encrypted_code
        /// </summary>
        public string code { get; set; }
        /// <summary>
        /// 验券结果说明
        /// </summary>
        public string msg { get; set; }
        /// <summary>
        /// 验券结果码, 0表示成功, 非0表示失败
        /// </summary>
        public int result { get; set; }
        /// <summary>
        /// 企业号商家总店ID
        /// </summary>
        public string account_id { get; set; }
        /// <summary>
        /// 代表一张券码的标识(撤销时需要)
        /// </summary>
        public string certificate_id { get; set; }
        /// <summary>
        /// 券码
        /// </summary>
        public string certificate_no { get; set; }
        /// <summary>
        /// 身份证
        /// </summary>
        public string id_card { get; set; }
        /// <summary>
        /// 订单ID
        /// </summary>
        public string order_id { get; set; }
        /// <summary>
        /// 抖音券的12位原始券码
        /// </summary>
        public string origin_code { get; set; }
        /// <summary>
        /// 二维码
        /// </summary>
        public string qrcode { get; set; }
        /// <summary>
        /// 核销返回的金额信息
        /// </summary>
        public object verify_amount_info { get; set; }
        /// <summary>
        /// 代表券码一次核销的标识(撤销时需要)
        /// </summary>
        public string verify_id { get; set; }
    }
}
