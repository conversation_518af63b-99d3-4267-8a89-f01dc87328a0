﻿using ComponentCore.Threading.Impl;
using Newtonsoft.Json;
using Saas.Pos.Application.Lib.Bar;
using Saas.Pos.Appliction.Lib.Bar;
using Saas.Pos.Common.Log;
using Saas.Pos.Common.Tools;
using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Saas.Pos.Drive.Bar.WineStockManage.SmsPushMode
{
    public class SmsPushFristRenew : SmsPushModeBase
    {
        static object obj = new object();
        public override int SendMessage(GetWineStockData context)
        {
            var result = 0;
            WineStockApp w = new WineStockApp();
            DateTime nowDate = DateTime.Now;
            var rep = new PhoneSendMessageContext();
            rep.ReqType = 1;
            int sendState = 0;
            var editRecord = new List<EditSendReardContext>();
            foreach (var item in context.UserData)
            {
                //酒水名称和数量合并
                var drinkData = item.DrinksData.Select(i => $"{i.DrinksQty}{i.Unit}{i.DrinksName}").ToList();
                rep.Title = "1";
                rep.PhoneNumber = item.CustTel;
                var datePoor = item.DeDatetime.AddDays(60) - nowDate;
                var ShopUrl = "bar_expired_th";
                rep.No = 0;
                if (item.DeShopId == 11)
                {
                    ShopUrl = "bar_expired_kboss";
                    rep.No = 1;
                }
                var sceneEx = string.Format(@"t-{0}@key-{1}@stime-{2}@etime-{3}", ShopUrl, item.iKeyMsg.Replace("-", ""),DateTime.Now.ToString("yyyy/MM/dd"), item.DeDatetime.AddDays(60).ToString("yyyy/MM/dd"));
                var shortUrl = generate_urllink("pages/card/selfcollect/index", "scene=" + sceneEx);
                if (datePoor.Days <= 30 && datePoor.Days > 15)
                {
                    rep.Message = string.Format(@"亲爱的{0}：您好！您的{1}酒水将于{2}到期，为避免浪费，期待您抽出宝贵时间到店欢唱！祝愿您生活愉快！"
                        , item.CustName, string.Join(",", drinkData), item.DeDatetime.AddDays(60).ToString("yyyy/MM/dd"));
                    sendState = 1;
                }
                else if (datePoor.Days <= 15 && datePoor.Days < 30)
                {
                    sendState = 2;
                    rep.Message = string.Format(@"亲爱的{0}：您好！您的{1}酒水将于{2}到期，为避免浪费，我们特为您赠送价值1098元的中小房欢唱体验券一张！请点击链接领取：{3} 限时优惠，逾期失效！期待您抽出宝贵时间到店欢唱！祝愿您生活愉快！"
                        , item.CustName, string.Join(",", drinkData), item.DeDatetime.AddDays(60).ToString("yyyy/MM/dd"), shortUrl);
                }
                var repData = MessageSendHelper.PhoneSendMessage(rep);
                if (repData)
                {
                    editRecord.Add(new EditSendReardContext() { iKeyMsg = item.iKeyMsg, SendState = sendState });//添加发送信息记录
                    result++;
                }
                else
                    LogHelper.TaskInfo("SendMessage", "发送短信失败\n参数:" + JsonConvert.SerializeObject(item) + "\n原因:短信未发送成功!\n发送信息记录:" + JsonConvert.SerializeObject(editRecord), 2);
            }

            w.EditWineStockData(editRecord);//记录发送信息

            return result;
        }

        public string generate_urllink(string path, string param)
        {
            StringBuilder sb = new StringBuilder("http://r.tang-hui.com.cn/api/miniapp/generate_urllink.ashx");
            sb.Append("?");
            sb.Append("path=");
            sb.Append(path);
            if (!string.IsNullOrWhiteSpace(param))
            {
                sb.Append("&query=");
                sb.Append(param);

            }
            string webJson = Post(sb.ToString(), string.Empty);
            var jobject = JsonConvert.DeserializeObject<UrlModel>(webJson);

            return jobject.Data.url_link;
        }

        public string Post(string sendurl, string param)
        {
            WebClient client = new WebClient();
            client.Credentials = CredentialCache.DefaultCredentials;
            client.Headers[HttpRequestHeader.ContentType] = "application/json";
            client.Encoding = Encoding.UTF8;
            return client.UploadString(sendurl, param);
        }
    }
}
