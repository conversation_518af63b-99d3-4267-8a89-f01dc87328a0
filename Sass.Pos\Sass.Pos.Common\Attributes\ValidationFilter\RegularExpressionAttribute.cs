﻿using Saas.Pos.Common.Attributes.ValidationFilter.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Saas.Pos.Common.Attributes.ValidationFilter
{
    /// <summary>
    /// 通过正则表达式校验当前字符串
    /// </summary>
    public class RegularExpressionAttribute : ValidationFilterAttribute
    {
        public string Pattern { get; private set; }
        private Regex Regex { get; set; }

        public RegularExpressionAttribute(string pattern)
        {
            Pattern = pattern;
        }

        public override void Inspect(object currentValue, ValidationContext context)
        {
            SetupRegex();
            if (currentValue != null)
            {
                string text = currentValue.ToString();
                if (!string.IsNullOrEmpty(text))
                {
                    Match match = Regex.Match(text);
                    if (match.Length != text.Length)
                        throw new Exception(string.IsNullOrEmpty(ErrorMessage) ? string.Format(AppInterface.dat.lang["RegularExpressionMsg2"], context.PropertyName) : ErrorMessage);
                }
            }
        }

        private void SetupRegex()
        {
            if (string.IsNullOrEmpty(Pattern))
                throw new Exception(AppInterface.dat.lang["RegularExpressionMsg1"]);

            Regex = new Regex(Pattern);
        }
    }
}
