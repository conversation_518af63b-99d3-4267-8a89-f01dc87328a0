﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace external.open.library.TakeaWayPlatform.Tiktok.Response
{
    public class TiktokQueryCouponResponse
    {
        public int error_code { get; set; }
        public string description { get; set; }
        public Certificate certificate { get; set; }
    }

    public class Certificate
    {
        /// <summary>
        /// 加密券码
        /// </summary>
        public string encrypted_code { get; set; }
        /// <summary>
        /// 券的状态，0表示初始状态，1 表示未使用，2表示已使用，3表示退款申请中（待审核），4表示退款成功，5表示退款失败，6表示退款中
        /// 备注：如果是次卡券，在次卡次数没有全部履约完成前，券状态为未使用；
        /// </summary>
        public int status { get; set; }
        /// <summary>
        /// 金额信息
        /// </summary>
        public Amount amount { get; set; }
        /// <summary>
        /// 团购信息
        /// </summary>
        public Sku sku { get; set; }
        /// <summary>
        /// 券的有效期结束时间，单位毫秒，时间戳
        /// </summary>
        public long expire_time { get; set; }
        /// <summary>
        /// 券的有效期开始时间，单位毫秒，时间戳
        /// </summary>
        public long start_time { get; set; }
        /// <summary>
        /// 券码(未核销时不返回、已核销可撤销时不返回)
        /// </summary>
        public string code { get; set; }

        public Verify verify { get; set; }
    }

    public class Verify
    {
        /// <summary>
        /// 核销记录ID
        /// </summary>
        public string verify_id { get; set; }
        /// <summary>
        /// 券ID
        /// </summary>
        public string certificate_id { get; set; }
        /// <summary>
        /// 核销类型，0默认值，1用户自验，2商家扫二维码，3商家手动输入，4开放平台API，5抖音来客APP履约，7自动履约
        /// </summary>
        public int verify_type { get; set; }
        /// <summary>
        /// 核销时间， 单位秒，时间戳
        /// </summary>
        public long verify_time { get; set; }
        /// <summary>
        /// 是否可撤销
        /// </summary>
        public bool can_cancel { get; set; }
        /// <summary>
        /// 核销人抖音号（当核销类型verify_type=4时，默认为"开放平台API")
        /// </summary>
        public string verifier_unique_id { get; set; }
        /// <summary>
        /// 次卡信息
        /// </summary>
        public TimeCard time_card { get; set; }

        public List<VerifyRecords> verify_records { get; set; }
    }

    public class VerifyRecords
    {
        public string verify_id { get; set; }

        public string certificate_id { get; set; }

        public long verify_time { get; set; }

        public bool can_cancel { get; set; }

        public int verify_type { get; set; }

        public string verifier_unique_id { get; set; }

        public int uesed_status_type { get; set; }
    }
}
