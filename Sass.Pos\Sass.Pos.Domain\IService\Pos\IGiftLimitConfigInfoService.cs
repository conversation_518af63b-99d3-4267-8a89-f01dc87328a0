﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    public interface IGiftLimitConfigInfoService
    {
        [OperationContract]
        ResponseContext<SaveGiftLimitConfigInfoModel> SaveLimitConfig(SaveGiftLimitConfigInfoContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetGiftLimitConfigInfoModel>> GetLimitConfigList(GetGiftLimitConfigInfoContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetGiftLimitConfigInfoModel>> GetLimitConfigListByStore(GetGiftLimitConfigInfoContext context);

        [OperationContract]
        ResponseContext<SaveGiftLimitConfigInfoModel> SaveLimitConfigByStore(SaveGiftLimitConfigInfoContext context);
    }
}
