﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Coupons.Context;
using Saas.Pos.Model.Coupons.Model;
using Saas.Pos.Model.GrouponBase.Context;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.GrouponBase
{
    [ServiceContract]
    public interface IGrouponInfoService
    {
        [OperationContract]
        ResponseContext<GetCustomCouponInfoModel> GetGroponInfo(GetGrouponInfoContext context);

        [OperationContract]
        ResponseContext<CouponsVerifyModel> GroponVerify(CouponsVerifyContext context);

        //[OperationContract]
        //ResponseContext<ReverseConsumeModel> GroponReverse(ReverseConsumeContext context);

        [OperationContract]
        ResponseContext<GetCustomCouponDetailModel> GetCustomCouponDetail(GetCustomCouponDetailContext context);

        [OperationContract]
        ResponseContext<GetCustomCouponModel> GetCustomCouponData(GetCustomCouponDataContext context);

        [OperationContract]
        ResponseContext<ReturnInt> DistributeCoupons(DistributeCouponsContext context);

        [OperationContract]
        ResponseContext<RegisterCouponsModel> RegisterCoupons(RegisterCouponsContext context);

        [OperationContract]
        ResponseContext<Model.SaasPos.Context.ReturnBool> UnFreezeCoupons(UnFreezeCouponsContext context);
    }
}
