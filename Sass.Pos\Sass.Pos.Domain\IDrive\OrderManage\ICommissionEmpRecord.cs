﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IDrive.OrderManage
{
    public interface ICommissionEmpRecord
    {
        /// <summary>
        /// 记录当前订单的提成信息
        /// </summary>
        /// <returns></returns>
        ResponseContext<ReturnBool> SaveOrderSchemes(SaveOrderSchemesContext context);

        /// <summary>
        /// 查询订单提成记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<RespPaginationModel<GetOrderEmpRecordModel>> GetOrderSchemesList(GetOrderSchemesContext context);

        ResponseContext<ExcelEmpRecordModel> ExcelEmpRecord(ExcelEmpRecordContext context);

        ResponseContext<GetPersonCommissionPosterModel> GetPersonCommissionPoster(GetPersonCommissionPosterContext context);

        ResponseContext<GetPersonCommissionPosterModel> GetAnnualPassImage(GetPersonCommissionPosterContext context);
    }
}
