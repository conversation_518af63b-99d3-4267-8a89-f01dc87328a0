-- =====================================================
-- KTV营业数据查询系统快速部署脚本
-- 执行服务器：192.168.2.5 (operatedata数据库)
-- 目标日期：2025-07-17
-- =====================================================

USE master
GO

PRINT '开始KTV营业数据查询系统部署...'
PRINT '执行时间: ' + CONVERT(NVARCHAR(20), GETDATE(), 120)
PRINT '=========================================='

-- =====================================================
-- 第一步：配置链接服务器
-- =====================================================
PRINT '第一步：配置链接服务器到rms2019...'

-- 删除已存在的链接服务器
IF EXISTS(SELECT * FROM sys.servers WHERE name = 'RMS2019_LINK')
BEGIN
    PRINT '删除现有链接服务器...'
    EXEC sp_dropserver 'RMS2019_LINK', 'droplogins'
END

-- 创建新的链接服务器
PRINT '创建链接服务器到193.112.2.229...'
EXEC sp_addlinkedserver 
    @server = 'RMS2019_LINK',
    @srvproduct = 'SQL Server',
    @provider = 'SQLNCLI',
    @datasrc = '193.112.2.229',
    @catalog = 'dbfood'

-- 配置安全性
PRINT '配置链接服务器安全性...'
EXEC sp_addlinkedsrvlogin 
    @rmtsrvname = 'RMS2019_LINK',
    @useself = 'false',
    @locallogin = NULL,
    @rmtuser = 'sa',
    @rmtpassword = 'Musicbox@123'

-- 配置链接服务器选项
PRINT '配置链接服务器选项...'
EXEC sp_serveroption 'RMS2019_LINK', 'collation compatible', 'false'
EXEC sp_serveroption 'RMS2019_LINK', 'data access', 'true'
EXEC sp_serveroption 'RMS2019_LINK', 'rpc', 'true'
EXEC sp_serveroption 'RMS2019_LINK', 'rpc out', 'true'
EXEC sp_serveroption 'RMS2019_LINK', 'connect timeout', '30'
EXEC sp_serveroption 'RMS2019_LINK', 'query timeout', '300'

-- 测试链接服务器连接
PRINT '测试链接服务器连接...'
BEGIN TRY
    DECLARE @TestResult INT
    SELECT @TestResult = COUNT(*) FROM [RMS2019_LINK].dbfood.dbo.opencacheinfo WHERE 1=0
    PRINT '✓ 链接服务器连接成功'
END TRY
BEGIN CATCH
    PRINT '✗ 链接服务器连接失败: ' + ERROR_MESSAGE()
    PRINT '请检查网络连接和认证信息'
    -- 不要停止脚本，继续执行其他部分
END CATCH

-- =====================================================
-- 第二步：切换到operatedata数据库
-- =====================================================
PRINT '第二步：切换到operatedata数据库...'
USE operatedata
GO

-- =====================================================
-- 第三步：创建主存储过程
-- =====================================================
PRINT '第三步：创建主存储过程...'

-- 删除已存在的存储过程
IF OBJECT_ID('dbo.sp_GetKtvBusinessData', 'P') IS NOT NULL
BEGIN
    PRINT '删除现有存储过程 sp_GetKtvBusinessData...'
    DROP PROCEDURE dbo.sp_GetKtvBusinessData
END

-- 创建主存储过程
PRINT '创建主存储过程 sp_GetKtvBusinessData...'
GO

CREATE PROCEDURE [dbo].[sp_GetKtvBusinessData]
    @QueryDate DATE = NULL,
    @ShopId INT = NULL,
    @Debug BIT = 0
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;
    
    -- 参数验证和默认值设置
    IF @QueryDate IS NULL
        SET @QueryDate = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE)
    
    DECLARE @StartTime DATETIME = @QueryDate
    DECLARE @EndTime DATETIME = DATEADD(DAY, 1, @QueryDate)
    DECLARE @ErrorMessage NVARCHAR(4000)
    DECLARE @StartExecTime DATETIME = GETDATE()
    
    IF @Debug = 1
        PRINT '开始执行KTV营业数据查询，查询日期: ' + CAST(@QueryDate AS NVARCHAR(10))
    
    BEGIN TRY
        -- 创建临时表
        CREATE TABLE #BusinessData (
            InvNo NVARCHAR(50) NOT NULL,
            ShopId INT,
            RmNo NVARCHAR(20),
            OpenTime DATETIME,
            CustName NVARCHAR(100),
            Numbers INT,
            CashAmount DECIMAL(18,2) DEFAULT 0,
            VesaAmount DECIMAL(18,2) DEFAULT 0,
            WXPayAmount DECIMAL(18,2) DEFAULT 0,
            TotalAmount DECIMAL(18,2) DEFAULT 0,
            PaymentMethod NVARCHAR(100),
            IsDirect BIT DEFAULT 0,
            ChannelType NVARCHAR(50),
            OrderUserName NVARCHAR(100),
            CtName NVARCHAR(100)
        )
        
        CREATE CLUSTERED INDEX IX_BusinessData_InvNo ON #BusinessData(InvNo)
        
        -- 获取开台数据
        INSERT INTO #BusinessData (
            InvNo, ShopId, RmNo, OpenTime, CustName, Numbers, OrderUserName, CtName
        )
        SELECT 
            o.Invno,
            o.ShopId,
            o.RmNo,
            o.BookDateTime,
            o.CustName,
            ISNULL(o.Numbers, 0),
            o.OrderUserName,
            o.CtName
        FROM [RMS2019_LINK].dbfood.dbo.opencacheinfo o
        WHERE o.BookDateTime >= @StartTime 
            AND o.BookDateTime < @EndTime
            AND o.Invno IS NOT NULL 
            AND o.Invno != ''
            AND (@ShopId IS NULL OR o.ShopId = @ShopId)
        
        DECLARE @OpenRecordCount INT = @@ROWCOUNT
        
        -- 更新结账数据
        UPDATE bd
        SET 
            CashAmount = ISNULL(c.Cash, 0),
            VesaAmount = ISNULL(c.Vesa, 0),
            WXPayAmount = ISNULL(c.WXPay, 0),
            TotalAmount = ISNULL(c.Cash, 0) + ISNULL(c.Vesa, 0) + ISNULL(c.WXPay, 0),
            IsDirect = 1,
            PaymentMethod = CASE 
                WHEN ISNULL(c.WXPay, 0) > 0 THEN '微信支付'
                WHEN ISNULL(c.Vesa, 0) > 0 THEN '会员卡'
                WHEN ISNULL(c.Cash, 0) > 0 THEN '现金'
                ELSE '未支付'
            END,
            ChannelType = CASE 
                WHEN ISNULL(c.WXPay, 0) > 0 THEN '线上'
                WHEN ISNULL(c.Vesa, 0) > 0 THEN '会员'
                WHEN ISNULL(c.Cash, 0) > 0 THEN '现金'
                ELSE '其他'
            END
        FROM #BusinessData bd
        INNER JOIN dbo.rmcloseinfo c ON bd.InvNo = c.InvNo
        
        DECLARE @CloseRecordCount INT = @@ROWCOUNT
        
        -- 返回业务数据
        SELECT 
            '业务数据' as DataType,
            InvNo, ShopId, RmNo, OpenTime, CustName, Numbers,
            CashAmount, VesaAmount, WXPayAmount, TotalAmount,
            PaymentMethod, IsDirect, ChannelType, OrderUserName, CtName
        FROM #BusinessData
        ORDER BY OpenTime, InvNo
        
        -- 返回直落统计
        SELECT 
            '直落统计' as DataType,
            COUNT(*) as TotalOrders,
            SUM(CASE WHEN IsDirect = 1 THEN 1 ELSE 0 END) as DirectOrders,
            SUM(CASE WHEN IsDirect = 0 THEN 1 ELSE 0 END) as NonDirectOrders,
            CASE WHEN COUNT(*) > 0 
                THEN CAST(SUM(CASE WHEN IsDirect = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2))
                ELSE 0 END as DirectRate,
            SUM(CASE WHEN IsDirect = 1 THEN TotalAmount ELSE 0 END) as DirectAmount,
            SUM(CASE WHEN IsDirect = 0 THEN TotalAmount ELSE 0 END) as NonDirectAmount
        FROM #BusinessData
        
        -- 返回渠道统计
        SELECT 
            '渠道统计' as DataType, ChannelType,
            COUNT(*) as OrderCount, SUM(TotalAmount) as TotalAmount,
            CASE WHEN COUNT(*) > 0 THEN CAST(SUM(TotalAmount) / COUNT(*) AS DECIMAL(18,2)) ELSE 0 END as AvgAmount,
            CASE WHEN (SELECT COUNT(*) FROM #BusinessData) > 0 
                THEN CAST(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM #BusinessData) AS DECIMAL(5,2)) ELSE 0 END as Percentage
        FROM #BusinessData
        GROUP BY ChannelType
        ORDER BY TotalAmount DESC
        
        -- 返回时段统计
        SELECT 
            '时段统计' as DataType,
            CASE 
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 10 AND 13 THEN '上午(10-13)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 14 AND 17 THEN '下午(14-17)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 18 AND 21 THEN '晚上(18-21)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 22 AND 23 OR DATEPART(HOUR, OpenTime) BETWEEN 0 AND 2 THEN '深夜(22-02)'
                ELSE '其他时段'
            END as TimeSlot,
            COUNT(*) as OrderCount, SUM(TotalAmount) as TotalAmount,
            SUM(CASE WHEN IsDirect = 1 THEN 1 ELSE 0 END) as DirectCount
        FROM #BusinessData
        WHERE OpenTime IS NOT NULL
        GROUP BY 
            CASE 
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 10 AND 13 THEN '上午(10-13)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 14 AND 17 THEN '下午(14-17)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 18 AND 21 THEN '晚上(18-21)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 22 AND 23 OR DATEPART(HOUR, OpenTime) BETWEEN 0 AND 2 THEN '深夜(22-02)'
                ELSE '其他时段'
            END
        ORDER BY TotalAmount DESC
        
        -- 返回执行信息
        SELECT 
            '执行信息' as DataType, @QueryDate as QueryDate,
            @OpenRecordCount as OpenRecordCount, @CloseRecordCount as CloseRecordCount,
            DATEDIFF(MILLISECOND, @StartExecTime, GETDATE()) as ExecutionTimeMs, 'SUCCESS' as Status
        
        DROP TABLE #BusinessData
        
    END TRY
    BEGIN CATCH
        IF OBJECT_ID('tempdb..#BusinessData') IS NOT NULL
            DROP TABLE #BusinessData
            
        SET @ErrorMessage = ERROR_MESSAGE()
        SELECT '错误信息' as DataType, @ErrorMessage as ErrorMessage, 'ERROR' as Status
        RAISERROR(@ErrorMessage, 16, 1)
    END CATCH
END
GO

-- =====================================================
-- 第四步：创建验证存储过程
-- =====================================================
PRINT '第四步：创建验证存储过程...'

IF OBJECT_ID('dbo.sp_ValidateKtvBusinessData', 'P') IS NOT NULL
    DROP PROCEDURE dbo.sp_ValidateKtvBusinessData

GO

CREATE PROCEDURE [dbo].[sp_ValidateKtvBusinessData]
    @QueryDate DATE = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @QueryDate IS NULL
        SET @QueryDate = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE)
    
    DECLARE @StartTime DATETIME = @QueryDate
    DECLARE @EndTime DATETIME = DATEADD(DAY, 1, @QueryDate)
    
    -- 检查链接服务器连接
    BEGIN TRY
        DECLARE @TestCount INT
        SELECT @TestCount = COUNT(*) FROM [RMS2019_LINK].dbfood.dbo.opencacheinfo WHERE 1=0
        SELECT '链接服务器连接' as CheckType, 'SUCCESS' as Status, '连接正常' as Message
    END TRY
    BEGIN CATCH
        SELECT '链接服务器连接' as CheckType, 'ERROR' as Status, ERROR_MESSAGE() as Message
        RETURN -1
    END CATCH
    
    -- 数据质量检查
    DECLARE @OpenCount INT, @CloseCount INT, @MatchCount INT
    
    SELECT @OpenCount = COUNT(*) 
    FROM [RMS2019_LINK].dbfood.dbo.opencacheinfo 
    WHERE BookDateTime >= @StartTime AND BookDateTime < @EndTime 
        AND Invno IS NOT NULL AND Invno != ''
    
    SELECT @CloseCount = COUNT(*) FROM dbo.rmcloseinfo
    
    SELECT @MatchCount = COUNT(*) 
    FROM [RMS2019_LINK].dbfood.dbo.opencacheinfo o
    INNER JOIN dbo.rmcloseinfo c ON o.Invno = c.InvNo
    WHERE o.BookDateTime >= @StartTime AND o.BookDateTime < @EndTime
        AND o.Invno IS NOT NULL AND o.Invno != ''
    
    SELECT 
        '数据质量检查' as CheckType, 'INFO' as Status, @QueryDate as QueryDate,
        @OpenCount as OpenRecords, @CloseCount as CloseRecords, @MatchCount as MatchedRecords,
        CASE WHEN @OpenCount > 0 THEN CAST(@MatchCount * 100.0 / @OpenCount AS DECIMAL(5,2)) ELSE 0 END as MatchRate
END
GO

-- =====================================================
-- 第五步：执行测试
-- =====================================================
PRINT '第五步：执行测试验证...'

-- 验证数据
PRINT '执行数据验证...'
EXEC sp_ValidateKtvBusinessData '2025-07-17'

-- 测试主存储过程
PRINT '执行主存储过程测试...'
EXEC sp_GetKtvBusinessData '2025-07-17', NULL, 1

PRINT '=========================================='
PRINT 'KTV营业数据查询系统部署完成！'
PRINT '完成时间: ' + CONVERT(NVARCHAR(20), GETDATE(), 120)
PRINT '=========================================='
PRINT ''
PRINT '使用说明：'
PRINT '1. 获取昨天营业数据：EXEC sp_GetKtvBusinessData'
PRINT '2. 获取指定日期数据：EXEC sp_GetKtvBusinessData ''2025-07-17'''
PRINT '3. 获取指定店铺数据：EXEC sp_GetKtvBusinessData ''2025-07-17'', 1'
PRINT '4. 验证数据质量：EXEC sp_ValidateKtvBusinessData ''2025-07-17'''
PRINT ''
