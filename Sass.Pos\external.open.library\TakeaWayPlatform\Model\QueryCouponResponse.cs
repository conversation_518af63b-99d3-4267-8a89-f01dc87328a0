﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace external.open.library.TakeaWayPlatform.Model
{
    public class QueryCouponResponse
    {
        /// <summary>
        /// 券码
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }
        /// <summary>
        /// 券有效期截止
        /// </summary>
        public DateTime EndTime { get; set; }
        /// <summary>
        /// 券有效期开始
        /// </summary>
        public DateTime StartTime { get; set; }
        /// <summary>
        /// 抖音平台专门返回（测试发现抖音平台核销的时候不会返回商品编号，只能查询明细ID）
        /// </summary>
        public List<string> ThirdFdNos { get; set; }

        /// <summary>
        /// 真实可用数量
        /// </summary>
        public int Count { get; set; }
        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }
    }
}
