﻿using Saas.Pos.Common.Attributes.ValidationFilter.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Attributes.ValidationFilter
{
    /// <summary>
    /// 字符串最大长度校验
    /// </summary>
    public class StringLengthAttribute : ValidationFilterAttribute
    {
        public int MaxLength { get; private set; }

        public StringLengthAttribute(int maxLength)
        {
            MaxLength = maxLength;
        }

        public override void Inspect(object currentValue, ValidationContext context)
        {
            if (MaxLength < 0)
                throw new Exception(string.Format(AppInterface.dat.lang["StringLengthMsg2"], context.PropertyName));
            if (currentValue != null && currentValue.ToString().Trim().Length > MaxLength)
                throw new Exception(string.IsNullOrEmpty(ErrorMessage) ? string.Format(AppInterface.dat.lang["StringLengthMsg1"], context.PropertyName) : ErrorMessage);
        }
    }
}
