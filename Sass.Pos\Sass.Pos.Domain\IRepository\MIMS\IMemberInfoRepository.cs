﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.MIMS;
using Saas.Pos.Model.MIMS.Context;
using Saas.Pos.Model.MIMS.Model;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.MIMS
{
    public partial interface IMemberInfoRepository : IRepositoryBase<MemberInfo>
    {
        int MemBerDeductMoney(MemBerAcc context);

        List<GetBookingNewAttDayDataModel> GetBookingNewAttDayData(GetBookingNewAttDayDataContext context);

        List<ExportBookingData> ExportGetBookingNewAttDayData(ExportBookingNewAttDayDataContext context);

        MemberTopUpEditModel MemberTopUpEdit(MemberTopUpEditContext context);

        List<GetMemberCardRecordDataModel> GetMemberCardRecordData(GetMemberCardRecordDataContext context);

        bool IntegralOreder(IntegralOrederContext context);

        BindMemberInfo GetBindMemberInfo(string cardNumber);

        int InsertMember(MemberInfo member);

        int InsertMemberCard(MemberCardInfo cardInfo);

        int UpdateMemberPhone(string phone, Guid memberKye);

        int InsertRecord(MemberGiftRecord memberGift);
    }
}
