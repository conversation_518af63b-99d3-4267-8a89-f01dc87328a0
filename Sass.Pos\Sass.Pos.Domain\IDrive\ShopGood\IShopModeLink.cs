﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive
{
    public interface IShopModeLink
    {
        ResponseContext<ReturnInt> SaveShopModeLink(SaveShopModeLinkContext context);

        ResponseContext<ReturnInt> DeleteShopModeLink(DeleteShopModeLinkContext context);

        ResponseContext<RespPaginationModel<GetModeLinkDataModel>> GetModeLinkData(GetModeLinkDataContext context);
    }
}
