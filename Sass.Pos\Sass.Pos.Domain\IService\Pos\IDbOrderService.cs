﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    public interface IDbOrderService
    {
        [OperationContract]
        ResponseContext<bool> PlaceOrder(PlaceOrderModel model);

        [OperationContract]
        ResponseContext<int> GetOrderNumbers(GetOrderNumbersContext context);

        [OperationContract]
        ResponseContext<List<GetRoomConsumeModel>> GetRoomNumberList(GetRoomNumberContext context);

        [OperationContract]
        ResponseContext<List<GetRoomConsumeModel>> GetRoomNumberListByStore(GetRoomNumberContext context);

        [OperationContract]
        ResponseContext<GetUserPermission> CheckUserPermis(PermissionCheckContext context);

        [OperationContract]
        ResponseContext<GetUserPermission> CheckUserPermisByStore(PermissionCheckContext context);

        [OperationContract]
        ResponseContext<int> CreateHeadDetail(ConfirmNumberContext context);

        [OperationContract]
        ResponseContext<GetCheckoutSuggestTimeModel> GetCheckoutSuggestTime(GetCheckoutSuggestTimeContext context);

        [OperationContract]
        ResponseContext<GetCheckoutSuggestTimeModel> GetCheckoutSuggestTimeByStore(GetCheckoutSuggestTimeContext context);

        [OperationContract]
        ResponseContext<ContinueOrderModel> ContinueOrder(ContinueOrderContext context);

        [OperationContract]
        ResponseContext<ContinueOrderModel> ContinueOrderByStore(ContinueOrderContext context);

        [OperationContract]
        ResponseContext<GetFdCashInfoModel> GetFdCashInfoByStore(GetFdCashInfoContext context);

        [OperationContract]
        ResponseContext<GetFdCashInfoModel> GetFdCashInfo(GetFdCashInfoContext context);

        [OperationContract]
        ResponseContext<bool> Bill_Fd_DelOrder(Bill_Fd_DelOrderContext context);

        [OperationContract]
        ResponseContext<bool> Bill_Fd_DelOrderByStore(Bill_Fd_DelOrderContext context);

        [OperationContract]
        ResponseContext<bool> Bill_RefToZD(Bill_RefToZDContext context);

        [OperationContract]
        ResponseContext<bool> Bill_RefToZDByStore(Bill_RefToZDContext context);

        [OperationContract]
        ResponseContext<bool> Bill_RefReCall(Bill_RefReCallContext context);

        [OperationContract]
        ResponseContext<bool> Bill_RefReCallByStore(Bill_RefReCallContext context);

        [OperationContract]
        ResponseContext<bool> Bill_AddQrInfo(Bill_AddQrInfoContext context);

        [OperationContract]
        ResponseContext<bool> Bill_AddQrInfoByStore(Bill_AddQrInfoContext context);

        [OperationContract]
        ResponseContext<bool> Bill_AddWxPayInfo(Bill_AddWxPayInfoContext context);

        [OperationContract]
        ResponseContext<List<DbFoodBusinessReportModel>> GetBusinessData(DbFoodBusinessReportContext context);

        [OperationContract]
        ResponseContext<ReturnBool> ExprotBusinessData(DbFoodBusinessReportContext context);

        [OperationContract]
        ResponseContext<GetFdInvInfoModel> GetFdInvInfo(GetFdInvInfoContext context);

        [OperationContract]
        ResponseContext<ReturnBool> ChangeFdInvStatus(GetFdInvInfoContext context);
    }
}
