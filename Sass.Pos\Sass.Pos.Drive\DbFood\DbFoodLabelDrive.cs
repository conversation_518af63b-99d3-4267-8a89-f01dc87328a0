using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Common;
using Saas.Pos.Common.Exceptions;
using Saas.Pos.Common.Extend;
using Saas.Pos.Common.Log;
using Saas.Pos.Common.Rms;
using Saas.Pos.Common.ServiceClient;
using Saas.Pos.Common.Tools;
using Saas.Pos.Drive.Common.Extend;
using Saas.Pos.Drive.SaasPos.StoreManage;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.Enum;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.DbFood
{
    public class DbFoodLabelDrive : DriveBase
    {
        /// <summary>
        /// 获取门店食品标签列表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<RespPaginationModel<GetFoodLabelListModel>> GetFoodLabelList(FoodLabelContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var list = app.DbFood.FoodLabel.GetFoodLabelList(context);

                var resultData = RespPaginationModel<GetFoodLabelListModel>.Package(context.pagination, list);

                return resultData;
            });
        }

        /// <summary>
        /// 批量导入（存在的就进行修改，不存在的就进行插入）
        /// </summary>
        /// <returns></returns>
        public ResponseContext<int> BatchImport(List<BatchImportContext> context)
        {
            return ActionFun.Run(context, () =>
            {
                //不存在食品标签的数据
                int count = 0;
                var noFoods = new List<string>();
                //待插入数据
                var insertData = new List<FoodLabel>();
                var fdNos = context.Select(w => w.FdNo).Distinct().ToList();
                //数据库已存在标签数据
                var dbFoodLabelList = app.DbFood.FoodLabel.IQueryable(w => fdNos.Contains(w.FdNo)).ToList();
                var dbFoodList = app.DbFood.Food.IQueryable(w => fdNos.Contains(w.FdNo)).Select(w => new
                {
                    FdNo = w.FdNo,
                    FdCName = w.FdCName
                }).ToList();
                int foodCodeIndex = app.DbFood.FoodLabel.GetMaxCode();
                context.ForEach(w =>
                {
                    if (w.TypeId.HasValue)
                        w.Type = EnumHelper.GetEnumDescription((DbFdLabelTypeEnum)w.TypeId.Value);
                    if (w.CateId.HasValue && string.IsNullOrEmpty(w.Category1))
                        w.Category1 = EnumHelper.GetEnumDescription((DbFdLabelCategoryEnum)w.CateId.Value);

                    var food = dbFoodList.FirstOrDefault(x => x.FdNo == w.FdNo);
                    if (food == null)
                    {
                        noFoods.Add(w.FdNo);
                        return;
                    }

                    var label = dbFoodLabelList.FirstOrDefault(x => x.FdNo == w.FdNo);
                    //如果存在，就修改数据否则就插入数据
                    if (label != null)
                    {
                        label.RtNo = w.RtNo;
                        label.Type = w.Type;
                        label.PeoNumber = w.PeoNumber;
                        label.Platform = w.Platform;
                        label.Depart = w.Depart;
                        label.BeverageType = w.BeverageType;
                        label.SnackType = w.SnackType;
                        label.Unit = w.Unit;
                        label.MemberMode = w.MemberMode;
                        label.CardType = w.CardType;
                        label.CashType = w.CashType;
                        label.HeadType = w.HeadType;
                        label.Period = w.Period;
                        label.Bank = w.Bank;
                        label.VoucherType = w.VoucherType;
                        label.MemberCard = w.MemberCard;
                        label.MemberLevel = w.MemberLevel;
                        label.Losses = w.Losses;
                        label.PriceType = w.PriceType;
                        label.BJF = w.BJF;
                        label.ServiceCharge = w.ServiceCharge;
                        label.Deduction = w.Deduction;
                        label.Hours = w.Hours;
                        label.Quantity = w.Quantity;
                        label.Integral = w.Integral;
                        label.PackageCost = w.PackageCost;
                        label.PackageType = w.PackageType;
                        label.GoodsType = w.GoodsType;
                        label.DivideBar = w.DivideBar;
                        label.DivideBuffet = w.DivideBuffet;
                        label.DivideInfield = w.DivideInfield;
                        label.DivideRestaurant = w.DivideRestaurant;
                        label.Category1 = w.Category1;
                        label.Category2 = w.Category2;
                        label.TimeNo = w.TimeNo;
                        label.SaleDate = w.SaleDate;
                        label.FtNo = w.FtNo;
                        label.RtNo = w.RtNo;
                        label.CtTypeName = w.CtTypeName;
                        label.NightPeoNumber = w.NightPeoNumber;
                        label.NightPay = w.NightPay;
                        label.FreeRmFeeBegHour = w.FreeRmFeeBegHour;
                        label.FreeRmFeeEndHour = w.FreeRmFeeEndHour;

                        count += app.DbFood.FoodLabel.Update(label);
                    }
                    else
                    {
                        var labelData = EntityConversion.Map<FoodLabel>(w);
                        labelData.FoodCode = (++foodCodeIndex).ToString();
                        insertData.Add(labelData);
                    }
                });

                if (insertData.Count > 0)
                {
                    app.DbFood.FoodLabel.Insert(insertData);
                    count = app.DbFood.FoodLabel.SaveChanges();
                }

                if (noFoods.Count > 0)
                    throw new ExMessage("以下食品编号不存在，请检查！" + string.Join(",", noFoods));

                return count;
            });
        }

        /// <summary>
        /// 创建食品标签列表
        /// </summary>
        /// <returns></returns>
        public ResponseContext<int> CreateLabel(CreateLabelContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var result = 0;
                try
                {
                    if (string.IsNullOrEmpty(context.FdNo))
                        throw new ExMessage("请输入食品编号！");

                    var food = app.DbFood.Food.GetFood(context.FdNo);
                    if (food == null)
                        throw new ExMessage("食品信息不存在！");

                    int index = app.DbFood.FoodLabel.GetMaxCode();
                    var foodLabel = EntityConversion.Map<FoodLabel>(context);
                    foodLabel.CtTypeName = string.Empty;
                    foodLabel.NightPeoNumber = 0;
                    foodLabel.NightPay = 0;
                    foodLabel.FoodCode = (++index).ToString();
                    if (context.TypeId.HasValue)
                        foodLabel.Type = EnumHelper.GetEnumDescription((DbFdLabelTypeEnum)context.TypeId.Value);
                    if (context.CateId.HasValue)
                        foodLabel.Category1 = EnumHelper.GetEnumDescription((DbFdLabelCategoryEnum)context.CateId.Value);

                    result = app.DbFood.FoodLabel.Insert(foodLabel);
                }
                catch (Exception ex)
                {
                    ex.HandlerException();
                }
                return result;
            });
        }

        /// <summary>
        /// 获取食品标签详细信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<GetFoodLabelInfoModel> GetLabelInfo(GetFoodLabelInfoContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var data = (from label in app.DbFood.FoodLabel.IQueryable().AsEnumerable()
                            join food in app.DbFood.Food.IQueryable().AsEnumerable() on label.FdNo equals food.FdNo
                            join type in app.DbFood.FdType.IQueryable().AsEnumerable() on food.FtNo equals type.FtNo
                            where label.FdNo == context.FoodCode
                            select new GetFoodLabelInfoModel()
                            {
                                FdCName = food.FdCName,
                                FtCName = type.FtCName,
                                FoodLabel = label
                            }).FirstOrDefault();

                if (data == null)
                    throw new Exception("所选择的视频标签不存在！");

                if (!string.IsNullOrEmpty(data.FoodLabel.Type))
                    data.TypeId = EnumHelper.GetValue<DbFdLabelTypeEnum>(data.FoodLabel.Type);
                if (!string.IsNullOrEmpty(data.FoodLabel.Category1))
                    data.CateId = EnumHelper.GetValue<DbFdLabelCategoryEnum>(data.FoodLabel.Category1);

                return data;
            });
        }

        /// <summary>
        /// 删除标签
        /// </summary>
        /// <returns></returns>
        public ResponseContext<int> Delete(DeleteFoodLabelContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var label = app.DbFood.FoodLabel.FindEntity(x => x.FoodCode == context.FoodCode);
                if (label == null)
                    throw new Exception("要删除的食品标签不存在！");

                return app.DbFood.FoodLabel.Delete(label);
            });
        }

        public ResponseContext<List<KeyValuePair<string, string>>> GetFtDropDown(GetFtDropDownContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var data = app.DbFood.FdType.IQueryable().ToList().Select(x => new KeyValuePair<string, string>(x.FtCName, x.FtNo)).ToList();

                return data;
            });
        }

        public ResponseContext<List<KeyValuePair<int, string>>> GetTypes(GetFtDropDownContext context)
        {
            return ActionFun.Run(context, () =>
            {
                return typeof(DbFdLabelTypeEnum).GetEnumList();
            });
        }

        public ResponseContext<List<KeyValuePair<int, string>>> GetCates(GetFtDropDownContext context)
        {
            return ActionFun.Run(context, () =>
            {
                return typeof(DbFdLabelCategoryEnum).GetEnumList();
            });
        }

        /// <summary>
        /// 查询快捷结账页面数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<List<GetCheckOutDataModel>> GetCheckOutData(GetCheckOutDataContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var SearchDate = DateTime.Now;
                if (SearchDate < DateTime.Parse("06:00"))
                    SearchDate = SearchDate.AddDays(-1);

                var list = app.DbFood.FoodLabel.GetCheckOutData(context);
                var copyList = new List<GetCheckOutModel>();
                list.ForEach(li =>
                {
                    copyList.AddRange(li.TimeNo.Split(',').Select(w => new GetCheckOutModel()
                    {
                        CashType = li.CashType,
                        FdNo = li.FdNo,
                        FdPrice = li.FdPrice,
                        FdSPrice = li.FdSPrice,
                        HeadType = li.HeadType,
                        RtNo = li.RtNo,
                        TimeNo = w
                    }).ToList());
                });

                var month = SearchDate.Month.ToString();
                var day = SearchDate.Day.ToString();
                var sData = app.DbFood.SDate.FindEntity(w => w.SMonth == month && w.SDay == day);
                var timeNos = copyList.Select(w => w.TimeNo).Distinct().ToList();
                var rtNos = copyList.Where(w => !string.IsNullOrEmpty(w.RtNo)).Select(w => w.RtNo).Distinct().ToList();

                var timeInfo = app.Rms.timeinfo.IQueryable(w => timeNos.Contains(w.TimeNo)).ToList();
                var rtInfo = app.Rms.rtinfo.IQueryable(w => rtNos.Contains(w.RtNo) && w.ShopId == context.ShopId).OrderBy(w => w.NumberMax).Select(w => new
                {
                    w.NumberMax,
                    w.RtNo,
                    w.RtName
                }).ToList();
                var data = (from li in copyList
                            join time in timeInfo on li.TimeNo equals time.TimeNo
                            group new { li, time } by li.TimeNo into gropuData
                            select new GetCheckOutDataModel
                            {
                                TimeNo = gropuData.Key,
                                TimeName = gropuData.FirstOrDefault().time.TimeName,
                                ConsumList = gropuData.GroupBy(w => w.li.CashType).Select(w => new ConsumModel()
                                {
                                    ConsumName = w.Key,
                                    CharginList = w.OrderBy(x => x.li.HeadType)
                                    .GroupBy(x => x.li.HeadType).Select(x =>
                                    {
                                        var rtNo = x.Where(rt => !string.IsNullOrEmpty(rt.li.RtNo))
                                        .Select(rt => rt.li.RtNo).Distinct().ToList();
                                        var rtList = (from li in list
                                                      join rt in rtInfo on li.RtNo equals rt.RtNo
                                                      where li.CashType == w.Key && li.HeadType == x.Key
                                                      && rtNo.Contains(rt.RtNo)
                                                      orderby rt.NumberMax ascending
                                                      select new CheckOutModel()
                                                      {
                                                          MobileFdNo = li.FdNo,
                                                          MobileFdName = rt.RtName,
                                                          FdPrice1 = sData == null ? li.FdPrice : li.FdSPrice
                                                      }).ToList();

                                        return new ChargingModel()
                                        {
                                            Charging = new CheckOutModel()
                                            {
                                                MobileFdName = x.Key,
                                                MobileFdNo = x.FirstOrDefault().li.FdNo,
                                                FdPrice1 = sData == null ? x.FirstOrDefault().li.FdPrice : x.FirstOrDefault().li.FdSPrice
                                            },
                                            RtList = rtList
                                        };
                                    }).ToList()
                                }).ToList()
                            }).OrderBy(w => w.TimeName).ToList();

                var currentDate = DateTime.Now;
                var result = new List<GetCheckOutDataModel>();
                if (data.Count > 0)
                {
                    var startIndex = data.FindIndex(w => w.TimeNo.DetectionTimeIn(timeInfo) && SearchDate.Date == currentDate.Date);
                    if (startIndex >= 0)
                    {
                        var endIndex = data.FindIndex(startIndex, w => !w.TimeNo.DetectionTimeIn(timeInfo) && SearchDate.Date == currentDate.Date);
                        //找到最后的索引
                        if (endIndex < 0)
                            endIndex = data.Count - 1;

                        if (endIndex > 0)
                            result.AddRange(data.GetRange(startIndex, endIndex + 1 - startIndex));
                    }
                    else
                        result.Add(data.LastOrDefault());
                }

                //if (startIndex >= 0 && endIndex > 0)
                //    result.AddRange(data.GetRange(startIndex, endIndex + 1 - startIndex));
                //else
                //    result.Add(data.LastOrDefault());

                result.OrderBy(w => w.TimeName).ToList().ForEach(w =>
                {
                    w.ConsumList = w.ConsumList.OrderBy(x => x.ConsumName).ToList();
                    var timeData = timeInfo.FirstOrDefault(time => time.TimeNo == w.TimeNo);
                    w.StartTime = BookingHelper.ConvertTimeDate(SearchDate, timeData.BegTime.ToString());
                    w.EndTime = BookingHelper.ConvertTimeDate(SearchDate, timeData.EndTime.ToString());
                });
                return result;
            });
        }

        public ResponseContext<List<GetCheckOutDataModel>> GetCheOutList(GetCheckOutDataContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    var SearchDate = DateTime.Now;
                    if (SearchDate < DateTime.Parse("06:00"))
                        SearchDate = SearchDate.AddDays(-1);

                    var storeDrive = new StoreManageDrive();
                    var contextEx = context.Clone<SERVICE.PROXY.PosService.GetCheckOutDataContext>();
                    contextEx.Week = storeDrive.Smb.GetBtSpecialWeekTime(new BeginEndBusinessTimeContext()
                    {
                        NowDate = SearchDate.Date,
                        ShopId = context.ShopId
                    }).Item1;

                    var posClient = ServiceClientBase.GetPosClient(context.ShopId);
                    var data = posClient.GetCheckOutData(contextEx);
                    if (data.state != ResponseType.success)
                        throw new Exception(data.message);

                    var result = new List<GetCheckOutDataModel>();
                    foreach (var item in data.data)
                    {
                        result.Add(new GetCheckOutDataModel()
                        {
                            TimeNo = item.TimeNo,
                            TimeName = item.TimeName,
                            StartTime = item.StartTime,
                            EndTime = item.EndTime,
                            ConsumList = item.ConsumList.Select(w => new ConsumModel()
                            {
                                ConsumName = w.ConsumName,
                                CharginList = w.CharginList.Select(charg => new ChargingModel()
                                {
                                    Charging = new CheckOutModel()
                                    {
                                        MobileFdNo = charg.Charging.MobileFdNo,
                                        MobileFdName = charg.Charging.MobileFdName,
                                        FdPrice1 = charg.Charging.FdPrice1
                                    },
                                    RtList = charg.RtList.Select(rt => new CheckOutModel()
                                    {
                                        MobileFdNo = rt.MobileFdNo,
                                        MobileFdName = rt.MobileFdName,
                                        FdPrice1 = rt.FdPrice1
                                    }).ToList()
                                }).ToList()
                            }).ToList()
                        });
                    }
                    return result;
                }
                catch (Exception ex)
                {
                    throw new ClientException(ex.Message);
                }
            });
        }

        /// <summary>
        /// 结账下单
        /// </summary>
        /// <returns>true：下单成功；false：下单失败</returns>
        public ResponseContext<bool> CheckOutOrder(CheckOutOrderContext context)
        {
            return ActionFun.Run(context, () =>
            {
                if (context.OrderList.Count <= 0)
                    throw new ExMessage("请输入下单详情！");

                //获取所有下单人数
                var peoNumber = context.OrderList.Where(w => w.Number > 0).Sum(w => w.Number);
                //获取下单人数大于0的数据
                var orderData = context.OrderList.Where(w => w.Number > 0).ToList();
                var fdNos = orderData.Select(w => w.FdNo).Distinct().ToList();

                //查询商品价格
                var foodData = app.DbFood.Food.IQueryable(w => fdNos.Contains(w.FdNo)).Select(w => new
                {
                    w.FdNo,
                    FdPrice = w.FdPrice1
                });

                var placeOrderData = new PlaceOrderModel()
                {
                    CashType = context.CashType,
                    CashUserId = context.CashUserId,
                    InputUserId = string.Empty,
                    RmNo = context.RmNo,
                    StoreId = context.ShopId
                };
                var items = new List<OrderItem>();
                orderData.ForEach(order =>
                {
                    var food = foodData.FirstOrDefault(w => w.FdNo == order.FdNo);
                    if (food != null)
                    {
                        items.Add(new OrderItem()
                        {
                            FdNo = food.FdNo,
                            FdQty = order.Number,
                            FdPrice = food.FdPrice,//价格待查询
                            Ai = string.Empty
                        });
                    }
                });

                //添加下单人数
                items.Add(new OrderItem()
                {
                    FdNo = GlobalConfig.Global.OrderConfig.HeadFdNo,
                    FdPrice = 0,
                    FdQty = peoNumber,
                    Ai = string.Empty
                });

                placeOrderData.Items = items;
                var orderDrive = new OrderServiceDrive();
                var resp = orderDrive.PlaceOrder(placeOrderData);
                if (resp.state != ResponseType.success)
                    throw new ExMessage(resp.message);

                return resp.data;
            });
        }

        /// <summary>
        /// 结账
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<bool> CheckOut(CheckOutOrderContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    var posClient = ServiceClientBase.GetPosClient(context.ShopId);
                    var contextEx = context.Clone<SERVICE.PROXY.PosService.CheckOutOrderContext>();
                    contextEx.OrderList = context.OrderList.Select(w => new SERVICE.PROXY.PosService.CheckOutOrder()
                    {
                        FdNo = w.FdNo,
                        Number = w.Number
                    }).ToArray();

                    var data = posClient.CheckOutOrder(contextEx);
                    if (data.state != ResponseType.success)
                        throw new Exception(data.message);

                    return data.data;
                }
                catch (Exception ex)
                {
                    throw new ClientException(ex.Message);
                }
            });
        }
    }
}
