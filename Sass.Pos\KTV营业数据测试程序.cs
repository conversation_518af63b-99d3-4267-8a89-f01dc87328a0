using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.Linq;

namespace KtvBusinessDataTest
{
    /// <summary>
    /// KTV营业数据测试程序
    /// 用于验证跨数据库查询功能和业务逻辑
    /// </summary>
    public class KtvBusinessDataTestProgram
    {
        private readonly string _connectionString = 
            "Data Source=192.168.2.5;Initial Catalog=operatedata;User ID=sa;Password=***********;Connection Timeout=30;Command Timeout=300;";

        /// <summary>
        /// 执行完整的KTV营业数据测试
        /// </summary>
        public async Task<TestResult> ExecuteFullTestAsync()
        {
            var testResult = new TestResult
            {
                TestStartTime = DateTime.Now,
                TestName = "KTV营业数据跨库查询测试",
                TestPeriod = "2025-05-01 到 2025-05-07",
                ShopId = 11
            };

            try
            {
                Console.WriteLine("========================================");
                Console.WriteLine("开始执行KTV营业数据跨库查询测试");
                Console.WriteLine($"测试时间: {testResult.TestStartTime:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine($"测试范围: {testResult.TestPeriod}, ShopId = {testResult.ShopId}");
                Console.WriteLine("========================================");

                // 第一步：验证数据库连接
                Console.WriteLine("\n第一步：验证数据库连接...");
                var connectionTest = await TestDatabaseConnectionAsync();
                testResult.ConnectionTestResult = connectionTest;
                
                if (!connectionTest.IsSuccess)
                {
                    testResult.OverallResult = "FAILED";
                    testResult.ErrorMessage = "数据库连接失败";
                    return testResult;
                }

                // 第二步：执行业务数据测试
                Console.WriteLine("\n第二步：执行业务数据测试...");
                var businessData = await GetBusinessDataAsync(
                    new DateTime(2025, 5, 1), 
                    new DateTime(2025, 5, 7), 
                    11);
                
                testResult.BusinessDataResult = businessData;

                // 第三步：分析测试结果
                Console.WriteLine("\n第三步：分析测试结果...");
                AnalyzeTestResults(testResult);

                testResult.TestEndTime = DateTime.Now;
                testResult.ExecutionTimeMs = (int)(testResult.TestEndTime - testResult.TestStartTime).TotalMilliseconds;
                testResult.OverallResult = "SUCCESS";

                // 输出测试报告
                PrintTestReport(testResult);

                return testResult;
            }
            catch (Exception ex)
            {
                testResult.TestEndTime = DateTime.Now;
                testResult.OverallResult = "ERROR";
                testResult.ErrorMessage = ex.Message;
                Console.WriteLine($"\n测试执行失败: {ex.Message}");
                return testResult;
            }
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        private async Task<ConnectionTestResult> TestDatabaseConnectionAsync()
        {
            var result = new ConnectionTestResult();
            
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    // 测试基本连接
                    using (var command = new SqlCommand("SELECT 1", connection))
                    {
                        await command.ExecuteScalarAsync();
                        result.BasicConnectionTest = true;
                        Console.WriteLine("✓ 基本数据库连接测试通过");
                    }

                    // 测试链接服务器
                    using (var command = new SqlCommand(
                        "SELECT COUNT(*) FROM [RMS2019_LINK].rms2019.dbo.opencacheinfo WHERE 1=0", 
                        connection))
                    {
                        await command.ExecuteScalarAsync();
                        result.LinkedServerTest = true;
                        Console.WriteLine("✓ 链接服务器连接测试通过");
                    }

                    result.IsSuccess = true;
                }
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;
                Console.WriteLine($"✗ 数据库连接测试失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 获取业务数据
        /// </summary>
        private async Task<BusinessDataResult> GetBusinessDataAsync(DateTime startDate, DateTime endDate, int shopId)
        {
            var result = new BusinessDataResult();
            
            using (var connection = new SqlConnection(_connectionString))
            {
                using (var command = new SqlCommand("sp_TestKtvBusinessData", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.CommandTimeout = 300;
                    command.Parameters.AddWithValue("@StartDate", startDate);
                    command.Parameters.AddWithValue("@EndDate", endDate);
                    command.Parameters.AddWithValue("@ShopId", shopId);

                    await connection.OpenAsync();

                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        // 读取基础营业数据
                        result.BusinessData = await ReadBusinessDataAsync(reader);
                        Console.WriteLine($"✓ 获取基础营业数据: {result.BusinessData.Count} 条记录");

                        // 读取直落统计
                        if (await reader.NextResultAsync())
                        {
                            result.DirectAnalysis = await ReadDirectAnalysisAsync(reader);
                            Console.WriteLine($"✓ 获取直落统计数据: {result.DirectAnalysis.Count} 天数据");
                        }

                        // 读取渠道统计
                        if (await reader.NextResultAsync())
                        {
                            result.ChannelAnalysis = await ReadChannelAnalysisAsync(reader);
                            Console.WriteLine($"✓ 获取渠道统计数据: {result.ChannelAnalysis.Count} 个渠道");
                        }

                        // 读取时段统计
                        if (await reader.NextResultAsync())
                        {
                            result.TimeSlotAnalysis = await ReadTimeSlotAnalysisAsync(reader);
                            Console.WriteLine($"✓ 获取时段统计数据: {result.TimeSlotAnalysis.Count} 个时段");
                        }

                        // 读取数据质量报告
                        if (await reader.NextResultAsync())
                        {
                            result.QualityReport = await ReadQualityReportAsync(reader);
                            Console.WriteLine($"✓ 获取数据质量报告: 关联率 {result.QualityReport.MatchRate}%");
                        }
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 读取基础营业数据
        /// </summary>
        private async Task<List<BusinessDataModel>> ReadBusinessDataAsync(SqlDataReader reader)
        {
            var data = new List<BusinessDataModel>();
            
            while (await reader.ReadAsync())
            {
                data.Add(new BusinessDataModel
                {
                    TestDate = Convert.ToDateTime(reader["TestDate"]),
                    InvNo = reader["InvNo"].ToString(),
                    ShopId = Convert.ToInt32(reader["ShopId"]),
                    RmNo = reader["RmNo"].ToString(),
                    OpenTime = reader["OpenTime"] as DateTime?,
                    CustName = reader["CustName"].ToString(),
                    Numbers = Convert.ToInt32(reader["Numbers"]),
                    CashAmount = Convert.ToDecimal(reader["CashAmount"]),
                    VesaAmount = Convert.ToDecimal(reader["VesaAmount"]),
                    WXPayAmount = Convert.ToDecimal(reader["WXPayAmount"]),
                    TotalAmount = Convert.ToDecimal(reader["TotalAmount"]),
                    PaymentMethod = reader["PaymentMethod"].ToString(),
                    IsDirect = Convert.ToBoolean(reader["IsDirect"]),
                    ChannelType = reader["ChannelType"].ToString()
                });
            }
            
            return data;
        }

        /// <summary>
        /// 读取直落分析数据
        /// </summary>
        private async Task<List<DirectAnalysisModel>> ReadDirectAnalysisAsync(SqlDataReader reader)
        {
            var data = new List<DirectAnalysisModel>();
            
            while (await reader.ReadAsync())
            {
                data.Add(new DirectAnalysisModel
                {
                    TestDate = Convert.ToDateTime(reader["TestDate"]),
                    TotalOrders = Convert.ToInt32(reader["TotalOrders"]),
                    DirectOrders = Convert.ToInt32(reader["DirectOrders"]),
                    NonDirectOrders = Convert.ToInt32(reader["NonDirectOrders"]),
                    DirectRate = Convert.ToDecimal(reader["DirectRate"]),
                    DirectAmount = Convert.ToDecimal(reader["DirectAmount"]),
                    NonDirectAmount = Convert.ToDecimal(reader["NonDirectAmount"])
                });
            }
            
            return data;
        }

        /// <summary>
        /// 读取渠道分析数据
        /// </summary>
        private async Task<List<ChannelAnalysisModel>> ReadChannelAnalysisAsync(SqlDataReader reader)
        {
            var data = new List<ChannelAnalysisModel>();
            
            while (await reader.ReadAsync())
            {
                data.Add(new ChannelAnalysisModel
                {
                    ChannelType = reader["ChannelType"].ToString(),
                    OrderCount = Convert.ToInt32(reader["OrderCount"]),
                    TotalAmount = Convert.ToDecimal(reader["TotalAmount"]),
                    AvgAmount = Convert.ToDecimal(reader["AvgAmount"]),
                    Percentage = Convert.ToDecimal(reader["Percentage"])
                });
            }
            
            return data;
        }

        /// <summary>
        /// 读取时段分析数据
        /// </summary>
        private async Task<List<TimeSlotAnalysisModel>> ReadTimeSlotAnalysisAsync(SqlDataReader reader)
        {
            var data = new List<TimeSlotAnalysisModel>();
            
            while (await reader.ReadAsync())
            {
                data.Add(new TimeSlotAnalysisModel
                {
                    TimeSlot = reader["TimeSlot"].ToString(),
                    OrderCount = Convert.ToInt32(reader["OrderCount"]),
                    TotalAmount = Convert.ToDecimal(reader["TotalAmount"]),
                    DirectCount = Convert.ToInt32(reader["DirectCount"]),
                    AvgAmount = Convert.ToDecimal(reader["AvgAmount"])
                });
            }
            
            return data;
        }

        /// <summary>
        /// 读取数据质量报告
        /// </summary>
        private async Task<QualityReportModel> ReadQualityReportAsync(SqlDataReader reader)
        {
            if (await reader.ReadAsync())
            {
                return new QualityReportModel
                {
                    StartDate = Convert.ToDateTime(reader["StartDate"]),
                    EndDate = Convert.ToDateTime(reader["EndDate"]),
                    ShopId = Convert.ToInt32(reader["ShopId"]),
                    OpenRecordCount = Convert.ToInt32(reader["OpenRecordCount"]),
                    CloseRecordCount = Convert.ToInt32(reader["CloseRecordCount"]),
                    MatchRate = Convert.ToDecimal(reader["MatchRate"]),
                    ExecutionTimeMs = Convert.ToInt32(reader["ExecutionTimeMs"]),
                    Status = reader["Status"].ToString()
                };
            }
            
            return new QualityReportModel();
        }

        /// <summary>
        /// 分析测试结果
        /// </summary>
        private void AnalyzeTestResults(TestResult testResult)
        {
            var businessData = testResult.BusinessDataResult;
            
            // 验证数据完整性
            if (businessData.QualityReport.MatchRate >= 85)
            {
                Console.WriteLine($"✓ 数据完整性验证通过: {businessData.QualityReport.MatchRate}%");
            }
            else
            {
                Console.WriteLine($"⚠ 数据完整性警告: {businessData.QualityReport.MatchRate}%");
            }

            // 验证直落率合理性
            var avgDirectRate = businessData.DirectAnalysis.Average(x => x.DirectRate);
            if (avgDirectRate >= 80 && avgDirectRate <= 95)
            {
                Console.WriteLine($"✓ 直落率验证通过: {avgDirectRate:F1}%");
            }
            else
            {
                Console.WriteLine($"⚠ 直落率异常: {avgDirectRate:F1}%");
            }

            // 验证渠道分布合理性
            var totalChannelPercentage = businessData.ChannelAnalysis.Sum(x => x.Percentage);
            if (Math.Abs(totalChannelPercentage - 100) < 1)
            {
                Console.WriteLine($"✓ 渠道分布验证通过: {totalChannelPercentage:F1}%");
            }
            else
            {
                Console.WriteLine($"⚠ 渠道分布异常: {totalChannelPercentage:F1}%");
            }
        }

        /// <summary>
        /// 打印测试报告
        /// </summary>
        private void PrintTestReport(TestResult testResult)
        {
            Console.WriteLine("\n========================================");
            Console.WriteLine("测试报告汇总");
            Console.WriteLine("========================================");
            Console.WriteLine($"测试名称: {testResult.TestName}");
            Console.WriteLine($"测试时间: {testResult.TestStartTime:yyyy-MM-dd HH:mm:ss} - {testResult.TestEndTime:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"执行耗时: {testResult.ExecutionTimeMs}ms");
            Console.WriteLine($"测试结果: {testResult.OverallResult}");
            
            if (testResult.BusinessDataResult != null)
            {
                var businessData = testResult.BusinessDataResult;
                Console.WriteLine($"\n数据统计:");
                Console.WriteLine($"- 总记录数: {businessData.BusinessData.Count}");
                Console.WriteLine($"- 数据关联率: {businessData.QualityReport.MatchRate}%");
                Console.WriteLine($"- 平均直落率: {businessData.DirectAnalysis.Average(x => x.DirectRate):F1}%");
                Console.WriteLine($"- 总营业额: ¥{businessData.BusinessData.Sum(x => x.TotalAmount):N2}");
                
                Console.WriteLine($"\n渠道分布:");
                foreach (var channel in businessData.ChannelAnalysis.OrderByDescending(x => x.TotalAmount))
                {
                    Console.WriteLine($"- {channel.ChannelType}: {channel.OrderCount}单, ¥{channel.TotalAmount:N2} ({channel.Percentage:F1}%)");
                }
            }
            
            Console.WriteLine("========================================");
        }

        /// <summary>
        /// 程序入口点
        /// </summary>
        public static async Task Main(string[] args)
        {
            var testProgram = new KtvBusinessDataTestProgram();
            var result = await testProgram.ExecuteFullTestAsync();
            
            Console.WriteLine($"\n测试完成，最终结果: {result.OverallResult}");
            if (!string.IsNullOrEmpty(result.ErrorMessage))
            {
                Console.WriteLine($"错误信息: {result.ErrorMessage}");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }

    #region 数据模型定义

    /// <summary>
    /// 测试结果模型
    /// </summary>
    public class TestResult
    {
        public string TestName { get; set; }
        public DateTime TestStartTime { get; set; }
        public DateTime TestEndTime { get; set; }
        public string TestPeriod { get; set; }
        public int ShopId { get; set; }
        public int ExecutionTimeMs { get; set; }
        public string OverallResult { get; set; }
        public string ErrorMessage { get; set; }
        public ConnectionTestResult ConnectionTestResult { get; set; }
        public BusinessDataResult BusinessDataResult { get; set; }
    }

    /// <summary>
    /// 连接测试结果
    /// </summary>
    public class ConnectionTestResult
    {
        public bool IsSuccess { get; set; }
        public bool BasicConnectionTest { get; set; }
        public bool LinkedServerTest { get; set; }
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// 业务数据结果
    /// </summary>
    public class BusinessDataResult
    {
        public List<BusinessDataModel> BusinessData { get; set; } = new List<BusinessDataModel>();
        public List<DirectAnalysisModel> DirectAnalysis { get; set; } = new List<DirectAnalysisModel>();
        public List<ChannelAnalysisModel> ChannelAnalysis { get; set; } = new List<ChannelAnalysisModel>();
        public List<TimeSlotAnalysisModel> TimeSlotAnalysis { get; set; } = new List<TimeSlotAnalysisModel>();
        public QualityReportModel QualityReport { get; set; } = new QualityReportModel();
    }

    /// <summary>
    /// 基础营业数据模型
    /// </summary>
    public class BusinessDataModel
    {
        public DateTime TestDate { get; set; }
        public string InvNo { get; set; }
        public int ShopId { get; set; }
        public string RmNo { get; set; }
        public DateTime? OpenTime { get; set; }
        public string CustName { get; set; }
        public int Numbers { get; set; }
        public decimal CashAmount { get; set; }
        public decimal VesaAmount { get; set; }
        public decimal WXPayAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public string PaymentMethod { get; set; }
        public bool IsDirect { get; set; }
        public string ChannelType { get; set; }
    }

    /// <summary>
    /// 直落分析模型
    /// </summary>
    public class DirectAnalysisModel
    {
        public DateTime TestDate { get; set; }
        public int TotalOrders { get; set; }
        public int DirectOrders { get; set; }
        public int NonDirectOrders { get; set; }
        public decimal DirectRate { get; set; }
        public decimal DirectAmount { get; set; }
        public decimal NonDirectAmount { get; set; }
    }

    /// <summary>
    /// 渠道分析模型
    /// </summary>
    public class ChannelAnalysisModel
    {
        public string ChannelType { get; set; }
        public int OrderCount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AvgAmount { get; set; }
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// 时段分析模型
    /// </summary>
    public class TimeSlotAnalysisModel
    {
        public string TimeSlot { get; set; }
        public int OrderCount { get; set; }
        public decimal TotalAmount { get; set; }
        public int DirectCount { get; set; }
        public decimal AvgAmount { get; set; }
    }

    /// <summary>
    /// 数据质量报告模型
    /// </summary>
    public class QualityReportModel
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int ShopId { get; set; }
        public int OpenRecordCount { get; set; }
        public int CloseRecordCount { get; set; }
        public decimal MatchRate { get; set; }
        public int ExecutionTimeMs { get; set; }
        public string Status { get; set; }
    }

    #endregion
}
