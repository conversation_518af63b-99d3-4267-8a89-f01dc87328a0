﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.Coupons.Model;
using Saas.Pos.Model.GrouponBase;
using Saas.Pos.Model.GrouponBase.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.GrouponBase
{
    public partial interface INGrouponCodeInfoRepository : IRepositoryBase<NGrouponCodeInfo>
    {
        List<CutomCouponInfo> GetGrouponDetail(GetGrouponInfoContext context);

        List<CutomCouponInfo> GetGrouponDetailByDistributeId(int month, Guid distributeId);

        /// <summary>
        /// 获取待核销卡券数据
        /// </summary>
        /// <returns></returns>
        NGrouponCodeInfo GetPreGrouponCode(int month, Guid distributeId);

        /// <summary>
        /// 获取指点卡券套餐核销卡券
        /// </summary>
        /// <param name="distributeId"></param>
        /// <returns></returns>
        List<GetCustomUsedCouponModel> GetUsedCustomCoupon(Guid distributeId);

        /// <summary>
        /// 派发卡券
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        int SendCodeInfo(SendCodeInfoContext context);
    }
}
