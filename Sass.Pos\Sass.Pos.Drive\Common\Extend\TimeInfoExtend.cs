﻿using Saas.Pos.Model.Rms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Extend
{
    public static class TimeInfoExtend
    {
        public static timeinfo GetFirst(this List<timeinfo> timeinfos, string timeNo)
        {
            return timeinfos.FirstOrDefault(w => w.TimeNo == timeNo);
        }

        /// <summary>
        /// 检测传入的时段是否超过当前时间
        /// </summary>
        /// <param name="timeinfos">源数据</param>
        /// <param name="timeNo">时段ID</param>
        /// <param name="totalMinte">总分钟数</param>
        /// <returns></returns>
        public static bool DetectionTimeOut(this string timeNo, List<timeinfo> timeinfos, int totalMinte = 0)
        {
            var timeInfo = timeinfos.GetFirst(timeNo);
            //没有找到就直接显示超时
            if (timeInfo == null || string.IsNullOrEmpty(timeInfo.TimeName))
                return true;

            var timeLines = timeInfo.TimeName.Split('-');
            //如果只有一个，就说明是直接到第二天早上六点的，不会超时
            if (timeLines.Length == 1 || timeLines[1].ToLower() == "end")
                return false;

            var date = DateTime.Now.ToString("yyyy-MM-dd") + " " + timeLines[0];

            var compareTime = new DateTime();
            if (!DateTime.TryParse(date, out compareTime))
                return true;
            //超过一半时间，就提示超时
            if (totalMinte > 0)
                compareTime = compareTime.AddMinutes(totalMinte / 2);
            else
            {
                //没有传入分钟数就按照结束时间来比较
                if (!DateTime.TryParse(DateTime.Now.ToString("yyyy-MM-dd") + " " + timeLines[1], out compareTime))
                    return true;
            }

            if (compareTime < DateTime.Now)
                return true;

            return false;
        }

        /// <summary>
        /// 判断传入的时段是否在当前时间内
        /// </summary>
        /// <param name="timeNo"></param>
        /// <param name="timeinfos"></param>
        /// <returns></returns>
        public static bool DetectionTimeIn(this string timeNo, List<timeinfo> timeinfos)
        {
            var timeInfo = timeinfos.GetFirst(timeNo);
            //没有找到就直接显示超时
            if (timeInfo == null || string.IsNullOrEmpty(timeInfo.TimeName))
                return false;

            var timeLines = timeInfo.TimeName.Split('-');
            //如果只有一个，就说明是直接到第二天早上六点的，不会超时
            if (timeLines.Length == 1 || timeLines[1].ToLower() == "end")
                return true;

            var date = DateTime.Now.ToString("yyyy-MM-dd") + " " + timeLines[0];
            var startTime = new DateTime();
            var endTime = new DateTime();
            if (!DateTime.TryParse(date, out startTime))
                return false;

            //没有传入分钟数就按照结束时间来比较
            if (!DateTime.TryParse(DateTime.Now.ToString("yyyy-MM-dd") + " " + timeLines[1], out endTime))
                return false;

            if (startTime <= DateTime.Now && endTime >= DateTime.Now)
                return true;
            else
                return false;
        }

        #region =======直落处理相关方法=======

        /// <summary>
        /// 根据传入的一个时段，获取完整的可直落时间段
        /// </summary>
        /// <param name="timeinfo">需要计算的时段数据</param>
        /// <param name="shoptimeinfos">所有门店时段信息</param>
        /// <param name="timeinfos">所有时段数据</param>
        /// <param name="shopId">所属门店</param>
        /// <param name="currentDate">计算日期</param>
        /// <returns></returns>
        public static List<string> GetTimeNos(this timeinfo timeinfo, List<shoptimeinfo> shoptimeinfos, List<timeinfo> timeinfos, int shopId, DateTime currentDate)
        {
            var shopTimeInfo = shoptimeinfos.GetFirst(shopId, timeinfo.TimeNo);
            if (shopTimeInfo == null)
                return new List<string>();

            string number = GetWeekNumber(currentDate);
            var shopTimeList = shoptimeinfos.Where(w => w.ShopId == shopId && w.TimeType.ToString().Contains(shopTimeInfo.TimeType.ToString()) && (w.DayType == 7 || w.DayType.ToString().Contains(number))).ToList();

            var resultData = (from shoptime in shopTimeList
                              join time in timeinfos on shoptime.TimeNo equals time.TimeNo
                              orderby time.BegTime
                              select shoptime.TimeNo).ToList();

            return resultData;
        }

        /// <summary>
        /// 根据传入的时段计算出跨越了多少时段
        /// </summary>
        /// <param name="timeinfo">开始时段数据</param>
        /// <param name="shoptimeinfos">所有门店时段数据</param>
        /// <param name="timeinfos">所有时段信息</param>
        /// <param name="shopId">所属门店</param>
        /// <param name="endTimeNo">结束时段</param>
        /// <param name="currentDate">计算日期</param>
        /// <returns></returns>
        public static List<string> GetCrossTimeNos(this timeinfo timeinfo, List<shoptimeinfo> shoptimeinfos, List<timeinfo> timeinfos, int shopId, string endTimeNo, DateTime currentDate)
        {
            var shopTimeInfo = shoptimeinfos.GetFirst(shopId, timeinfo.TimeNo);
            if (shopTimeInfo == null)
                return new List<string>();

            string number = GetWeekNumber(currentDate);

            var shopTimeList = shoptimeinfos.Where(w => w.ShopId == shopId && w.TimeType.ToString().Contains(shopTimeInfo.TimeType.ToString()) && (w.DayType == 7 || w.DayType.ToString().Contains(number))).ToList();

            var shopTimes = (from shoptime in shopTimeList
                             join time in timeinfos on shoptime.TimeNo equals time.TimeNo
                             orderby time.BegTime
                             select new
                             {
                                 TimeNo = shoptime.TimeNo,
                                 BeginTime = time.BegTime,
                                 EndTime = time.EndTime,
                                 TimeName = time.TimeName
                             }).ToList();

            var beginShopTime = shopTimes.FirstOrDefault(w => w.TimeNo == timeinfo.TimeNo);
            var endShopTime = shopTimes.FirstOrDefault(w => w.TimeNo == endTimeNo);

            var resultData = new List<string>();
            shopTimes.ForEach(x =>
            {
                if (beginShopTime != null && endShopTime != null)
                {
                    if (x.BeginTime >= beginShopTime.EndTime)
                    {
                        if (x.BeginTime <= endShopTime.BeginTime)
                            resultData.Add(x.TimeNo);
                        //处理特殊时间段，也需要返回出去
                        //处理类似于20:10-6:00这种数据
                        //这种数据需要把房型数据在时间段内减掉
                    }
                }
            });
            //将开始时段加入到列表中，最后一条已经在列表中
            if (beginShopTime != null)
                resultData.Insert(0, beginShopTime.TimeNo);

            return resultData;
        }

        /// <summary>
        /// 获取当前日期所属星期的数字
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public static string GetWeekNumber(DateTime date)
        {
            int count = 0;
            if (date.DayOfWeek.ToString() == "Monday")
                count = 1;
            else if (date.DayOfWeek.ToString() == "Tuesday")
                count = 2;
            else if (date.DayOfWeek.ToString() == "Wednesday")
                count = 3;
            else if (date.DayOfWeek.ToString() == "Thursday")
                count = 4;
            else if (date.DayOfWeek.ToString() == "Friday")
                count = 5;
            else if (date.DayOfWeek.ToString() == "Saturday")
                count = 6;

            return count.ToString();
        }

        #endregion
    }
}
