﻿using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.SaasPos
{
    public partial class Shop_TimeRtInfoDepositApp : AppBase<Shop_TimeRtInfoDeposit>
    {
        public List<GetShopTimeRtInfoDepositListModel> GetDepositList(GetDepositListContext context)
        { 
            return Repository.Shop_TimeRtInfoDeposit.GetDepositList(context);
        }
    }
}
