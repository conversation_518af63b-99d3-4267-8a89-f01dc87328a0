﻿using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
    public partial class SceneRole_ConfigApp : AppBase<SceneRole_Config>
    {
        public GetSceneRoleConfigModel GetSceneConfig(GetSceneRoleConfigContext context)
        {
            return Repository.SceneRole_Config.GetSceneConfig(context);
        }

        public List<GetSceneRoleConfigExModel> GetSceneRoleConfigData(GetSceneRoleConfigDataContext context)
        {
            return Repository.SceneRole_Config.GetSceneRoleConfigData(context);
        }
    }
}
