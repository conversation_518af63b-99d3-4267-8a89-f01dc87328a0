﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Domain.IService.Rms;
using Saas.Pos.Model.Rms;
using Saas.Pos.Model.Rms.Context;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService
{
    [ServiceContract]
    public interface IRmsService : IRmsReportService, IRmsCustomerService, IRmsBookingService, IRmsOpeningService
    {
        [OperationContract]
        ResponseContext<ReturnBool> SaveRmRemark(SaveRmRemarkContext context);

        [OperationContract]
        ResponseContext<List<RmOperation>> GetRmOperation(GetRmRemarkContext context);
    }
}
