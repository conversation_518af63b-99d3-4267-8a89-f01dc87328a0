﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.Validation;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Log
{
    public class LogHelper
    {
        private static object lockObj = new object();
        private static object errorLock = new object();
        private static object taskInfoObj = new object();
        private readonly static string _logDir = AppDomain.CurrentDomain.BaseDirectory + "\\Log";
        private readonly static string _taskLogDir = AppDomain.CurrentDomain.BaseDirectory + "\\TaskLog";
        public static void Info(string message)
        {
            string filePath = _logDir + "\\log_info" + DateTime.Now.ToString("yyyy-MM-dd") + ".txt";
            CreateLogDir(_logDir);
            CreateLogFile(filePath);
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.AppendLine("请求时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            stringBuilder.AppendLine("异常信息：" + message);
            stringBuilder.AppendLine();
            lock (lockObj)
            {
                File.AppendAllLines(filePath, new string[]
                {
                    stringBuilder.ToString()
                });
            }
        }

        /// <summary>
        /// 记录错误信息，与正常的打印日志分流，不然会导致正常日志很难排查
        /// </summary>
        /// <param name="message"></param>
        public static void Error(string message) 
        {
            string filePath = _logDir + "\\log_error" + DateTime.Now.ToString("yyyy-MM-dd") + ".txt";
            CreateLogDir(_logDir);
            CreateLogFile(filePath);
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.AppendLine("记录时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            stringBuilder.AppendLine("异常信息：" + message);
            stringBuilder.AppendLine();
            lock (errorLock)
            {
                File.AppendAllLines(filePath, new string[]
                {
                    stringBuilder.ToString()
                });
            }
        }

        /// <summary>
        /// 定时任务专用日志记录方法
        /// </summary>
        /// <param name="taskName">任务名称</param>
        /// <param name="message">日志内容</param>
        /// <param name="type">日志类型1：正常2：错误</param>
        public static void TaskInfo(string taskName, string message, int type = 1)
        {
            string messsageType = "Info";
            if (type != 1)
                messsageType = "Error";

            string filePath = _taskLogDir + "\\log_info" + DateTime.Now.ToString("yyyy-MM-dd") + ".txt";
            CreateLogDir(_taskLogDir);
            CreateLogFile(filePath);
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.AppendLine("请求时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            stringBuilder.AppendLine("任务名称：" + taskName);
            stringBuilder.AppendLine("日志类型：" + messsageType);
            stringBuilder.AppendLine("日志信息：" + message);
            stringBuilder.AppendLine();
            lock (taskInfoObj)
            {
                File.AppendAllLines(filePath, new string[]
                {
                    stringBuilder.ToString()
                });
            }
        }

        public static void Info(Exception ex)
        {
            string message = string.Empty;
            if (ex is DbUpdateException)
            {
                var exception = (DbUpdateException)ex;
                message += exception.InnerException == null ? string.Empty : exception.InnerException.Message;
                //记录相关信息到日志
                Error(message);
                throw new Exception("更新数据失败，失败原因：" + ex.Message);
            }
            else if (ex is DbUpdateConcurrencyException)
            {
                var exception = (DbUpdateConcurrencyException)ex;
                message += exception.InnerException == null ? string.Empty : exception.InnerException.Message;
                //记录相关信息到日志
                Error(message);
                throw new Exception("更新数据失败，失败原因：" + ex.Message);
            }
            else if (ex is DbEntityValidationException)
            {
                var exception = (DbEntityValidationException)ex;
                exception.EntityValidationErrors.ToList().ForEach(x =>
                {
                    x.ValidationErrors.ToList().ForEach(w =>
                    {
                        message += "错误字段：" + w.PropertyName + "；错误原因：" + w.ErrorMessage;
                    });
                });
                Error(message);
                throw new Exception("数据校验未通过！");
            }
            else if (ex is ObjectDisposedException)
            {
                var exception = (ObjectDisposedException)ex;
                Error(message);
                throw new Exception("数据库未连接，无法获取数据！");
            }
            else if (ex is SqlException)
            {
                message += ex.InnerException == null ? string.Empty : ex.InnerException.Message;
                Error(message);
                throw new Exception(ex.Message);
            }
            else 
            {
                message += ex.InnerException == null ? string.Empty : ex.InnerException.Message;
                Error(message);
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 创建文件夹
        /// </summary>
        private static void CreateLogDir(string logDir)
        {
            if (!Directory.Exists(logDir))
                Directory.CreateDirectory(logDir);
        }

        /// <summary>
        /// 创建文件夹里面的日志文件（一天创建一个）
        /// </summary>
        /// <param name="strlogFile">文件名称和地址</param>
        private static void CreateLogFile(string strlogFile)
        {
            if (!File.Exists(strlogFile))
                File.Create(strlogFile).Dispose();
        }

        public static void Info(object p)
        {
            throw new NotImplementedException();
        }
    }
}
