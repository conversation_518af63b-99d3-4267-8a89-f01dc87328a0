<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子账单</title>
    <script src="http://hd.tang-hui.com.cn:800/Content/js/jquery/jquery-2.1.1.min.js"></script>
    <script src="./qrcode.js/qrcode.min.js"></script>

    <style>
        #app {
            width: 60%;
            /* height: 900px; */
            /* background-color: green; */
            display: flex;
            margin: 10px auto;
            flex-direction: column;
            gap: 20px;
            /* padding-bottom: 50px; */
            font-size: 25px;

        }

        .partOne {
            text-align: center;
        }

        .title {
            font-size: 50px;
            height: 40px;
            line-height: 40px;
        }

        #ecode {
            width: 200px;
            height: 200px;
            margin: 5px auto
        }

        .bigSize {
            font-size: 50px;
        }

        .line {
            width: 100%;
            height: 2px;
            border-top: 1px dashed black;
            /* 上边框为1像素宽的黑色虚线 */
            border-bottom: 1px dashed black;
            /* 下边框为1像素宽的黑色虚线 */
        }

        .partTwo {
            padding-left: 20px
        }

        .partThree {
            padding-left: 20px
        }

        .blodLine {
            width: 100%;
            height: 1px;
            border-top: 2px dashed black;
            /* 上边框为1像素宽的黑色虚线 */
            border-bottom: 2px dashed black;
            /* 下边框为1像素宽的黑色虚线 */
        }


        table {
            border-collapse: collapse;
            width: 100%;
            text-align: center;
        }

        .hasLine {
            border-top: 1px solid black;
        }

        .partFour {
            padding-left: 18px;
        }

        .last {
            margin-left: 40px;
        }
    </style>
</head>

<body>
    <!-- 页面内容 -->
    <div id="app">
        <div class="partOne">
            <div class="title" id="storeName"></div>
            <div>预约专线：020 8-3333-999(24H~40条线)</div>
            <div id="ecode"></div>
            <div id="money">￥</div>
            <div>扫描二维码，使用微信支付享充值优惠</div>
        </div>
        <div class="line"></div>
        <div class="partTwo">
            <div>房间号码：<span id="RmNo" class="bigSize"></span></div>
            <div id="invno">账单号码：</div>
            <div id="InDate">开房日期：</div>
            <div id="InTime">开房时间：</div>
            <div id="Memberno">会员卡号：</div>
            <div id="AccUserName">结账员工：</div>
            <div id="PrintDate">打印时间：</div>
        </div>

        <div class="line"></div>
        <div id="timeList">
        </div>
        <div class="blodLine"></div>
        <table>
            <tr>
                <th>名称</th>
                <th>单价</th>
                <th>数量</th>
                <th>金额</th>
                <th>时间</th>
                <th>经办人</th>
            </tr>
        </table>
        <div class="line"></div>
        <div class="partFour">
            <div id="FdCost">落单金额：￥</div>
            <div id="RmCost">包房费：￥</div>
            <div id="Serv">消耗品费：￥</div>
            <div id="disc">折扣：￥</div>
            <div id="FixedDisc">固定折扣：￥</div>
            <div id="Tot">消费总金额：￥</div>
            <div id="ReturnAccount">返还支付：￥</div>
            <div id="RechargeAccount">充值支付：￥</div>
            <div id="WechatDeposit">已支付：￥</div>
        </div>
        <div class="bigSize last" id="YFPrice">应付金额：￥</div>
    </div>

    <script>
        // $(document).ready(function () {
            // http://*************:1031/ebill/bill.html?storeName="测试"&RmNo=002&ShopId=9&InvNo=''
            // 接口获取详情

            //  --------------------测试写的数据--------------------------
            // var detailData = [
            //     {
            //         CashName: "Z钟海泉",
            //         CashTime: "11:40",
            //         CashTypeStr: "常规下单",
            //         FdName: "咪套",
            //         FdPrice: "0",
            //         FdPriceBeforeDisc: "0",
            //         FdQty: "2"
            //     },
            //     {
            //         CashName: "Z钟海泉",
            //         CashTime: "11:40",
            //         CashTypeStr: "常规下单",
            //         FdName: "咪套",
            //         FdPrice: "0",
            //         FdPriceBeforeDisc: "0",
            //         FdQty: "2"
            //     },
            //     {
            //         CashName: "Z钟海泉",
            //         CashTime: "11:40",
            //         CashTypeStr: "常规下单",
            //         FdName: "咪套",
            //         FdPrice: "0",
            //         FdPriceBeforeDisc: "0",
            //         FdQty: "2"
            //     }
            // ]
            // var headData = {
            //     AccUserName: "Z钟海泉",
            //     FdCost: "720",
            //     FixedDisc: "0",
            //     InDate: "********",
            //     InTime: "20:30",
            //     Memberno: "*********",
            //     Invno: "A01845796",
            //     PrintDate: "2024-03-13 11:52:55",
            //     RechargeAccount: "0",
            //     ReturnAccount: "0",
            //     RmCost: "0",
            //     Rmno: "002",
            //     Serv: "21",
            //     TimeSeg: [
            //         {
            //             FromTime: "00:00",
            //             ToTime: "01:00",
            //             RmCost: "100"
            //         },
            //         {
            //             FromTime: "00:00",
            //             ToTime: "01:00",
            //             RmCost: "100"
            //         },
            //         {
            //             FromTime: "00:00",
            //             ToTime: "01:00",
            //             RmCost: "100"
            //         }
            //     ],
            //     Tot: "741",
            //     WechatDeposit: "0",
            //     YFPrice: "741",
            //     disc: "0"
            // }
            // setData(headData, detailData,)
            //  ------------------------------------------------------------
            // 示例：获取账单数据并展示
           
        // });

        getBillData();
        // 需要的链接http://127.0.0.1:5500/ebill/bill.html?storeName='fzh'&RmNo=002&ShopId=9&InvNo=''
        function getBillData() {
            // 使用URLSearchParams处理URL查询参数
            const url = window.location.href;
            console.log("url", url);
            const searchParams = new URLSearchParams(new URL(url).search);
            const newParams = {}
            for (const [key, value] of searchParams) {
                console.log(key, value);
                newParams[key] = value
            }
            $("#storeName").append(newParams.storeName);
            $("#RmNo").text(newParams.RmNo);
            const params = JSON.stringify({
                RmNo: newParams.RmNo,
                ShopId: newParams.ShopId,
                InvNo: newParams.InvNo
            })
            $.ajax({
                url: 'http://saasapi.tang-hui.com.cn:7801/FinancePos/Service/Rpc',
                async: false,
                method: 'POST',
                data: {
                    Action: "FdGetRmBillData",
                    Context: params
                },
                success: function (res) {
                    if (res.state === 1) {
                        console.log("请求数据成功");
                        var detailData = res.data.DetailData;
                        var headData = res.data.HeadData;
                        setData(headData, detailData,)
                    } else {
                        console.log("失败");
                    }
                },
                error: function (error) {
                    console.error(error);
                    console.log("接口报错");
                }
            });
        }

        function setData(headData, detailData) {
            getQRcode(headData.Invno);
            $("#money").append(headData.YFPrice);
            $("#invno").append(headData.Invno);
            $("#InDate").append(headData.InDate);
            $("#InTime").append(headData.InTime);
            $("#Memberno").append(headData.Memberno);
            $("#AccUserName").append(headData.JZData.AccUserName);
            $("#PrintDate").append(headData.PrintDate);
            // table以下
            $("#FdCost").append(headData.JZData.FdCost);
            $("#RmCost").append(headData.JZData.RmCost);
            $("#Serv").append(headData.JZData.Serv + "（3%）");
            $("#disc").append(headData.JZData.disc);
            $("#FixedDisc").append(headData.JZData.FixedDisc);
            $("#Tot").append(headData.JZData.Tot);
            $("#ReturnAccount").append(headData.JZData.ReturnAccount);
            $("#RechargeAccount").append(headData.JZData.RechargeAccount);
            $("#WechatDeposit").append(headData.JZData.WechatDeposit);
            $("#YFPrice").append(headData.JZData.YFPrice);
            // 遍历数据并动态创建元素
            $.each(headData.TimeSeg, function (index, item) {
                var div = $("<div>").addClass("partThree");
                div.append($("<div>").text("时间段：" + item.FromTime + "-" + item.ToTime + " 房费金额：" + item.RmCost));
                $("#timeList").append(div);
            });

            // 遍历数据并动态创建表格行
            $.each(detailData, function (index, item) {
                var tr = $("<tr>").addClass("hasLine");
                tr.append($("<td>").text(item.FdName));
                tr.append($("<td>").text(item.FdPrice));
                tr.append($("<td>").text(item.FdQty));
                tr.append($("<td>").text(item.FdPriceBeforeDisc));
                tr.append($("<td>").text(item.CashTime));
                tr.append($("<td>").text(item.CashName));
                $("table").append(tr);
            });

        }

        function getQRcode(invno) {
                var qrCodeContainer = $("#ecode")[0];
                var qr = new QRCode(qrCodeContainer, {
                    text: invno,
                    width: 200,
                    height: 200
                });
            }
    </script>
</body>

</html>