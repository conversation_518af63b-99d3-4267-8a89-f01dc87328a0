﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.DbFood
{
    public partial interface IInv_TimeSectionRepository : IRepositoryBase<Inv_TimeSection>
    {
        List<TimeSegment> GetTimeSegment(string invNo);
    }
}
