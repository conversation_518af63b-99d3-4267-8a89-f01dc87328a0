﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.StoreManage
{
    /// <summary>
    /// 门店营业时间须知
    /// </summary>
    public interface IStoreManageBTimeNotice
    {
        /// <summary>
        /// 获取门店营业时间须知
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        List<GetBTimeNoticeDataModel> GetBTimeNoticeData(GetBTimeId context);

        /// <summary>
        /// 删除门店营业时间须知(软删除)
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<ReturnInt> DeleteBTimeData(DeleteBTimeDataContext context);

        ResponseContext<List<GetBTimeNoticeDataExModel>> GetBTimeNoticeExData(GetBTimeIdEx context);

        /// <summary>
        /// 新增门店营业时间须知
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<ReturnInt> InsertBTimeData(InsertBTimeDataContext context);

        /// <summary>
        /// 修改门店营业时间须知
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<ReturnInt> EditBTimeData(EditBTimeDataContext context);
    }
}
