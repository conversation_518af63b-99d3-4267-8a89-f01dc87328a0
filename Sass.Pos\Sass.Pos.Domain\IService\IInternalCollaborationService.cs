﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Collaboration.Context;
using Saas.Pos.Model.Collaboration.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService
{
    [ServiceContract]
    public interface IInternalCollaborationService
    {
        [OperationContract]
        ResponseContext<List<GetRecordItemModel>> GetLatest(GetRecordItemContext context);

        [OperationContract]
        ResponseContext<int> CreateCategory(CreateCategoryContext context);

        [OperationContract]
        ResponseContext<int> UpdateCategory(UpdateCategoryContext context);

        [OperationContract]
        ResponseContext<int> DeleteCategory(DeleteCategoryContext context);

        [OperationContract]
        ResponseContext<List<Model.SaasPos.Energy_Metering_Category>> GetCateList(GetCategoryListContext context);

        [OperationContract]
        ResponseContext<int> CreateItem(CreateCategoryItemContext context);

        [OperationContract]
        ResponseContext<int> UpdateItem(UpdateCategoryItemContext context);

        [OperationContract]
        ResponseContext<int> DeleteItem(DeleteCategoryItemContext context);

        [OperationContract]
        ResponseContext<List<CategoryItemListModel>> GetItemList(GetCategoryItemListContext context);

        [OperationContract]
        ResponseContext<int> CreateRecord(CreateRecordContext context);

        [OperationContract]
        ResponseContext<List<GetRecordItemListModel>> GetRecordItemList(GetRecordItemListContext context);
    }
}
