﻿using external.open.library.TakeaWayPlatform.MeiTuan;
using external.open.library.TakeaWayPlatform.TikTok;
using Saas.Pos.Model.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace external.open.library.TakeaWayPlatform
{
    public class LocalLifeFactory
    {
        private static Dictionary<LocalLifePlatformEnum, LocalLifeBase> pairs = new Dictionary<LocalLifePlatformEnum, LocalLifeBase>();

        /// <summary>
        /// 初始化数据
        /// </summary>
        /// <param name="localLife"></param>
        /// <returns></returns>
        public static LocalLifeBase CreateInstance(LocalLifePlatformEnum localLifeEnum)
        {
            if (!pairs.ContainsKey(localLifeEnum))
            {
                LocalLifeBase localLife = null;
                switch (localLifeEnum)
                {
                    case LocalLifePlatformEnum.Meituan:
                        localLife = new DeliveryMeituan();
                        break;
                    case LocalLifePlatformEnum.Tiktok:
                        localLife = new DeliveryTiktok();
                        break;
                    default:
                        throw new Exception("未对接该平台！");
                }

                if (localLife == null)
                    throw new Exception("初始化失败！");

                pairs.Add(localLifeEnum, localLife);
                return localLife;
            }
            else
                return pairs[localLifeEnum];
        }
    }
}
