﻿using Castle.DynamicProxy;
using Saas.Pos.Common.Attributes.ValidationFilter;
using Saas.Pos.Common.Attributes.ValidationFilter.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Attributes.Filter
{
    /// <summary>
    /// 公用拦截器
    /// </summary>
    public class GeneralInterceptor : StandardInterceptor
    {
        /// <summary>
        /// 调用前拦截
        /// </summary>
        /// <param name="invocation"></param>
        protected override void PreProceed(IInvocation invocation)
        {
            //先鉴权，再做参数校验
            var attributes = invocation.Method.GetCustomAttributes(typeof(AuthorizationAttribute), true);
            foreach (var item in attributes)
            {
                ((AuthorizationAttribute)item).ActionAuthorize(invocation.Arguments);
            }

            //参数拦截
            var arguments = invocation.Arguments;
            foreach (var item in arguments)
            {
                var type = item.GetType();
                foreach (var property in type.GetProperties())
                {
                    foreach (var attributeItem in property.GetCustomAttributes(typeof(ValidationFilterAttribute), true))
                    {
                        ((ValidationFilterAttribute)attributeItem).Inspect(property.GetValue(item, null), new ValidationContext()
                        {
                            PropertyName = property.Name,
                            ObjectType = type,
                            ObjectInstance = item
                        });
                    }
                }
            }

            base.PerformProceed(invocation);
            //Console.WriteLine("调用前拦截,调用方法：" + invocation.Method.Name);
        }
        /// <summary>
        /// 拦截的方法返回时调用
        /// </summary>
        /// <param name="invocation"></param>
        protected override void PerformProceed(IInvocation invocation)
        {
            //方法拦截
            var attributes = invocation.Method.GetCustomAttributes(typeof(FilterAttribute), true);
            foreach (var item in attributes)
            {
                ((FilterAttribute)item).Action();
            }

            //Console.WriteLine("调用方法返回时拦截,调用方法：" + invocation.Method.Name);

            //base.PerformProceed(invocation);
            //base.PostProceed(invocation);
        }
        /// <summary>
        /// 调用后拦截
        /// </summary>
        /// <param name="invocation"></param>
        protected override void PostProceed(IInvocation invocation)
        {
            //Console.WriteLine("调用后拦截,调用方法：" + invocation.Method.Name);
            base.PreProceed(invocation);
        }
    }
}
