﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    public interface IConsumeBillService
    {
        [OperationContract]
        ResponseContext<RespPaginationModel<GetConsumeBillDataModel>> GetConsumeBillDataEx(GetConsumeBillDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetConsumeBillDataModel>> GetConsumeBillData(GetConsumeBillDataContext context);

        [OperationContract]
        ResponseContext<ExcelConsumeBillDataExModel> ExcelConsumeBillData(ExcelConsumeBillDataContext context);

        [OperationContract]
        ResponseContext<ExcelConsumeBillDataModel> ExcelConsumeBillDataEx(ExcelConsumeBillDataExContext context);

        [OperationContract]
        ResponseContext<RoomCloseLabelRecordDataModel> RmCloseLabelRecordData(RmCloseLabelRecordDataContext context);

        [OperationContract]
        ResponseContext<RoomCloseLabelRecordDataModel> RmCloseLabelRecordListData(RmCloseLabelRecordDataListContext context);

        [OperationContract]
        ResponseContext<List<GetConsumeBillInvNoDataModel>> GetConsumeBillInvNoData(GetConsumeBillInvNoDataContext context);

        [OperationContract]
        ResponseContext<RoomCloseLabelRecordDataModel> RoomCloseLabelRecordData(RoomCloseLabelRecordDataContext context);
    }
}
