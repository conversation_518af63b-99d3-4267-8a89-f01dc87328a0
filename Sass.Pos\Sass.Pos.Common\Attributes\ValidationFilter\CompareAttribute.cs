﻿using Saas.Pos.Common.Attributes.ValidationFilter.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Attributes.ValidationFilter
{
    /// <summary>
    /// 与指定属性的值做相同对比
    /// </summary>
    public class CompareAttribute : ValidationFilterAttribute
    {
        public string OtherProperty { get; private set; }

        public CompareAttribute(string otherProperty)
        {
            OtherProperty = otherProperty;
        }

        public override void Inspect(object currentValue, ValidationContext context)
        {
            var property = context.ObjectType.GetProperty(OtherProperty);
            if (property == null)
                throw new Exception(string.Format("{0}输入错误，未找到该属性！", OtherProperty));

            object value2 = property.GetValue(context.ObjectInstance, null);

            if (!Equals(currentValue, value2))
                throw new Exception(string.IsNullOrEmpty(ErrorMessage) ? string.Format(AppInterface.dat.lang["CompareMsg"], context.PropertyName, OtherProperty) : ErrorMessage);
        }
    }
}
