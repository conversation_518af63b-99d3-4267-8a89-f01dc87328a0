﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Rms.Context;
using Saas.Pos.Model.Rms.Model;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService
{
    [ServiceContract]
    public interface IStoreManageService
    {
        [OperationContract]
        ResponseContext<List<GetStoreListModel>> GetShopList(GetStoreListContext context);

        [OperationContract]
        ResponseContext<List<KeyValueCommonModel>> GetShopDropList(GetStoreListContext context);


        [OperationContract]
        ResponseContext<int> AddStoreMap(AddStoreMapContext context);

        [OperationContract]
        ResponseContext<int> UpdateStoreMap(UpadateStoreMapContext context);

        [OperationContract]
        ResponseContext<int> DeleteStoreMap(DeleteStoreMapContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetStoreMapModel>> GetStoreMapList(GetStoreMapListContext context);

        [OperationContract]
        ResponseContext<GetStoreMapModel> GetStoreMap(GetStoreMapContext context);

        [OperationContract]
        ResponseContext<int> AddFoodMap(AddFoodMapContext context);

        [OperationContract]
        ResponseContext<int> UpdateFoodMap(UpdateFoodMapContext context);

        [OperationContract]
        ResponseContext<int> DeleteFoodMap(DeleteFoodMapContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetFoodMapModel>> GetFoodMapList(GetFoodMapListContext context);

        [OperationContract]
        ResponseContext<GetFoodMapModel> GetFoodMap(GetFoodMapContext context);

        /// <summary>
        /// 获取门店管理数据
        /// </summary>
        [OperationContract]
        ResponseContext<GetStoreManageModel> GetStoreManageDatas(GetStoreManageContext context);

        /// <summary>
        /// 保存门店管理信息
        /// </summary>
        [OperationContract]
        ResponseContext<ReturnInt> InsertStoreManageData(InsertStoreManageContext context);

        /// <summary>
        /// 添加门店管理营业时间
        /// </summary>
        [OperationContract]
        ResponseContext<ReturnInt> InsertBusinessTimeData(List<EditBusinessTimeContext> context);

        /// <summary>
        /// 删除门店管理信息(软删除)
        /// </summary>
        [OperationContract]
        ResponseContext<ReturnInt> DeleteStoreManageData(DeleteStoreManageContext context);

        /// <summary>
        /// 删除营业时间信息(软删除)
        /// </summary>
        [OperationContract]
        ResponseContext<ReturnInt> DeleteBusinessTimeData(DeleteBusinessTimeContext context);

        /// <summary>
        /// 变更店铺状态
        /// </summary>
        [OperationContract]
        ResponseContext<ReturnInt> EditBusinessState(EditBusinessStateContext context);

        /// <summary>
        /// 修改店铺信息
        /// </summary>
        [OperationContract]
        ResponseContext<ReturnInt> EditShopData(EditShopDataContext context);

        /// <summary>
        /// 修改营业时间信息
        /// </summary>
        [OperationContract]
        ResponseContext<ReturnInt> EditBusinessTimeData(EditBusinessByIdContext context);

        /// <summary>
        /// 查询基础服务功能
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<List<BasicService>> GetBasicService(GetStoreManageContext context);

        /// <summary>
        /// 删除基础服务功能(软删除)
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<ReturnInt> DeleteBasicService(DeleteBasicServiceContest context);

        /// <summary>
        /// 新增基础服务数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<ReturnInt> InsertBasicService(InsertBasicServiceContext context);

        /// <summary>
        /// 修改基础服务数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<ReturnInt> EditBasicService(EditBasicServiceContext context);

        /// <summary>
        /// 删除门店营业时间须知(软删除)
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<ReturnInt> DeleteBTimeData(DeleteBTimeDataContext context);

        [OperationContract]
        ResponseContext<List<GetBTimeNoticeDataExModel>> GetBTimeNoticeExData(GetBTimeIdEx context);

        /// <summary>
        /// 新增门店营业时间须知
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<ReturnInt> InsertBTimeData(InsertBTimeDataContext context);

        /// <summary>
        /// 修改门店营业时间须知
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<ReturnInt> EditBTimeData(EditBTimeDataContext context);

        [OperationContract]
        ResponseContext<List<GetManageStoreListModel>> GetManageStoreList(GetManageStoreListContext context);

        [OperationContract]
        ResponseContext<GetStoreManageModel> GetStoreManageExDatas(GetStoreManageContext context);

        [OperationContract]
        Tuple<int, string, bool> GetBtSpecialWeekTime(BeginEndBusinessTimeContext context);

        [OperationContract]
        BeginEndBusinessTime GetShopBeginEndTime(BeginEndBusinessTimeContext context);

        [OperationContract]
        ResponseContext<GetBookSysDataModel> GetBookSysData(GetBookShopContext context);
    }
}
