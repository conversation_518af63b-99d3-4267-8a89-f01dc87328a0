﻿using Saas.Pos.Model.Enum;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Application.Lib.SaasPos
{
    public partial class Commission_EmpRecordApp : AppBase<Commission_EmpRecord>
    {
        public List<GetOrderEmpRecordModel> GetOrderSchemesList(GetOrderSchemesContext context)
        {
            var data= Repository.Commission_EmpRecord.GetOrderSchemesList(context);
            data = data.Select(i => { i.OrderStateName = Enum.GetName(typeof(OrderStateEnum), i.OrderState); return i; }).ToList();
            return data;
        }

        public List<GetOrderEmpRecordModel> GetExcelEmpRecord(ExcelEmpRecordContext context)
        {
            var data = Repository.Commission_EmpRecord.GetExcelEmpRecord(context);
            data = data.Select(i => { i.OrderStateName = Enum.GetName(typeof(OrderStateEnum), i.OrderState); return i; }).ToList();
            return data;
        }
    }
}
