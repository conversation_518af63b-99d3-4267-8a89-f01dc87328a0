﻿using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.Report.Model;
using Saas.Pos.Model.Rms;
using Saas.Pos.Model.Rms.Context;
using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Appliction.Lib.Rms
{
    public partial class opencacheinfoApp : AppBase<opencacheinfo>
    {
        public List<OpenCacheNumberModel> GetOpenCacheList(GetBookCacheContext context)
        {
            return Repository.opencacheinfo.GetOpenCacheList(context);
        }

        public List<GetOnlineOpenDataModel> GetOnlineOpenData(GetOnlineIkeyContext context)
        {
            return Repository.opencacheinfo.GetOnlineOpenData(context);
        }

        public List<GetOpenRoomRecordsModel> GetOpenRoomRecords(GetOpenRoomRecordsContext context)
        {
            var data = Repository.opencacheinfo.GetOpenRoomRecords(context);
            var iKeys = data.Select(w => w.Ikey).ToList();

            var rmOperationList = Repository.RmOperation.IQueryable(w => iKeys.Contains(w.Ikey)).Select(w => new
            {
                w.Ikey,
                w.Remark,
                w.UserId,
                w.UserName,
            }).ToList();

            data.ForEach(w =>
            {
                w.Remarks = rmOperationList.Where(x => x.Ikey == w.Ikey).Select(x => new RmOperationModel()
                {
                    Remark = x.Remark,
                    UserId = x.UserId,
                    Name = x.UserName
                }).ToList();
            });

            return data;
        }
    }
}
