﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.DbFood.GiftOrderManage
{
    public interface IGiftLimitConfigInfo
    {
        /// <summary>
        /// 保存限额配置信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<SaveGiftLimitConfigInfoModel> SaveLimitConfig(SaveGiftLimitConfigInfoContext context);

        ResponseContext<SaveGiftLimitConfigInfoModel> SaveLimitConfigByStore(SaveGiftLimitConfigInfoContext context);

        /// <summary>
        /// 获取限额配置列表信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<RespPaginationModel<GetGiftLimitConfigInfoModel>> GetLimitConfigList(GetGiftLimitConfigInfoContext context);

        ResponseContext<RespPaginationModel<GetGiftLimitConfigInfoModel>> GetLimitConfigListByStore(GetGiftLimitConfigInfoContext context);

        /// <summary>
        /// 根据指定要求获取限额配置信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        List<Limit_ConfigInfo> GetLimitConfig(GetLimitConfigContext context);

        /// <summary>
        /// 对请求数据进行校验，是否能够通过验证
        /// </summary>
        /// <returns></returns>
        bool LimitValiDate(LimitValiDateContext context);
    }
}
