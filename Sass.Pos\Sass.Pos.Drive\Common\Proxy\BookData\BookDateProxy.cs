﻿using Saas.Pos.Common.Tools;
using Saas.Pos.Model.Enum;
using Saas.Pos.Model.Rms.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Proxy.BookData
{
    public class BookDateProxy
    {
        public BookDateProxyContext Context;

        public BookDateProxy(BookDateProxyContext context)
        {
            Context = context;
        }

        #region 各代理属性初始化

        RoomBaseProxy _Room;
        RtBaseProxy _RtInfo;
        DateProxyBase _Date;
        BookingBaseProxy _Booking;
        TimeInfoBaseProxy _TimeInfo;
        ConsumBaseProxy _Consum;
        ShopDataBaseProxy _Shop;


        /// <summary>
        /// 日期代理基类
        /// </summary>
        public DateProxyBase Date
        {
            get
            {
                if (_Date == null) _Date = CreateProxy<DateProxyBase>("Date", this);
                return _Date;
            }
        }

        /// <summary>
        /// 门店房型动态数据代理基类
        /// </summary>
        public BookingBaseProxy Booking
        {
            get
            {
                if (_Booking == null) _Booking = CreateProxy<BookingBaseProxy>("Booking", this);
                return _Booking;
            }
        }

        /// <summary>
        /// 门店房型代理基类
        /// </summary>
        public RtBaseProxy RtInfo
        {
            get
            {
                if (_RtInfo == null) _RtInfo = CreateProxy<RtBaseProxy>("RtInfo", this);
                return _RtInfo;
            }
        }

        /// <summary>
        /// 时段处理代理基类
        /// </summary>
        public TimeInfoBaseProxy TimeInfo
        {
            get
            {
                if (_TimeInfo == null) _TimeInfo = CreateProxy<TimeInfoBaseProxy>("TimeInfo", this);
                return _TimeInfo;
            }
        }

        /// <summary>
        /// 消费模式代理基类
        /// </summary>
        public ConsumBaseProxy Consum
        {
            get
            {
                if (_Consum == null) _Consum = CreateProxy<ConsumBaseProxy>("Consum", this);
                return _Consum;
            }
        }

        /// <summary>
        /// 房间处理代理基类
        /// </summary>
        public RoomBaseProxy Room
        {
            get
            {
                if (_Room == null) _Room = CreateProxy<RoomBaseProxy>("Room", this);
                return _Room;
            }
        }

        /// <summary>
        /// 门店处理代理基类
        /// </summary>
        public ShopDataBaseProxy Shop
        {
            get
            {
                if (_Shop == null) _Shop = CreateProxy<ShopDataBaseProxy>("Shop", this);
                return _Shop;
            }
        }

        #endregion

        static T CreateProxy<T>(string name, BookDateProxy proxy)
        {
            var platformStr = EnumHelper.GetEnumDescription((RmsPlatformEnum)proxy.Context.Platform);
            var obj = AssemblyHelper.CreateInstance<T>("Saas.Pos.Drive", "Saas.Pos.Drive.Common.Proxy.BookData",
                string.Format("{0}_{1}_Proxy", name, platformStr));
            var data = obj as BookDateBaseProxy;
            if (data != null) data.Proxy = proxy;
            return obj;
        }
    }
}
