﻿<#@ template debug="false" hostspecific="false" language="C#" #>
<#@ include file="EF.Utility.CS.ttinclude"#>
<#@ output extension=".cs" #>
<#
CodeGenerationTools code =new CodeGenerationTools(this);
MetadataLoader Loader =new MetadataLoader(this);
CodeRegion region=new CodeRegion(this,1);
MetadataTools ef=new MetadataTools(this);
string inputFile=@"E:\CompanyFile\Codes\ServiceApplication\Sass.Pos\Sass.Pos\Sass.Pos.Model\DbFood\DbFood_DB.edmx";
EdmItemCollection Itemcollection = Loader.CreateEdmItemCollection(inputFile);
string namespaceName=code.VsNamespaceSuggestion();
EntityFrameworkTemplateFileManager fileManager = EntityFrameworkTemplateFileManager.Create(this);
#>
using ComponentApplicationServiceInterface.Web;
using Saas.Pos.Model.DbFood;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
<#
foreach(EntityType entity in Itemcollection.GetItems<EntityType>().OrderBy(e=>e.Name))
{
#>
 public partial class <#=entity.Name#>App : AppBase<<#=entity.Name#>> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<<#=entity.Name#>> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.<#=entity.Name#>;
        }
   
        
 
 }
  

<#}#>
}
