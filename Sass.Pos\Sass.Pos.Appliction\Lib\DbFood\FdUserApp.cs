﻿using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
    public partial class FdUserApp : AppBase<FdUser>
    {
        public string GetUserName(string userId)
        {
            return Repository.FdUser.GetUserName(userId);
        }

        public List<GetFdUserAllModel> GetFdUserAll(GetFdUserAllContext context)
        {
            return Repository.FdUser.GetFdUserAll(context);
        }
    }
}
