<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute">
            <summary>
            Wird zum Angeben des Bereitstellungselements (Datei oder Verzeichnis) für eine Bereitstellung pro Test verwendet.
            Kann für eine Testklasse oder Testmethode angegeben werden.
            Kann mehrere Instanzen des Attributs besitzen, um mehrere Elemente anzugeben.
            Der Elementpfad kann absolut oder relativ sein. Wenn er relativ ist, dann relativ zu "RunConfig.RelativePathRoot".
            </summary>
            <example>
            [DeploymentItem("file1.xml")]
            [DeploymentItem("file2.xml", "DataFiles")]
            [DeploymentItem("bin\Debug")]
            </example>
            <remarks>
            Putting this in here so that UWP discovery works. We still do not want users to be using DeploymentItem in the UWP world - Hence making it internal.
            We should separate out DeploymentItem logic in the adapter via a Framework extensiblity point.
            Filed https://github.com/Microsoft/testfx/issues/100 to track this.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/>-Klasse.
            </summary>
            <param name="path">Die bereitzustellende Datei oder das Verzeichnis. Der Pfad ist relativ zum Buildausgabeverzeichnis. Das Element wird in das gleiche Verzeichnis wie die bereitgestellten Testassemblys kopiert.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String,System.String)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/>-Klasse.
            </summary>
            <param name="path">Der relative oder absolute Pfad zur bereitzustellenden Datei oder zum Verzeichnis. Der Pfad ist relativ zum Buildausgabeverzeichnis. Das Element wird in das gleiche Verzeichnis wie die bereitgestellten Testassemblys kopiert.</param>
            <param name="outputDirectory">Der Pfad des Verzeichnisses, in das die Elemente kopiert werden sollen. Er kann absolut oder relativ zum Bereitstellungsverzeichnis sein. Alle Dateien und Verzeichnisse, die identifiziert werden durch <paramref name="path"/> werden in dieses Verzeichnis kopiert.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.Path">
            <summary>
            Ruft den Pfad der Quelldatei oder des -ordners ab, die bzw. der kopiert werden soll.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.OutputDirectory">
            <summary>
            Ruft den Pfad des Verzeichnisses ab, in das das Element kopiert werden soll.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AppContainer.UITestMethodAttribute">
            <summary>
            Hiermit wird Testcode im UI-Thread für Windows Store-Apps ausgeführt.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AppContainer.UITestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Hiermit wird die Testmethode für den UI-Thread ausgeführt.
            </summary>
            <param name="testMethod">
            Die Testmethode.
            </param>
            <returns>
            Ein Array aus <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>-Instanzen.
            </returns>
            Throws <exception cref="T:System.NotSupportedException"> when run on an async test method.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext">
            <summary>
            Die TestContext-Klasse. Diese Klasse muss vollständig abstrakt sein und keine
                    Member enthalten. Der Adapter implementiert die Member. Benutzer im Framework sollten
           darauf nur über eine klar definierte Schnittstelle zugreifen.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.Properties">
            <summary>
            Ruft Testeigenschaften für einen Test ab.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.FullyQualifiedTestClassName">
            <summary>
            Ruft den vollqualifizierten Namen der Klasse ab, die die Testmethode enthält, die zurzeit ausgeführt wird.
            </summary>
            <remarks>
            This property can be useful in attributes derived from ExpectedExceptionBaseAttribute.
            Those attributes have access to the test context, and provide messages that are included
            in the test results. Users can benefit from messages that include the fully-qualified
            class name in addition to the name of the test method currently being executed.
            </remarks>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestName">
            <summary>
            Ruft den Namen der zurzeit ausgeführten Testmethode ab.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.CurrentTestOutcome">
            <summary>
            Ruft das aktuelle Testergebnis ab.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String)">
            <summary>
            Used to write trace messages while the test is running
            </summary>
            <param name="message">formatted message string</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String,System.Object[])">
            <summary>
            Used to write trace messages while the test is running
            </summary>
            <param name="format">format string</param>
            <param name="args">the arguments</param>
        </member>
    </members>
</doc>
