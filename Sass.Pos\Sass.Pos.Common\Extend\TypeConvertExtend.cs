﻿using Saas.Pos.Common.Tools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Extend
{
    /// <summary>
    /// 类型转换扩展方法
    /// </summary>
    public static class TypeConvertExtend
    {
        /// <summary>
        /// 克隆实体
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="type"></param>
        /// <returns></returns>
        public static T Clone<T>(this object type)
        {
            return EntityConversion.Map<T>(type);
        }
    }
}
