﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Global.Configs.DbFood
{
    /// <summary>
    /// 天王下单固定配置
    /// </summary>
    public class OrderConfig
    {

        #region 用户下单确认
        /// <summary>
        /// 用于统计报表下载自助餐人数
        /// </summary>
        public string FdCName = "消费人数";
        /// <summary>
        /// 默认消费模式
        /// </summary>
        public string DefaultCashType = "N";
        /// <summary>
        /// 用户客户确认人头数
        /// </summary>
        public string DefaultVistorUserName = "9923";
        /// <summary>
        /// 访客确认人头
        /// </summary>
        public string DefaultUserName = "9995";
        /// <summary>
        /// 所有门店人头下单默认项目
        /// </summary>
        public string HeadFdNo = "0902";

        #endregion

        #region 在线预订下单结构
        /// <summary>
        /// 下单消费模式
        /// </summary>
        public string OrderCashType = "Z";
        /// <summary>
        /// 订单下单人
        /// </summary>
        public string OrderUserName = "9987";
        /// <summary>
        /// 订单输单人
        /// </summary>
        public string OrderInputName = "9987";

        /// <summary>
        /// 微服务下单工号
        /// </summary>
        public string MicServiceInpId = "9924";

        /// <summary>
        /// 套餐下单工号
        /// </summary>
        public string PackageInputId = "0990";
        #endregion

        /// <summary>
        /// 店铺配置文件保存地址
        /// </summary>
        public string ShopFilePath = AppDomain.CurrentDomain.BaseDirectory + "\\Configs\\Shop\\storeUrlConfig.json";

        /// <summary>
        /// 销单权限名称
        /// </summary>
        public string DeletePermiss = "urFdDele";
    }
}
