﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.Common.Extend
{
    /// <summary>
    /// 核销卡券帮助类
    /// </summary>
    public class WriteOffExtend
    {
        /// <summary>
        /// 计算平均下单金额
        /// </summary>
        /// <param name="totalCount">卡券总数</param>
        /// <param name="totalUsedCount">卡券历史使用总数</param>
        /// <param name="usedCount">本次使用数量</param>
        /// <param name="payAmount">支付金额</param>
        /// <returns></returns>
        public static List<int> GetFdPrice(int totalCount, int totalUsedCount, int usedCount, decimal payAmount)
        {
            //每次下单的金额
            var avgPrice = (int)Math.Floor(payAmount / totalCount);

            var priceData = new List<int>();
            for (int i = 1; i <= usedCount; i++)
            {
                if (totalCount == totalUsedCount && totalUsedCount == usedCount && totalCount == 1)
                {
                    priceData.Add((int)payAmount);
                }
                else if (i == usedCount && totalCount == totalUsedCount)
                {
                    var usedPrice = avgPrice * (totalCount - 1);
                    var currentPrice = (int)payAmount - avgPrice;
                    priceData.Add(currentPrice);
                }
                else
                {
                    priceData.Add(avgPrice);
                }
            }

            return priceData;
        }
    }
}
