<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DoNotParallelizeAttribute">
            <summary>
            Specification to disable parallelization.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DynamicDataSourceType">
            <summary>
            Enum to specify whether the data is stored as property or in method.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DynamicDataSourceType.Property">
            <summary>
            Data is declared as property.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DynamicDataSourceType.Method">
            <summary>
            Data is declared in method.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DynamicDataAttribute">
            <summary>
            Attribute to define dynamic data for a test method.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DynamicDataAttribute.#ctor(System.String,Microsoft.VisualStudio.TestTools.UnitTesting.DynamicDataSourceType)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DynamicDataAttribute"/> class.
            </summary>
            <param name="dynamicDataSourceName">
            The name of method or property having test data.
            </param>
            <param name="dynamicDataSourceType">
            Specifies whether the data is stored as property or in method.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DynamicDataAttribute.#ctor(System.String,System.Type,Microsoft.VisualStudio.TestTools.UnitTesting.DynamicDataSourceType)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DynamicDataAttribute"/> class when the test data is present in a class different
            from test method's class.
            </summary>
            <param name="dynamicDataSourceName">
            The name of method or property having test data.
            </param>
            <param name="dynamicDataDeclaringType">
            The declaring type of property or method having data. Useful in cases when declaring type is present in a class different from
            test method's class. If null, declaring type defaults to test method's class type.
            </param>
            <param name="dynamicDataSourceType">
            Specifies whether the data is stored as property or in method.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DynamicDataAttribute.DynamicDataDisplayName">
            <summary>
            Gets or sets the name of method used to customize the display name in test results.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DynamicDataAttribute.DynamicDataDisplayNameDeclaringType">
            <summary>
            Gets or sets the declaring type used to customize the display name in test results.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DynamicDataAttribute.GetData(System.Reflection.MethodInfo)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DynamicDataAttribute.GetDisplayName(System.Reflection.MethodInfo,System.Object[])">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ParallelizeAttribute">
            <summary>
            Specification for parallelization level for a test run.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.ParallelizeAttribute.DefaultExecutionScope">
            <summary>
            The default scope for the parallel run. Although method level gives maximum parallelization, the default is set to
            class level to enable maximum number of customers to easily convert their tests to run in parallel. In most cases within
            a class tests aren't thread safe.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ParallelizeAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ParallelizeAttribute"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ParallelizeAttribute.Workers">
            <summary>
            Gets or sets the number of workers to be used for the parallel run.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ParallelizeAttribute.Scope">
            <summary>
            Gets or sets the scope of the parallel run.
            </summary>
            <remarks>
            To enable all classes to run in parallel set this to <see cref="F:Microsoft.VisualStudio.TestTools.UnitTesting.ExecutionScope.ClassLevel"/>.
            To get the maximum parallelization level set this to <see cref="F:Microsoft.VisualStudio.TestTools.UnitTesting.ExecutionScope.MethodLevel"/>.
            </remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExecutionScope">
            <summary>
            Parallel execution mode.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.ExecutionScope.ClassLevel">
            <summary>
            Each thread of execution will be handed a TestClass worth of tests to execute.
            Within the TestClass, the test methods will execute serially.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.ExecutionScope.MethodLevel">
            <summary>
            Each thread of execution will be handed TestMethods to execute.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ITestDataSource">
            <summary>
            Test data source for data driven tests.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestDataSource.GetData(System.Reflection.MethodInfo)">
            <summary>
            Gets the test data from custom test data source.
            </summary>
            <param name="methodInfo">
            The method info of test method.
            </param>
            <returns>
            Test data for calling test method.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestDataSource.GetDisplayName(System.Reflection.MethodInfo,System.Object[])">
            <summary>
            Gets the display name corresponding to test data row for displaying in TestResults.
            </summary>
            <param name="methodInfo">
            The method info of test method.
            </param>
            <param name="data">
            The test data which is passed to test method.
            </param>
            <returns>
            The <see cref="T:System.String"/>.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod">
            <summary>
            TestMethod for execution.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestMethodName">
            <summary>
            Gets the name of test method.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestClassName">
            <summary>
            Gets the name of test class.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ReturnType">
            <summary>
            Gets the return type of test method.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.Arguments">
            <summary>
            Gets the arguments with which test method is invoked.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ParameterTypes">
            <summary>
            Gets the parameters of test method.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.MethodInfo">
            <summary>
            Gets the methodInfo for test method.
            </summary>
            <remarks>
            This is just to retrieve additional information about the method.
            Do not directly invoke the method using MethodInfo. Use ITestMethod.Invoke instead.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.Invoke(System.Object[])">
            <summary>
            Invokes the test method.
            </summary>
            <param name="arguments">
            Arguments to pass to test method. (E.g. For data driven)
            </param>
            <returns>
            Result of test method invocation.
            </returns>
            <remarks>
            This call handles asynchronous test methods as well.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAllAttributes(System.Boolean)">
            <summary>
            Get all attributes of the test method.
            </summary>
            <param name="inherit">
            Whether attribute defined in parent class is valid.
            </param>
            <returns>
            All attributes.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAttributes``1(System.Boolean)">
            <summary>
            Get attribute of specific type.
            </summary>
            <typeparam name="AttributeType"> System.Attribute type. </typeparam>
            <param name="inherit">
            Whether attribute defined in parent class is valid.
            </param>
            <returns>
            The attributes of the specified type.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Helper">
            <summary>
            The helper.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNull(System.Object,System.String,System.String)">
            <summary>
            The check parameter not null.
            </summary>
            <param name="param">
            The parameter.
            </param>
            <param name="parameterName">
            The parameter name.
            </param>
            <param name="message">
            The message.
            </param>
            <exception cref="T:System.ArgumentNullException"> Throws argument null exception when parameter is null. </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNullOrEmpty(System.String,System.String,System.String)">
            <summary>
            The check parameter not null or empty.
            </summary>
            <param name="param">
            The parameter.
            </param>
            <param name="parameterName">
            The parameter name.
            </param>
            <param name="message">
            The message.
            </param>
            <exception cref="T:System.ArgumentException"> Throws ArgumentException when parameter is null. </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod">
            <summary>
            Enumeration for how we access data rows in data driven testing.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Sequential">
            <summary>
            Rows are returned in sequential order.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random">
            <summary>
            Rows are returned in random order.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute">
            <summary>
            Attribute to define in-line data for a test method.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> class.
            </summary>
            <param name="data1"> The data object. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> class which takes in an array of arguments.
            </summary>
            <param name="data1"> A data object. </param>
            <param name="moreData"> More data. </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.Data">
            <summary>
            Gets data for calling test method.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.DisplayName">
            <summary>
            Gets or sets display name in test results for customization.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.GetData(System.Reflection.MethodInfo)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.GetDisplayName(System.Reflection.MethodInfo,System.Object[])">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            <summary>
            The assert inconclusive exception.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> class.
            </summary>
            <param name="msg"> The message. </param>
            <param name="ex"> The exception. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> class.
            </summary>
            <param name="msg"> The message. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> class.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException">
            <summary>
            InternalTestFailureException class. Used to indicate internal failure for a test case
            </summary>
            <remarks>
            This class is only added to preserve source compatibility with the V1 framework.
            For all practical purposes either use AssertFailedException/AssertInconclusiveException.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> class.
            </summary>
            <param name="msg"> The exception message. </param>
            <param name="ex"> The exception. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> class.
            </summary>
            <param name="msg"> The exception message. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> class.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute">
            <summary>
            Attribute that specifies to expect an exception of the specified type
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> class with the expected type
            </summary>
            <param name="exceptionType">Type of the expected exception</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> class with
            the expected type and the message to include when no exception is thrown by the test.
            </summary>
            <param name="exceptionType">Type of the expected exception</param>
            <param name="noExceptionMessage">
            Message to include in the test result if the test fails due to not throwing an exception
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.ExceptionType">
            <summary>
            Gets a value indicating the Type of the expected exception
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.AllowDerivedTypes">
            <summary>
            Gets or sets a value indicating whether to allow types derived from the type of the expected exception to
            qualify as expected
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.NoExceptionMessage">
            <summary>
            Gets the message to include in the test result if the test fails due to not throwing an exception
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.Verify(System.Exception)">
            <summary>
            Verifies that the type of the exception thrown by the unit test is expected
            </summary>
            <param name="exception">The exception thrown by the unit test</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute">
            <summary>
            Base class for attributes that specify to expect an exception from a unit test
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> class with a default no-exception message
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> class with a no-exception message
            </summary>
            <param name="noExceptionMessage">
            Message to include in the test result if the test fails due to not throwing an
            exception
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.NoExceptionMessage">
            <summary>
            Gets the message to include in the test result if the test fails due to not throwing an exception
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.SpecifiedNoExceptionMessage">
            <summary>
            Gets the message to include in the test result if the test fails due to not throwing an exception
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.GetDefaultNoExceptionMessage(System.String)">
            <summary>
            Gets the default no-exception message
            </summary>
            <param name="expectedExceptionAttributeTypeName">The ExpectedException attribute type name</param>
            <returns>The default no-exception message</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.Verify(System.Exception)">
            <summary>
            Determines whether the exception is expected. If the method returns, then it is
            understood that the exception was expected. If the method throws an exception, then it
            is understood that the exception was not expected, and the thrown exception's message
            is included in the test result. The <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert"/> class can be used for
            convenience. If <see cref="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive"/> is used and the assertion fails,
            then the test outcome is set to Inconclusive.
            </summary>
            <param name="exception">The exception thrown by the unit test</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.RethrowIfAssertException(System.Exception)">
            <summary>
            Rethrow the exception if it is an AssertFailedException or an AssertInconclusiveException
            </summary>
            <param name="exception">The exception to rethrow if it is an assertion exception</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper">
            <summary>
            This class is designed to help user doing unit testing for types which uses generic types.
            GenericParameterHelper satisfies some common generic type constraints
            such as:
            1. public default constructor
            2. implements common interface: IComparable, IEnumerable
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> class that
            satisfies the 'newable' constraint in C# generics.
            </summary>
            <remarks>
            This constructor initializes the Data property to a random value.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> class that
            initializes the Data property to a user-supplied value.
            </summary>
            <param name="data">Any integer value</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Data">
            <summary>
            Gets or sets the Data
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Equals(System.Object)">
            <summary>
            Do the value comparison for two GenericParameterHelper object
            </summary>
            <param name="obj">object to do comparison with</param>
            <returns>true if obj has the same value as 'this' GenericParameterHelper object.
            false otherwise.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetHashCode">
            <summary>
            Returns a hashcode for this object.
            </summary>
            <returns>The hash code.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.CompareTo(System.Object)">
            <summary>
            Compares the data of the two <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> objects.
            </summary>
            <param name="obj">The object to compare with.</param>
            <returns>
            A signed number indicating the relative values of this instance and value.
            </returns>
            <exception cref="T:System.NotSupportedException">
            Thrown when the object passed in is not an instance of <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetEnumerator">
            <summary>
            Returns an IEnumerator object whose length is derived from
            the Data property.
            </summary>
            <returns>The IEnumerator object</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Clone">
            <summary>
            Returns a GenericParameterHelper object that is equal to
            the current object.
            </summary>
            <returns>The cloned object.</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger">
            <summary>
            Enables users to log/write traces from unit tests for diagnostics.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessageHandler">
            <summary>
            Handler for LogMessage.
            </summary>
            <param name="message">Message to log.</param>
        </member>
        <member name="E:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.OnLogMessage">
            <summary>
            Event to listen. Raised when unit test writer writes some message.
            Mainly to consume by adapter.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessage(System.String,System.Object[])">
            <summary>
            API for test writer to call to Log messages.
            </summary>
            <param name="format">String format with placeholders.</param>
            <param name="args">Parameters for placeholders.</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute">
            <summary>
            TestCategory attribute; used to specify the category of a unit test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute"/> class and applies the category to the test.
            </summary>
            <param name="testCategory">
            The test Category.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.TestCategories">
            <summary>
            Gets the test categories that has been applied to the test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute">
            <summary>
            Base class for the "Category" attribute
            </summary>
            <remarks>
            The reason for this attribute is to let the users create their own implementation of test categories.
            - test framework (discovery, etc) deals with TestCategoryBaseAttribute.
            - The reason that TestCategories property is a collection rather than a string,
              is to give more flexibility to the user. For instance the implementation may be based on enums for which the values can be OR'ed
              in which case it makes sense to have single attribute rather than multiple ones on the same test.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute"/> class.
            Applies the category to the test. The strings returned by TestCategories
            are used with the /category command to filter tests
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.TestCategories">
            <summary>
            Gets the test category that has been applied to the test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            <summary>
            AssertFailedException class. Used to indicate failure for a test case
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> class.
            </summary>
            <param name="msg"> The message. </param>
            <param name="ex"> The exception. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> class.
            </summary>
            <param name="msg"> The message. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> class.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert">
            <summary>
            A collection of helper classes to test various conditions within
            unit tests. If the condition being tested is not met, an exception
            is thrown.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.That">
            <summary>
            Gets the singleton instance of the Assert functionality.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void IsOfType&lt;T&gt;(this Assert assert, object obj)"
            Users could then use a syntax similar to the default assertions which in this case is "Assert.That.IsOfType&lt;Dog&gt;(animal);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean)">
            <summary>
            Tests whether the specified condition is true and throws an exception
            if the condition is false.
            </summary>
            <param name="condition">
            The condition the test expects to be true.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String)">
            <summary>
            Tests whether the specified condition is true and throws an exception
            if the condition is false.
            </summary>
            <param name="condition">
            The condition the test expects to be true.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="condition"/>
            is false. The message is shown in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String,System.Object[])">
            <summary>
            Tests whether the specified condition is true and throws an exception
            if the condition is false.
            </summary>
            <param name="condition">
            The condition the test expects to be true.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="condition"/>
            is false. The message is shown in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean)">
            <summary>
            Tests whether the specified condition is false and throws an exception
            if the condition is true.
            </summary>
            <param name="condition">
            The condition the test expects to be false.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String)">
            <summary>
            Tests whether the specified condition is false and throws an exception
            if the condition is true.
            </summary>
            <param name="condition">
            The condition the test expects to be false.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="condition"/>
            is true. The message is shown in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String,System.Object[])">
            <summary>
            Tests whether the specified condition is false and throws an exception
            if the condition is true.
            </summary>
            <param name="condition">
            The condition the test expects to be false.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="condition"/>
            is true. The message is shown in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object)">
            <summary>
            Tests whether the specified object is null and throws an exception
            if it is not.
            </summary>
            <param name="value">
            The object the test expects to be null.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String)">
            <summary>
            Tests whether the specified object is null and throws an exception
            if it is not.
            </summary>
            <param name="value">
            The object the test expects to be null.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="value"/>
            is not null. The message is shown in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String,System.Object[])">
            <summary>
            Tests whether the specified object is null and throws an exception
            if it is not.
            </summary>
            <param name="value">
            The object the test expects to be null.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="value"/>
            is not null. The message is shown in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object)">
            <summary>
            Tests whether the specified object is non-null and throws an exception
            if it is null.
            </summary>
            <param name="value">
            The object the test expects not to be null.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String)">
            <summary>
            Tests whether the specified object is non-null and throws an exception
            if it is null.
            </summary>
            <param name="value">
            The object the test expects not to be null.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="value"/>
            is null. The message is shown in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String,System.Object[])">
            <summary>
            Tests whether the specified object is non-null and throws an exception
            if it is null.
            </summary>
            <param name="value">
            The object the test expects not to be null.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="value"/>
            is null. The message is shown in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object)">
            <summary>
            Tests whether the specified objects both refer to the same object and
            throws an exception if the two inputs do not refer to the same object.
            </summary>
            <param name="expected">
            The first object to compare. This is the value the test expects.
            </param>
            <param name="actual">
            The second object to compare. This is the value produced by the code under test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String)">
            <summary>
            Tests whether the specified objects both refer to the same object and
            throws an exception if the two inputs do not refer to the same object.
            </summary>
            <param name="expected">
            The first object to compare. This is the value the test expects.
            </param>
            <param name="actual">
            The second object to compare. This is the value produced by the code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is not the same as <paramref name="expected"/>. The message is shown
            in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Tests whether the specified objects both refer to the same object and
            throws an exception if the two inputs do not refer to the same object.
            </summary>
            <param name="expected">
            The first object to compare. This is the value the test expects.
            </param>
            <param name="actual">
            The second object to compare. This is the value produced by the code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is not the same as <paramref name="expected"/>. The message is shown
            in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object)">
            <summary>
            Tests whether the specified objects refer to different objects and
            throws an exception if the two inputs refer to the same object.
            </summary>
            <param name="notExpected">
            The first object to compare. This is the value the test expects not
            to match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second object to compare. This is the value produced by the code under test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String)">
            <summary>
            Tests whether the specified objects refer to different objects and
            throws an exception if the two inputs refer to the same object.
            </summary>
            <param name="notExpected">
            The first object to compare. This is the value the test expects not
            to match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second object to compare. This is the value produced by the code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is the same as <paramref name="notExpected"/>. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Tests whether the specified objects refer to different objects and
            throws an exception if the two inputs refer to the same object.
            </summary>
            <param name="notExpected">
            The first object to compare. This is the value the test expects not
            to match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second object to compare. This is the value produced by the code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is the same as <paramref name="notExpected"/>. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0)">
            <summary>
            Tests whether the specified values are equal and throws an exception
            if the two values are not equal. Different numeric types are treated
            as unequal even if the logical values are equal. 42L is not equal to 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            The first value to compare. This is the value the tests expects.
            </param>
            <param name="actual">
            The second value to compare. This is the value produced by the code under test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String)">
            <summary>
            Tests whether the specified values are equal and throws an exception
            if the two values are not equal. Different numeric types are treated
            as unequal even if the logical values are equal. 42L is not equal to 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            The first value to compare. This is the value the tests expects.
            </param>
            <param name="actual">
            The second value to compare. This is the value produced by the code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is not equal to <paramref name="expected"/>. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Tests whether the specified values are equal and throws an exception
            if the two values are not equal. Different numeric types are treated
            as unequal even if the logical values are equal. 42L is not equal to 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            The first value to compare. This is the value the tests expects.
            </param>
            <param name="actual">
            The second value to compare. This is the value produced by the code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is not equal to <paramref name="expected"/>. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0)">
            <summary>
            Tests whether the specified values are unequal and throws an exception
            if the two values are equal. Different numeric types are treated
            as unequal even if the logical values are equal. 42L is not equal to 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            The first value to compare. This is the value the test expects not
            to match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second value to compare. This is the value produced by the code under test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String)">
            <summary>
            Tests whether the specified values are unequal and throws an exception
            if the two values are equal. Different numeric types are treated
            as unequal even if the logical values are equal. 42L is not equal to 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            The first value to compare. This is the value the test expects not
            to match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second value to compare. This is the value produced by the code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is equal to <paramref name="notExpected"/>. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Tests whether the specified values are unequal and throws an exception
            if the two values are equal. Different numeric types are treated
            as unequal even if the logical values are equal. 42L is not equal to 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            The first value to compare. This is the value the test expects not
            to match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second value to compare. This is the value produced by the code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is equal to <paramref name="notExpected"/>. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object)">
            <summary>
            Tests whether the specified objects are equal and throws an exception
            if the two objects are not equal. Different numeric types are treated
            as unequal even if the logical values are equal. 42L is not equal to 42.
            </summary>
            <param name="expected">
            The first object to compare. This is the object the tests expects.
            </param>
            <param name="actual">
            The second object to compare. This is the object produced by the code under test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String)">
            <summary>
            Tests whether the specified objects are equal and throws an exception
            if the two objects are not equal. Different numeric types are treated
            as unequal even if the logical values are equal. 42L is not equal to 42.
            </summary>
            <param name="expected">
            The first object to compare. This is the object the tests expects.
            </param>
            <param name="actual">
            The second object to compare. This is the object produced by the code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is not equal to <paramref name="expected"/>. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Tests whether the specified objects are equal and throws an exception
            if the two objects are not equal. Different numeric types are treated
            as unequal even if the logical values are equal. 42L is not equal to 42.
            </summary>
            <param name="expected">
            The first object to compare. This is the object the tests expects.
            </param>
            <param name="actual">
            The second object to compare. This is the object produced by the code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is not equal to <paramref name="expected"/>. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object)">
            <summary>
            Tests whether the specified objects are unequal and throws an exception
            if the two objects are equal. Different numeric types are treated
            as unequal even if the logical values are equal. 42L is not equal to 42.
            </summary>
            <param name="notExpected">
            The first object to compare. This is the value the test expects not
            to match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second object to compare. This is the object produced by the code under test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String)">
            <summary>
            Tests whether the specified objects are unequal and throws an exception
            if the two objects are equal. Different numeric types are treated
            as unequal even if the logical values are equal. 42L is not equal to 42.
            </summary>
            <param name="notExpected">
            The first object to compare. This is the value the test expects not
            to match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second object to compare. This is the object produced by the code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is equal to <paramref name="notExpected"/>. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Tests whether the specified objects are unequal and throws an exception
            if the two objects are equal. Different numeric types are treated
            as unequal even if the logical values are equal. 42L is not equal to 42.
            </summary>
            <param name="notExpected">
            The first object to compare. This is the value the test expects not
            to match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second object to compare. This is the object produced by the code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is equal to <paramref name="notExpected"/>. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single)">
            <summary>
            Tests whether the specified floats are equal and throws an exception
            if they are not equal.
            </summary>
            <param name="expected">
            The first float to compare. This is the float the tests expects.
            </param>
            <param name="actual">
            The second float to compare. This is the float produced by the code under test.
            </param>
            <param name="delta">
            The required accuracy. An exception will be thrown only if
            <paramref name="actual"/> is different than <paramref name="expected"/>
            by more than <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Tests whether the specified floats are equal and throws an exception
            if they are not equal.
            </summary>
            <param name="expected">
            The first float to compare. This is the float the tests expects.
            </param>
            <param name="actual">
            The second float to compare. This is the float produced by the code under test.
            </param>
            <param name="delta">
            The required accuracy. An exception will be thrown only if
            <paramref name="actual"/> is different than <paramref name="expected"/>
            by more than <paramref name="delta"/>.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is different than <paramref name="expected"/> by more than
            <paramref name="delta"/>. The message is shown in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Tests whether the specified floats are equal and throws an exception
            if they are not equal.
            </summary>
            <param name="expected">
            The first float to compare. This is the float the tests expects.
            </param>
            <param name="actual">
            The second float to compare. This is the float produced by the code under test.
            </param>
            <param name="delta">
            The required accuracy. An exception will be thrown only if
            <paramref name="actual"/> is different than <paramref name="expected"/>
            by more than <paramref name="delta"/>.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is different than <paramref name="expected"/> by more than
            <paramref name="delta"/>. The message is shown in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single)">
            <summary>
            Tests whether the specified floats are unequal and throws an exception
            if they are equal.
            </summary>
            <param name="notExpected">
            The first float to compare. This is the float the test expects not to
            match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second float to compare. This is the float produced by the code under test.
            </param>
            <param name="delta">
            The required accuracy. An exception will be thrown only if
            <paramref name="actual"/> is different than <paramref name="notExpected"/>
            by at most <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Tests whether the specified floats are unequal and throws an exception
            if they are equal.
            </summary>
            <param name="notExpected">
            The first float to compare. This is the float the test expects not to
            match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second float to compare. This is the float produced by the code under test.
            </param>
            <param name="delta">
            The required accuracy. An exception will be thrown only if
            <paramref name="actual"/> is different than <paramref name="notExpected"/>
            by at most <paramref name="delta"/>.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is equal to <paramref name="notExpected"/> or different by less than
            <paramref name="delta"/>. The message is shown in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Tests whether the specified floats are unequal and throws an exception
            if they are equal.
            </summary>
            <param name="notExpected">
            The first float to compare. This is the float the test expects not to
            match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second float to compare. This is the float produced by the code under test.
            </param>
            <param name="delta">
            The required accuracy. An exception will be thrown only if
            <paramref name="actual"/> is different than <paramref name="notExpected"/>
            by at most <paramref name="delta"/>.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is equal to <paramref name="notExpected"/> or different by less than
            <paramref name="delta"/>. The message is shown in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double)">
            <summary>
            Tests whether the specified doubles are equal and throws an exception
            if they are not equal.
            </summary>
            <param name="expected">
            The first double to compare. This is the double the tests expects.
            </param>
            <param name="actual">
            The second double to compare. This is the double produced by the code under test.
            </param>
            <param name="delta">
            The required accuracy. An exception will be thrown only if
            <paramref name="actual"/> is different than <paramref name="expected"/>
            by more than <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Tests whether the specified doubles are equal and throws an exception
            if they are not equal.
            </summary>
            <param name="expected">
            The first double to compare. This is the double the tests expects.
            </param>
            <param name="actual">
            The second double to compare. This is the double produced by the code under test.
            </param>
            <param name="delta">
            The required accuracy. An exception will be thrown only if
            <paramref name="actual"/> is different than <paramref name="expected"/>
            by more than <paramref name="delta"/>.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is different than <paramref name="expected"/> by more than
            <paramref name="delta"/>. The message is shown in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Tests whether the specified doubles are equal and throws an exception
            if they are not equal.
            </summary>
            <param name="expected">
            The first double to compare. This is the double the tests expects.
            </param>
            <param name="actual">
            The second double to compare. This is the double produced by the code under test.
            </param>
            <param name="delta">
            The required accuracy. An exception will be thrown only if
            <paramref name="actual"/> is different than <paramref name="expected"/>
            by more than <paramref name="delta"/>.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is different than <paramref name="expected"/> by more than
            <paramref name="delta"/>. The message is shown in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double)">
            <summary>
            Tests whether the specified doubles are unequal and throws an exception
            if they are equal.
            </summary>
            <param name="notExpected">
            The first double to compare. This is the double the test expects not to
            match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second double to compare. This is the double produced by the code under test.
            </param>
            <param name="delta">
            The required accuracy. An exception will be thrown only if
            <paramref name="actual"/> is different than <paramref name="notExpected"/>
            by at most <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Tests whether the specified doubles are unequal and throws an exception
            if they are equal.
            </summary>
            <param name="notExpected">
            The first double to compare. This is the double the test expects not to
            match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second double to compare. This is the double produced by the code under test.
            </param>
            <param name="delta">
            The required accuracy. An exception will be thrown only if
            <paramref name="actual"/> is different than <paramref name="notExpected"/>
            by at most <paramref name="delta"/>.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is equal to <paramref name="notExpected"/> or different by less than
            <paramref name="delta"/>. The message is shown in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Tests whether the specified doubles are unequal and throws an exception
            if they are equal.
            </summary>
            <param name="notExpected">
            The first double to compare. This is the double the test expects not to
            match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second double to compare. This is the double produced by the code under test.
            </param>
            <param name="delta">
            The required accuracy. An exception will be thrown only if
            <paramref name="actual"/> is different than <paramref name="notExpected"/>
            by at most <paramref name="delta"/>.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is equal to <paramref name="notExpected"/> or different by less than
            <paramref name="delta"/>. The message is shown in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean)">
            <summary>
            Tests whether the specified strings are equal and throws an exception
            if they are not equal. The invariant culture is used for the comparison.
            </summary>
            <param name="expected">
            The first string to compare. This is the string the tests expects.
            </param>
            <param name="actual">
            The second string to compare. This is the string produced by the code under test.
            </param>
            <param name="ignoreCase">
            A Boolean indicating a case-sensitive or insensitive comparison. (true
            indicates a case-insensitive comparison.)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Tests whether the specified strings are equal and throws an exception
            if they are not equal. The invariant culture is used for the comparison.
            </summary>
            <param name="expected">
            The first string to compare. This is the string the tests expects.
            </param>
            <param name="actual">
            The second string to compare. This is the string produced by the code under test.
            </param>
            <param name="ignoreCase">
            A Boolean indicating a case-sensitive or insensitive comparison. (true
            indicates a case-insensitive comparison.)
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is not equal to <paramref name="expected"/>. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Tests whether the specified strings are equal and throws an exception
            if they are not equal. The invariant culture is used for the comparison.
            </summary>
            <param name="expected">
            The first string to compare. This is the string the tests expects.
            </param>
            <param name="actual">
            The second string to compare. This is the string produced by the code under test.
            </param>
            <param name="ignoreCase">
            A Boolean indicating a case-sensitive or insensitive comparison. (true
            indicates a case-insensitive comparison.)
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is not equal to <paramref name="expected"/>. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Tests whether the specified strings are equal and throws an exception
            if they are not equal.
            </summary>
            <param name="expected">
            The first string to compare. This is the string the tests expects.
            </param>
            <param name="actual">
            The second string to compare. This is the string produced by the code under test.
            </param>
            <param name="ignoreCase">
            A Boolean indicating a case-sensitive or insensitive comparison. (true
            indicates a case-insensitive comparison.)
            </param>
            <param name="culture">
            A CultureInfo object that supplies culture-specific comparison information.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Tests whether the specified strings are equal and throws an exception
            if they are not equal.
            </summary>
            <param name="expected">
            The first string to compare. This is the string the tests expects.
            </param>
            <param name="actual">
            The second string to compare. This is the string produced by the code under test.
            </param>
            <param name="ignoreCase">
            A Boolean indicating a case-sensitive or insensitive comparison. (true
            indicates a case-insensitive comparison.)
            </param>
            <param name="culture">
            A CultureInfo object that supplies culture-specific comparison information.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is not equal to <paramref name="expected"/>. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Tests whether the specified strings are equal and throws an exception
            if they are not equal.
            </summary>
            <param name="expected">
            The first string to compare. This is the string the tests expects.
            </param>
            <param name="actual">
            The second string to compare. This is the string produced by the code under test.
            </param>
            <param name="ignoreCase">
            A Boolean indicating a case-sensitive or insensitive comparison. (true
            indicates a case-insensitive comparison.)
            </param>
            <param name="culture">
            A CultureInfo object that supplies culture-specific comparison information.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is not equal to <paramref name="expected"/>. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean)">
            <summary>
            Tests whether the specified strings are unequal and throws an exception
            if they are equal. The invariant culture is used for the comparison.
            </summary>
            <param name="notExpected">
            The first string to compare. This is the string the test expects not to
            match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second string to compare. This is the string produced by the code under test.
            </param>
            <param name="ignoreCase">
            A Boolean indicating a case-sensitive or insensitive comparison. (true
            indicates a case-insensitive comparison.)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Tests whether the specified strings are unequal and throws an exception
            if they are equal. The invariant culture is used for the comparison.
            </summary>
            <param name="notExpected">
            The first string to compare. This is the string the test expects not to
            match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second string to compare. This is the string produced by the code under test.
            </param>
            <param name="ignoreCase">
            A Boolean indicating a case-sensitive or insensitive comparison. (true
            indicates a case-insensitive comparison.)
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is equal to <paramref name="notExpected"/>. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Tests whether the specified strings are unequal and throws an exception
            if they are equal. The invariant culture is used for the comparison.
            </summary>
            <param name="notExpected">
            The first string to compare. This is the string the test expects not to
            match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second string to compare. This is the string produced by the code under test.
            </param>
            <param name="ignoreCase">
            A Boolean indicating a case-sensitive or insensitive comparison. (true
            indicates a case-insensitive comparison.)
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is equal to <paramref name="notExpected"/>. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Tests whether the specified strings are unequal and throws an exception
            if they are equal.
            </summary>
            <param name="notExpected">
            The first string to compare. This is the string the test expects not to
            match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second string to compare. This is the string produced by the code under test.
            </param>
            <param name="ignoreCase">
            A Boolean indicating a case-sensitive or insensitive comparison. (true
            indicates a case-insensitive comparison.)
            </param>
            <param name="culture">
            A CultureInfo object that supplies culture-specific comparison information.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Tests whether the specified strings are unequal and throws an exception
            if they are equal.
            </summary>
            <param name="notExpected">
            The first string to compare. This is the string the test expects not to
            match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second string to compare. This is the string produced by the code under test.
            </param>
            <param name="ignoreCase">
            A Boolean indicating a case-sensitive or insensitive comparison. (true
            indicates a case-insensitive comparison.)
            </param>
            <param name="culture">
            A CultureInfo object that supplies culture-specific comparison information.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is equal to <paramref name="notExpected"/>. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Tests whether the specified strings are unequal and throws an exception
            if they are equal.
            </summary>
            <param name="notExpected">
            The first string to compare. This is the string the test expects not to
            match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second string to compare. This is the string produced by the code under test.
            </param>
            <param name="ignoreCase">
            A Boolean indicating a case-sensitive or insensitive comparison. (true
            indicates a case-insensitive comparison.)
            </param>
            <param name="culture">
            A CultureInfo object that supplies culture-specific comparison information.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is equal to <paramref name="notExpected"/>. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type)">
            <summary>
            Tests whether the specified object is an instance of the expected
            type and throws an exception if the expected type is not in the
            inheritance hierarchy of the object.
            </summary>
            <param name="value">
            The object the test expects to be of the specified type.
            </param>
            <param name="expectedType">
            The expected type of <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Tests whether the specified object is an instance of the expected
            type and throws an exception if the expected type is not in the
            inheritance hierarchy of the object.
            </summary>
            <param name="value">
            The object the test expects to be of the specified type.
            </param>
            <param name="expectedType">
            The expected type of <paramref name="value"/>.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="value"/>
            is not an instance of <paramref name="expectedType"/>. The message is
            shown in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Tests whether the specified object is an instance of the expected
            type and throws an exception if the expected type is not in the
            inheritance hierarchy of the object.
            </summary>
            <param name="value">
            The object the test expects to be of the specified type.
            </param>
            <param name="expectedType">
            The expected type of <paramref name="value"/>.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="value"/>
            is not an instance of <paramref name="expectedType"/>. The message is
            shown in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type)">
            <summary>
            Tests whether the specified object is not an instance of the wrong
            type and throws an exception if the specified type is in the
            inheritance hierarchy of the object.
            </summary>
            <param name="value">
            The object the test expects not to be of the specified type.
            </param>
            <param name="wrongType">
            The type that <paramref name="value"/> should not be.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Tests whether the specified object is not an instance of the wrong
            type and throws an exception if the specified type is in the
            inheritance hierarchy of the object.
            </summary>
            <param name="value">
            The object the test expects not to be of the specified type.
            </param>
            <param name="wrongType">
            The type that <paramref name="value"/> should not be.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="value"/>
            is an instance of <paramref name="wrongType"/>. The message is shown
            in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Tests whether the specified object is not an instance of the wrong
            type and throws an exception if the specified type is in the
            inheritance hierarchy of the object.
            </summary>
            <param name="value">
            The object the test expects not to be of the specified type.
            </param>
            <param name="wrongType">
            The type that <paramref name="value"/> should not be.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="value"/>
            is an instance of <paramref name="wrongType"/>. The message is shown
            in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail">
            <summary>
            Throws an AssertFailedException.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String)">
            <summary>
            Throws an AssertFailedException.
            </summary>
            <param name="message">
            The message to include in the exception. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String,System.Object[])">
            <summary>
            Throws an AssertFailedException.
            </summary>
            <param name="message">
            The message to include in the exception. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive">
            <summary>
            Throws an AssertInconclusiveException.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String)">
            <summary>
            Throws an AssertInconclusiveException.
            </summary>
            <param name="message">
            The message to include in the exception. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String,System.Object[])">
            <summary>
            Throws an AssertInconclusiveException.
            </summary>
            <param name="message">
            The message to include in the exception. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Equals(System.Object,System.Object)">
            <summary>
            Static equals overloads are used for comparing instances of two types for reference
            equality. This method should <b>not</b> be used for comparison of two instances for
            equality. This object will <b>always</b> throw with Assert.Fail. Please use
            Assert.AreEqual and associated overloads in your unit tests.
            </summary>
            <param name="objA"> Object A </param>
            <param name="objB"> Object B </param>
            <returns> False, always. </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action)">
            <summary>
            Tests whether the code specified by delegate <paramref name="action"/> throws exact given exception of type <typeparamref name="T"/> (and not of derived type)
            and throws
            <code>
            AssertFailedException
            </code>
            if code does not throws exception or throws exception of type other than <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegate to code to be tested and which is expected to throw exception.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            The exception that was thrown.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String)">
            <summary>
            Tests whether the code specified by delegate <paramref name="action"/> throws exact given exception of type <typeparamref name="T"/> (and not of derived type)
            and throws
            <code>
            AssertFailedException
            </code>
            if code does not throws exception or throws exception of type other than <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegate to code to be tested and which is expected to throw exception.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="action"/>
            does not throws exception of type <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            The exception that was thrown.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object})">
            <summary>
            Tests whether the code specified by delegate <paramref name="action"/> throws exact given exception of type <typeparamref name="T"/> (and not of derived type)
            and throws
            <code>
            AssertFailedException
            </code>
            if code does not throws exception or throws exception of type other than <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegate to code to be tested and which is expected to throw exception.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            The exception that was thrown.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String)">
            <summary>
            Tests whether the code specified by delegate <paramref name="action"/> throws exact given exception of type <typeparamref name="T"/> (and not of derived type)
            and throws
            <code>
            AssertFailedException
            </code>
            if code does not throws exception or throws exception of type other than <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegate to code to be tested and which is expected to throw exception.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="action"/>
            does not throws exception of type <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            The exception that was thrown.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String,System.Object[])">
            <summary>
            Tests whether the code specified by delegate <paramref name="action"/> throws exact given exception of type <typeparamref name="T"/> (and not of derived type)
            and throws
            <code>
            AssertFailedException
            </code>
            if code does not throws exception or throws exception of type other than <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegate to code to be tested and which is expected to throw exception.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="action"/>
            does not throws exception of type <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throw exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            The exception that was thrown.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String,System.Object[])">
            <summary>
            Tests whether the code specified by delegate <paramref name="action"/> throws exact given exception of type <typeparamref name="T"/> (and not of derived type)
            and throws
            <code>
            AssertFailedException
            </code>
            if code does not throws exception or throws exception of type other than <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegate to code to be tested and which is expected to throw exception.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="action"/>
            does not throws exception of type <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            The exception that was thrown.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Tests whether the code specified by delegate <paramref name="action"/> throws exact given exception of type <typeparamref name="T"/> (and not of derived type)
            and throws
            <code>
            AssertFailedException
            </code>
            if code does not throws exception or throws exception of type other than <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegate to code to be tested and which is expected to throw exception.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> executing the delegate.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            Tests whether the code specified by delegate <paramref name="action"/> throws exact given exception of type <typeparamref name="T"/> (and not of derived type)
            and throws <code>AssertFailedException</code> if code does not throws exception or throws exception of type other than <typeparamref name="T"/>.
            </summary>
            <param name="action">Delegate to code to be tested and which is expected to throw exception.</param>
            <param name="message">
            The message to include in the exception when <paramref name="action"/>
            does not throws exception of type <typeparamref name="T"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> executing the delegate.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String,System.Object[])">
            <summary>
            Tests whether the code specified by delegate <paramref name="action"/> throws exact given exception of type <typeparamref name="T"/> (and not of derived type)
            and throws <code>AssertFailedException</code> if code does not throws exception or throws exception of type other than <typeparamref name="T"/>.
            </summary>
            <param name="action">Delegate to code to be tested and which is expected to throw exception.</param>
            <param name="message">
            The message to include in the exception when <paramref name="action"/>
            does not throws exception of type <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> executing the delegate.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNullChars(System.String)">
            <summary>
            Replaces null characters ('\0') with "\\0".
            </summary>
            <param name="input">
            The string to search.
            </param>
            <returns>
            The converted string with null characters replaced by "\\0".
            </returns>
            <remarks>
            This is only public and still present to preserve compatibility with the V1 framework.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.HandleFail(System.String,System.String,System.Object[])">
            <summary>
            Helper function that creates and throws an AssertionFailedException
            </summary>
            <param name="assertionName">
            name of the assertion throwing an exception
            </param>
            <param name="message">
            message describing conditions for assertion failure
            </param>
            <param name="parameters">
            The parameters.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.CheckParameterNotNull(System.Object,System.String,System.String,System.String,System.Object[])">
            <summary>
            Checks the parameter for valid conditions
            </summary>
            <param name="param">
            The parameter.
            </param>
            <param name="assertionName">
            The assertion Name.
            </param>
            <param name="parameterName">
            parameter name
            </param>
            <param name="message">
            message for the invalid parameter exception
            </param>
            <param name="parameters">
            The parameters.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNulls(System.Object)">
            <summary>
            Safely converts an object to a string, handling null values and null characters.
            Null values are converted to "(null)". Null characters are converted to "\\0".
            </summary>
            <param name="input">
            The object to convert to a string.
            </param>
            <returns>
            The converted string.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert">
            <summary>
            The string assert.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.That">
            <summary>
            Gets the singleton instance of the CollectionAssert functionality.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void ContainsWords(this StringAssert customAssert, string value, ICollection substrings)"
            Users could then use a syntax similar to the default assertions which in this case is "StringAssert.That.ContainsWords(value, substrings);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String)">
            <summary>
            Tests whether the specified string contains the specified substring
            and throws an exception if the substring does not occur within the
            test string.
            </summary>
            <param name="value">
            The string that is expected to contain <paramref name="substring"/>.
            </param>
            <param name="substring">
            The string expected to occur within <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String)">
            <summary>
            Tests whether the specified string contains the specified substring
            and throws an exception if the substring does not occur within the
            test string.
            </summary>
            <param name="value">
            The string that is expected to contain <paramref name="substring"/>.
            </param>
            <param name="substring">
            The string expected to occur within <paramref name="value"/>.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="substring"/>
            is not in <paramref name="value"/>. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String,System.Object[])">
            <summary>
            Tests whether the specified string contains the specified substring
            and throws an exception if the substring does not occur within the
            test string.
            </summary>
            <param name="value">
            The string that is expected to contain <paramref name="substring"/>.
            </param>
            <param name="substring">
            The string expected to occur within <paramref name="value"/>.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="substring"/>
            is not in <paramref name="value"/>. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String)">
            <summary>
            Tests whether the specified string begins with the specified substring
            and throws an exception if the test string does not start with the
            substring.
            </summary>
            <param name="value">
            The string that is expected to begin with <paramref name="substring"/>.
            </param>
            <param name="substring">
            The string expected to be a prefix of <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String)">
            <summary>
            Tests whether the specified string begins with the specified substring
            and throws an exception if the test string does not start with the
            substring.
            </summary>
            <param name="value">
            The string that is expected to begin with <paramref name="substring"/>.
            </param>
            <param name="substring">
            The string expected to be a prefix of <paramref name="value"/>.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="value"/>
            does not begin with <paramref name="substring"/>. The message is
            shown in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Tests whether the specified string begins with the specified substring
            and throws an exception if the test string does not start with the
            substring.
            </summary>
            <param name="value">
            The string that is expected to begin with <paramref name="substring"/>.
            </param>
            <param name="substring">
            The string expected to be a prefix of <paramref name="value"/>.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="value"/>
            does not begin with <paramref name="substring"/>. The message is
            shown in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String)">
            <summary>
            Tests whether the specified string ends with the specified substring
            and throws an exception if the test string does not end with the
            substring.
            </summary>
            <param name="value">
            The string that is expected to end with <paramref name="substring"/>.
            </param>
            <param name="substring">
            The string expected to be a suffix of <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String)">
            <summary>
            Tests whether the specified string ends with the specified substring
            and throws an exception if the test string does not end with the
            substring.
            </summary>
            <param name="value">
            The string that is expected to end with <paramref name="substring"/>.
            </param>
            <param name="substring">
            The string expected to be a suffix of <paramref name="value"/>.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="value"/>
            does not end with <paramref name="substring"/>. The message is
            shown in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Tests whether the specified string ends with the specified substring
            and throws an exception if the test string does not end with the
            substring.
            </summary>
            <param name="value">
            The string that is expected to end with <paramref name="substring"/>.
            </param>
            <param name="substring">
            The string expected to be a suffix of <paramref name="value"/>.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="value"/>
            does not end with <paramref name="substring"/>. The message is
            shown in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Tests whether the specified string matches a regular expression and
            throws an exception if the string does not match the expression.
            </summary>
            <param name="value">
            The string that is expected to match <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            The regular expression that <paramref name="value"/> is
            expected to match.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Tests whether the specified string matches a regular expression and
            throws an exception if the string does not match the expression.
            </summary>
            <param name="value">
            The string that is expected to match <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            The regular expression that <paramref name="value"/> is
            expected to match.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="value"/>
            does not match <paramref name="pattern"/>. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Tests whether the specified string matches a regular expression and
            throws an exception if the string does not match the expression.
            </summary>
            <param name="value">
            The string that is expected to match <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            The regular expression that <paramref name="value"/> is
            expected to match.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="value"/>
            does not match <paramref name="pattern"/>. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Tests whether the specified string does not match a regular expression
            and throws an exception if the string matches the expression.
            </summary>
            <param name="value">
            The string that is expected not to match <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            The regular expression that <paramref name="value"/> is
            expected to not match.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Tests whether the specified string does not match a regular expression
            and throws an exception if the string matches the expression.
            </summary>
            <param name="value">
            The string that is expected not to match <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            The regular expression that <paramref name="value"/> is
            expected to not match.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="value"/>
            matches <paramref name="pattern"/>. The message is shown in test
            results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Tests whether the specified string does not match a regular expression
            and throws an exception if the string matches the expression.
            </summary>
            <param name="value">
            The string that is expected not to match <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            The regular expression that <paramref name="value"/> is
            expected to not match.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="value"/>
            matches <paramref name="pattern"/>. The message is shown in test
            results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert">
            <summary>
            A collection of helper classes to test various conditions associated
            with collections within unit tests. If the condition being tested is not
            met, an exception is thrown.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.That">
            <summary>
            Gets the singleton instance of the CollectionAssert functionality.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void AreEqualUnordered(this CollectionAssert customAssert, ICollection expected, ICollection actual)"
            Users could then use a syntax similar to the default assertions which in this case is "CollectionAssert.That.AreEqualUnordered(list1, list2);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object)">
            <summary>
            Tests whether the specified collection contains the specified element
            and throws an exception if the element is not in the collection.
            </summary>
            <param name="collection">
            The collection in which to search for the element.
            </param>
            <param name="element">
            The element that is expected to be in the collection.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Tests whether the specified collection contains the specified element
            and throws an exception if the element is not in the collection.
            </summary>
            <param name="collection">
            The collection in which to search for the element.
            </param>
            <param name="element">
            The element that is expected to be in the collection.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="element"/>
            is not in <paramref name="collection"/>. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Tests whether the specified collection contains the specified element
            and throws an exception if the element is not in the collection.
            </summary>
            <param name="collection">
            The collection in which to search for the element.
            </param>
            <param name="element">
            The element that is expected to be in the collection.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="element"/>
            is not in <paramref name="collection"/>. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object)">
            <summary>
            Tests whether the specified collection does not contain the specified
            element and throws an exception if the element is in the collection.
            </summary>
            <param name="collection">
            The collection in which to search for the element.
            </param>
            <param name="element">
            The element that is expected not to be in the collection.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Tests whether the specified collection does not contain the specified
            element and throws an exception if the element is in the collection.
            </summary>
            <param name="collection">
            The collection in which to search for the element.
            </param>
            <param name="element">
            The element that is expected not to be in the collection.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="element"/>
            is in <paramref name="collection"/>. The message is shown in test
            results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Tests whether the specified collection does not contain the specified
            element and throws an exception if the element is in the collection.
            </summary>
            <param name="collection">
            The collection in which to search for the element.
            </param>
            <param name="element">
            The element that is expected not to be in the collection.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="element"/>
            is in <paramref name="collection"/>. The message is shown in test
            results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection)">
            <summary>
            Tests whether all items in the specified collection are non-null and throws
            an exception if any element is null.
            </summary>
            <param name="collection">
            The collection in which to search for null elements.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String)">
            <summary>
            Tests whether all items in the specified collection are non-null and throws
            an exception if any element is null.
            </summary>
            <param name="collection">
            The collection in which to search for null elements.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="collection"/>
            contains a null element. The message is shown in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Tests whether all items in the specified collection are non-null and throws
            an exception if any element is null.
            </summary>
            <param name="collection">
            The collection in which to search for null elements.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="collection"/>
            contains a null element. The message is shown in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection)">
            <summary>
            Tests whether all items in the specified collection are unique or not and
            throws if any two elements in the collection are equal.
            </summary>
            <param name="collection">
            The collection in which to search for duplicate elements.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String)">
            <summary>
            Tests whether all items in the specified collection are unique or not and
            throws if any two elements in the collection are equal.
            </summary>
            <param name="collection">
            The collection in which to search for duplicate elements.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="collection"/>
            contains at least one duplicate element. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Tests whether all items in the specified collection are unique or not and
            throws if any two elements in the collection are equal.
            </summary>
            <param name="collection">
            The collection in which to search for duplicate elements.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="collection"/>
            contains at least one duplicate element. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Tests whether one collection is a subset of another collection and
            throws an exception if any element in the subset is not also in the
            superset.
            </summary>
            <param name="subset">
            The collection expected to be a subset of <paramref name="superset"/>.
            </param>
            <param name="superset">
            The collection expected to be a superset of <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Tests whether one collection is a subset of another collection and
            throws an exception if any element in the subset is not also in the
            superset.
            </summary>
            <param name="subset">
            The collection expected to be a subset of <paramref name="superset"/>.
            </param>
            <param name="superset">
            The collection expected to be a superset of <paramref name="subset"/>
            </param>
            <param name="message">
            The message to include in the exception when an element in
            <paramref name="subset"/> is not found in <paramref name="superset"/>.
            The message is shown in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Tests whether one collection is a subset of another collection and
            throws an exception if any element in the subset is not also in the
            superset.
            </summary>
            <param name="subset">
            The collection expected to be a subset of <paramref name="superset"/>.
            </param>
            <param name="superset">
            The collection expected to be a superset of <paramref name="subset"/>
            </param>
            <param name="message">
            The message to include in the exception when an element in
            <paramref name="subset"/> is not found in <paramref name="superset"/>.
            The message is shown in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Tests whether one collection is not a subset of another collection and
            throws an exception if all elements in the subset are also in the
            superset.
            </summary>
            <param name="subset">
            The collection expected not to be a subset of <paramref name="superset"/>.
            </param>
            <param name="superset">
            The collection expected not to be a superset of <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Tests whether one collection is not a subset of another collection and
            throws an exception if all elements in the subset are also in the
            superset.
            </summary>
            <param name="subset">
            The collection expected not to be a subset of <paramref name="superset"/>.
            </param>
            <param name="superset">
            The collection expected not to be a superset of <paramref name="subset"/>
            </param>
            <param name="message">
            The message to include in the exception when every element in
            <paramref name="subset"/> is also found in <paramref name="superset"/>.
            The message is shown in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Tests whether one collection is not a subset of another collection and
            throws an exception if all elements in the subset are also in the
            superset.
            </summary>
            <param name="subset">
            The collection expected not to be a subset of <paramref name="superset"/>.
            </param>
            <param name="superset">
            The collection expected not to be a superset of <paramref name="subset"/>
            </param>
            <param name="message">
            The message to include in the exception when every element in
            <paramref name="subset"/> is also found in <paramref name="superset"/>.
            The message is shown in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Tests whether two collections contain the same elements and throws an
            exception if either collection contains an element not in the other
            collection.
            </summary>
            <param name="expected">
            The first collection to compare. This contains the elements the test
            expects.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by
            the code under test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Tests whether two collections contain the same elements and throws an
            exception if either collection contains an element not in the other
            collection.
            </summary>
            <param name="expected">
            The first collection to compare. This contains the elements the test
            expects.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by
            the code under test.
            </param>
            <param name="message">
            The message to include in the exception when an element was found
            in one of the collections but not the other. The message is shown
            in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Tests whether two collections contain the same elements and throws an
            exception if either collection contains an element not in the other
            collection.
            </summary>
            <param name="expected">
            The first collection to compare. This contains the elements the test
            expects.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by
            the code under test.
            </param>
            <param name="message">
            The message to include in the exception when an element was found
            in one of the collections but not the other. The message is shown
            in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Tests whether two collections contain the different elements and throws an
            exception if the two collections contain identical elements without regard
            to order.
            </summary>
            <param name="expected">
            The first collection to compare. This contains the elements the test
            expects to be different than the actual collection.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by
            the code under test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Tests whether two collections contain the different elements and throws an
            exception if the two collections contain identical elements without regard
            to order.
            </summary>
            <param name="expected">
            The first collection to compare. This contains the elements the test
            expects to be different than the actual collection.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by
            the code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            contains the same elements as <paramref name="expected"/>. The message
            is shown in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Tests whether two collections contain the different elements and throws an
            exception if the two collections contain identical elements without regard
            to order.
            </summary>
            <param name="expected">
            The first collection to compare. This contains the elements the test
            expects to be different than the actual collection.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by
            the code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            contains the same elements as <paramref name="expected"/>. The message
            is shown in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type)">
            <summary>
            Tests whether all elements in the specified collection are instances
            of the expected type and throws an exception if the expected type is
            not in the inheritance hierarchy of one or more of the elements.
            </summary>
            <param name="collection">
            The collection containing elements the test expects to be of the
            specified type.
            </param>
            <param name="expectedType">
            The expected type of each element of <paramref name="collection"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String)">
            <summary>
            Tests whether all elements in the specified collection are instances
            of the expected type and throws an exception if the expected type is
            not in the inheritance hierarchy of one or more of the elements.
            </summary>
            <param name="collection">
            The collection containing elements the test expects to be of the
            specified type.
            </param>
            <param name="expectedType">
            The expected type of each element of <paramref name="collection"/>.
            </param>
            <param name="message">
            The message to include in the exception when an element in
            <paramref name="collection"/> is not an instance of
            <paramref name="expectedType"/>. The message is shown in test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String,System.Object[])">
            <summary>
            Tests whether all elements in the specified collection are instances
            of the expected type and throws an exception if the expected type is
            not in the inheritance hierarchy of one or more of the elements.
            </summary>
            <param name="collection">
            The collection containing elements the test expects to be of the
            specified type.
            </param>
            <param name="expectedType">
            The expected type of each element of <paramref name="collection"/>.
            </param>
            <param name="message">
            The message to include in the exception when an element in
            <paramref name="collection"/> is not an instance of
            <paramref name="expectedType"/>. The message is shown in test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Tests whether the specified collections are equal and throws an exception
            if the two collections are not equal. Equality is defined as having the same
            elements in the same order and quantity. Whether two elements are the same
            is checked using <see cref="M:System.Object.Equals(System.Object,System.Object)" /> method.
            Different references to the same value are considered equal.
            </summary>
            <param name="expected">
            The first collection to compare. This is the collection the tests expects.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by the
            code under test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Tests whether the specified collections are equal and throws an exception
            if the two collections are not equal. Equality is defined as having the same
            elements in the same order and quantity. Whether two elements are the same
            is checked using <see cref="M:System.Object.Equals(System.Object,System.Object)" /> method.
            Different references to the same value are considered equal.
            </summary>
            <param name="expected">
            The first collection to compare. This is the collection the tests expects.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by the
            code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is not equal to <paramref name="expected"/>. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Tests whether the specified collections are equal and throws an exception
            if the two collections are not equal. Equality is defined as having the same
            elements in the same order and quantity. Whether two elements are the same
            is checked using <see cref="M:System.Object.Equals(System.Object,System.Object)" /> method.
            Different references to the same value are considered equal.
            </summary>
            <param name="expected">
            The first collection to compare. This is the collection the tests expects.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by the
            code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is not equal to <paramref name="expected"/>. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Tests whether the specified collections are unequal and throws an exception
            if the two collections are equal. Equality is defined as having the same
            elements in the same order and quantity. Whether two elements are the same
            is checked using <see cref="M:System.Object.Equals(System.Object,System.Object)" /> method.
            Different references to the same value are considered equal.
            </summary>
            <param name="notExpected">
            The first collection to compare. This is the collection the tests expects
            not to match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by the
            code under test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Tests whether the specified collections are unequal and throws an exception
            if the two collections are equal. Equality is defined as having the same
            elements in the same order and quantity. Whether two elements are the same
            is checked using <see cref="M:System.Object.Equals(System.Object,System.Object)" /> method.
            Different references to the same value are considered equal.
            </summary>
            <param name="notExpected">
            The first collection to compare. This is the collection the tests expects
            not to match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by the
            code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is equal to <paramref name="notExpected"/>. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Tests whether the specified collections are unequal and throws an exception
            if the two collections are equal. Equality is defined as having the same
            elements in the same order and quantity. Whether two elements are the same
            is checked using <see cref="M:System.Object.Equals(System.Object,System.Object)" /> method.
            Different references to the same value are considered equal.
            </summary>
            <param name="notExpected">
            The first collection to compare. This is the collection the tests expects
            not to match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by the
            code under test.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is equal to <paramref name="notExpected"/>. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Tests whether the specified collections are equal and throws an exception
            if the two collections are not equal. Equality is defined as having the same
            elements in the same order and quantity. Different references to the same
            value are considered equal.
            </summary>
            <param name="expected">
            The first collection to compare. This is the collection the tests expects.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by the
            code under test.
            </param>
            <param name="comparer">
            The compare implementation to use when comparing elements of the collection.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Tests whether the specified collections are equal and throws an exception
            if the two collections are not equal. Equality is defined as having the same
            elements in the same order and quantity. Different references to the same
            value are considered equal.
            </summary>
            <param name="expected">
            The first collection to compare. This is the collection the tests expects.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by the
            code under test.
            </param>
            <param name="comparer">
            The compare implementation to use when comparing elements of the collection.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is not equal to <paramref name="expected"/>. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Tests whether the specified collections are equal and throws an exception
            if the two collections are not equal. Equality is defined as having the same
            elements in the same order and quantity. Different references to the same
            value are considered equal.
            </summary>
            <param name="expected">
            The first collection to compare. This is the collection the tests expects.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by the
            code under test.
            </param>
            <param name="comparer">
            The compare implementation to use when comparing elements of the collection.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is not equal to <paramref name="expected"/>. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Tests whether the specified collections are unequal and throws an exception
            if the two collections are equal. Equality is defined as having the same
            elements in the same order and quantity. Different references to the same
            value are considered equal.
            </summary>
            <param name="notExpected">
            The first collection to compare. This is the collection the tests expects
            not to match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by the
            code under test.
            </param>
            <param name="comparer">
            The compare implementation to use when comparing elements of the collection.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Tests whether the specified collections are unequal and throws an exception
            if the two collections are equal. Equality is defined as having the same
            elements in the same order and quantity. Different references to the same
            value are considered equal.
            </summary>
            <param name="notExpected">
            The first collection to compare. This is the collection the tests expects
            not to match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by the
            code under test.
            </param>
            <param name="comparer">
            The compare implementation to use when comparing elements of the collection.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is equal to <paramref name="notExpected"/>. The message is shown in
            test results.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Tests whether the specified collections are unequal and throws an exception
            if the two collections are equal. Equality is defined as having the same
            elements in the same order and quantity. Different references to the same
            value are considered equal.
            </summary>
            <param name="notExpected">
            The first collection to compare. This is the collection the tests expects
            not to match <paramref name="actual"/>.
            </param>
            <param name="actual">
            The second collection to compare. This is the collection produced by the
            code under test.
            </param>
            <param name="comparer">
            The compare implementation to use when comparing elements of the collection.
            </param>
            <param name="message">
            The message to include in the exception when <paramref name="actual"/>
            is equal to <paramref name="notExpected"/>. The message is shown in
            test results.
            </param>
            <param name="parameters">
            An array of parameters to use when formatting <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOfHelper(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Determines whether the first collection is a subset of the second
            collection. If either set contains duplicate elements, the number
            of occurrences of the element in the subset must be less than or
            equal to the number of occurrences in the superset.
            </summary>
            <param name="subset">
            The collection the test expects to be contained in <paramref name="superset"/>.
            </param>
            <param name="superset">
            The collection the test expects to contain <paramref name="subset"/>.
            </param>
            <returns>
            True if <paramref name="subset"/> is a subset of
            <paramref name="superset"/>, false otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.GetElementCounts(System.Collections.ICollection,System.Int32@)">
            <summary>
            Constructs a dictionary containing the number of occurrences of each
            element in the specified collection.
            </summary>
            <param name="collection">
            The collection to process.
            </param>
            <param name="nullCount">
            The number of null elements in the collection.
            </param>
            <returns>
            A dictionary containing the number of occurrences of each element
            in the specified collection.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.FindMismatchedElement(System.Collections.ICollection,System.Collections.ICollection,System.Int32@,System.Int32@,System.Object@)">
            <summary>
            Finds a mismatched element between the two collections. A mismatched
            element is one that appears a different number of times in the
            expected collection than it does in the actual collection. The
            collections are assumed to be different non-null references with the
            same number of elements. The caller is responsible for this level of
            verification. If there is no mismatched element, the function returns
            false and the out parameters should not be used.
            </summary>
            <param name="expected">
            The first collection to compare.
            </param>
            <param name="actual">
            The second collection to compare.
            </param>
            <param name="expectedCount">
            The expected number of occurrences of
            <paramref name="mismatchedElement"/> or 0 if there is no mismatched
            element.
            </param>
            <param name="actualCount">
            The actual number of occurrences of
            <paramref name="mismatchedElement"/> or 0 if there is no mismatched
            element.
            </param>
            <param name="mismatchedElement">
            The mismatched element (may be null) or null if there is no
            mismatched element.
            </param>
            <returns>
            true if a mismatched element was found; false otherwise.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.ObjectComparer">
            <summary>
            compares the objects using object.Equals
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException">
            <summary>
            Base class for Framework Exceptions.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> class.
            </summary>
            <param name="msg"> The message. </param>
            <param name="ex"> The exception. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> class.
            </summary>
            <param name="msg"> The message. </param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AccessStringInvalidSyntax">
            <summary>
              Looks up a localized string similar to Access string has invalid syntax..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ActualHasMismatchedElements">
            <summary>
              Looks up a localized string similar to The expected collection contains {1} occurrence(s) of &lt;{2}&gt;. The actual collection contains {3} occurrence(s). {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AllItemsAreUniqueFailMsg">
            <summary>
              Looks up a localized string similar to Duplicate item found:&lt;{1}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualCaseFailMsg">
            <summary>
              Looks up a localized string similar to Expected:&lt;{1}&gt;. Case is different for actual value:&lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDeltaFailMsg">
            <summary>
              Looks up a localized string similar to Expected a difference no greater than &lt;{3}&gt; between expected value &lt;{1}&gt; and actual value &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDifferentTypesFailMsg">
            <summary>
              Looks up a localized string similar to Expected:&lt;{1} ({2})&gt;. Actual:&lt;{3} ({4})&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualFailMsg">
            <summary>
              Looks up a localized string similar to Expected:&lt;{1}&gt;. Actual:&lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualDeltaFailMsg">
            <summary>
              Looks up a localized string similar to Expected a difference greater than &lt;{3}&gt; between expected value &lt;{1}&gt; and actual value &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualFailMsg">
            <summary>
              Looks up a localized string similar to Expected any value except:&lt;{1}&gt;. Actual:&lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreSameGivenValues">
            <summary>
              Looks up a localized string similar to Do not pass value types to AreSame(). Values converted to Object will never be the same. Consider using AreEqual(). {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AssertionFailed">
            <summary>
              Looks up a localized string similar to {0} failed. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AsyncUITestMethodNotSupported">
            <summary>
              Looks up a localized string similar to async TestMethod with UITestMethodAttribute are not supported. Either remove async or use TestMethodAttribute..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsEmpty">
            <summary>
              Looks up a localized string similar to Both collections are empty. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameElements">
            <summary>
              Looks up a localized string similar to Both collection contain same elements..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameReference">
            <summary>
              Looks up a localized string similar to Both collection references point to the same collection object. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothSameElements">
            <summary>
              Looks up a localized string similar to Both collections contain the same elements. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.CollectionEqualReason">
            <summary>
              Looks up a localized string similar to {0}({1}).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_NullInMessages">
            <summary>
              Looks up a localized string similar to (null).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_ObjectString">
            <summary>
              Looks up a localized string similar to (object).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ContainsFail">
            <summary>
              Looks up a localized string similar to String &apos;{0}&apos; does not contain string &apos;{1}&apos;. {2}..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DataDrivenResultDisplayName">
            <summary>
              Looks up a localized string similar to {0} ({1}).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DoNotUseAssertEquals">
            <summary>
              Looks up a localized string similar to Assert.Equals should not be used for Assertions. Please use Assert.AreEqual &amp; overloads instead..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DynamicDataDisplayName">
            <summary>
              Looks up a localized string similar to Method {0} must match the expected signature: public static {1} {0}({2})..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DynamicDataIEnumerableEmpty">
            <summary>
              Looks up a localized string similar to Property or method {0} on {1} returns empty IEnumerable&lt;object[]&gt;..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DynamicDataIEnumerableNull">
            <summary>
              Looks up a localized string similar to Property or method {0} on {1} does not return IEnumerable&lt;object[]&gt;..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DynamicDataValueNull">
            <summary>
              Looks up a localized string similar to Value returned by property or method {0} shouldn&apos;t be null..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementNumbersDontMatch">
            <summary>
              Looks up a localized string similar to The number of elements in the collections do not match. Expected:&lt;{1}&gt;. Actual:&lt;{2}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementsAtIndexDontMatch">
            <summary>
              Looks up a localized string similar to Element at index {0} do not match..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch">
            <summary>
              Looks up a localized string similar to Element at index {1} is not of expected type. Expected type:&lt;{2}&gt;. Actual type:&lt;{3}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch2">
            <summary>
              Looks up a localized string similar to Element at index {1} is (null). Expected type:&lt;{2}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EndsWithFail">
            <summary>
              Looks up a localized string similar to String &apos;{0}&apos; does not end with string &apos;{1}&apos;. {2}..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EqualsTesterInvalidArgs">
            <summary>
              Looks up a localized string similar to Invalid argument- EqualsTester can&apos;t use nulls..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ErrorInvalidCast">
            <summary>
              Looks up a localized string similar to Cannot convert object of type {0} to {1}..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InternalObjectNotValid">
            <summary>
              Looks up a localized string similar to The internal object referenced is no longer valid..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidParameterToAssert">
            <summary>
              Looks up a localized string similar to The parameter &apos;{0}&apos; is invalid. {1}..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidPropertyType">
            <summary>
              Looks up a localized string similar to The property {0} has type {1}; expected type {2}..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsInstanceOfFailMsg">
            <summary>
              Looks up a localized string similar to {0} Expected type:&lt;{1}&gt;. Actual type:&lt;{2}&gt;..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsMatchFail">
            <summary>
              Looks up a localized string similar to String &apos;{0}&apos; does not match pattern &apos;{1}&apos;. {2}..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotInstanceOfFailMsg">
            <summary>
              Looks up a localized string similar to Wrong Type:&lt;{1}&gt;. Actual type:&lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotMatchFail">
            <summary>
              Looks up a localized string similar to String &apos;{0}&apos; matches pattern &apos;{1}&apos;. {2}..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoDataRow">
            <summary>
              Looks up a localized string similar to No test data source specified. Atleast one TestDataSource is required with DataTestMethodAttribute..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoExceptionThrown">
            <summary>
              Looks up a localized string similar to No exception thrown. {1} exception was expected. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NullParameterToAssert">
            <summary>
              Looks up a localized string similar to The parameter &apos;{0}&apos; is invalid. The value cannot be null. {1}..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NumberOfElementsDiff">
            <summary>
              Looks up a localized string similar to Different number of elements..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorConstructorNotFound">
            <summary>
              Looks up a localized string similar to 
                 The constructor with the specified signature could not be found. You might need to regenerate your private accessor,
                 or the member may be private and defined on a base class. If the latter is true, you need to pass the type
                 that defines the member into PrivateObject&apos;s constructor.
               .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorMemberNotFound">
            <summary>
              Looks up a localized string similar to 
                 The member specified ({0}) could not be found. You might need to regenerate your private accessor,
                 or the member may be private and defined on a base class. If the latter is true, you need to pass the type
                 that defines the member into PrivateObject&apos;s constructor.
               .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.StartsWithFail">
            <summary>
              Looks up a localized string similar to String &apos;{0}&apos; does not start with string &apos;{1}&apos;. {2}..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_ExpectedExceptionTypeMustDeriveFromException">
            <summary>
              Looks up a localized string similar to The expected exception type must be System.Exception or a type derived from System.Exception..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_FailedToGetExceptionMessage">
            <summary>
              Looks up a localized string similar to (Failed to get the message for an exception of type {0} due to an exception.).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoException">
            <summary>
              Looks up a localized string similar to Test method did not throw expected exception {0}. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoExceptionDefault">
            <summary>
              Looks up a localized string similar to Test method did not throw an exception. An exception was expected by attribute {0} defined on the test method..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongException">
            <summary>
              Looks up a localized string similar to Test method threw exception {0}, but exception {1} was expected. Exception message: {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongExceptionDerivedAllowed">
            <summary>
              Looks up a localized string similar to Test method threw exception {0}, but exception {1} or a type derived from it was expected. Exception message: {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.WrongExceptionThrown">
             <summary>
               Looks up a localized string similar to Threw exception {2}, but exception {1} was expected. {0}
            Exception Message: {3}
            Stack Trace: {4}.
             </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome">
            <summary>
            unit test outcomes
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Failed">
            <summary>
            Test was executed, but there were issues.
            Issues may involve exceptions or failed assertions.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Inconclusive">
            <summary>
            Test has completed, but we can't say if it passed or failed.
            May be used for aborted tests.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Passed">
            <summary>
            Test was executed without any issues.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.InProgress">
            <summary>
            Test is currently executing.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Error">
            <summary>
            There was a system error while we were trying to execute a test.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Timeout">
            <summary>
            The test timed out.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Aborted">
            <summary>
            Test was aborted by the user.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Unknown">
            <summary>
            Test is in an unknown state
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.NotRunnable">
            <summary>
            Test cannot be executed.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper">
            <summary>
            Provides helper functionality for the unit test framework
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper.GetExceptionMsg(System.Exception)">
            <summary>
            Gets the exception messages, including the messages for all inner exceptions
            recursively
            </summary>
            <param name="ex">Exception to get messages for</param>
            <returns>string with error message information</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout">
            <summary>
            Enumeration for timeouts, that can be used with the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> class.
            The type of the enumeration must match
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout.Infinite">
            <summary>
            The infinite.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.InheritanceBehavior">
            <summary>
            Enumeration for inheritance behavior, that can be used with both the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute"/> class
            and <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute"/> class.
            Defines the behavior of the ClassInitialize and ClassCleanup methods of base classes.
            The type of the enumeration must match
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.InheritanceBehavior.None">
            <summary>
            None.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.InheritanceBehavior.BeforeEachDerivedClass">
            <summary>
            Before each derived class.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute">
            <summary>
            The test class attribute.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute.GetTestMethodAttribute(Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute)">
            <summary>
            Gets a test method attribute that enables running this test.
            </summary>
            <param name="testMethodAttribute">The test method attribute instance defined on this method.</param>
            <returns>The <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/> to be used to run this test.</returns>
            <remarks>Extensions can override this method to customize how all methods in a class are run.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute">
            <summary>
            The test method attribute.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/> class.
            </summary>
            <param name="displayName">
            Display Name for the Test Window
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.DisplayName">
            <summary>
            Gets display Name for the Test Window
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Executes a test method.
            </summary>
            <param name="testMethod">The test method to execute.</param>
            <returns>An array of TestResult objects that represent the outcome(s) of the test.</returns>
            <remarks>Extensions can override this method to customize running a TestMethod.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute">
            <summary>
            Attribute for data driven test where data can be specified in-line.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestInitializeAttribute">
            <summary>
            The test initialize attribute.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCleanupAttribute">
            <summary>
            The test cleanup attribute marks methods that are executed after every test marked with a <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute">
            <summary>
            The ignore attribute.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute"/> class.
            </summary>
            <param name="message">
            Message specifies reason for ignoring.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute.IgnoreMessage">
            <summary>
            Gets the owner.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute">
            <summary>
            The test property attribute.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute"/> class.
            </summary>
            <param name="name">
            The name.
            </param>
            <param name="value">
            The value.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Name">
            <summary>
            Gets the name.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Value">
            <summary>
            Gets the value.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute">
            <summary>
            The class initialize attribute.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute"/> class.
            ClassInitializeAttribute
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute.#ctor(Microsoft.VisualStudio.TestTools.UnitTesting.InheritanceBehavior)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute"/> class.
            ClassInitializeAttribute
            </summary>
            <param name="inheritanceBehavior">
            Specifies the ClassInitialize Inheritance Behavior
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute.InheritanceBehavior">
            <summary>
            Gets the Inheritance Behavior
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute">
            <summary>
            The class cleanup attribute.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute"/> class.
            ClassCleanupAttribute
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute.#ctor(Microsoft.VisualStudio.TestTools.UnitTesting.InheritanceBehavior)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute"/> class.
            ClassCleanupAttribute
            </summary>
            <param name="inheritanceBehavior">
            Specifies the ClassCleanup Inheritance Behavior
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute.InheritanceBehavior">
            <summary>
            Gets the Inheritance Behavior
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyInitializeAttribute">
            <summary>
            The assembly initialize attribute.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyCleanupAttribute">
            <summary>
            The assembly cleanup attribute.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute">
            <summary>
            Test Owner
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute"/> class.
            </summary>
            <param name="owner">
            The owner.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.Owner">
            <summary>
            Gets the owner.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute">
            <summary>
            Priority attribute; used to specify the priority of a unit test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute"/> class.
            </summary>
            <param name="priority">
            The priority.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.Priority">
            <summary>
            Gets the priority.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute">
            <summary>
            Description of the test
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute"/> class to describe a test.
            </summary>
            <param name="description">The description.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.Description">
            <summary>
            Gets the description of a test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute">
            <summary>
            CSS Project Structure URI
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute"/> class for CSS Project Structure URI.
            </summary>
            <param name="cssProjectStructure">The CSS Project Structure URI.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.CssProjectStructure">
            <summary>
            Gets the CSS Project Structure URI.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute">
            <summary>
            CSS Iteration URI
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute"/> class for CSS Iteration URI.
            </summary>
            <param name="cssIteration">The CSS Iteration URI.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.CssIteration">
            <summary>
            Gets the CSS Iteration URI.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute">
            <summary>
            WorkItem attribute; used to specify a work item associated with this test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute"/> class for the WorkItem Attribute.
            </summary>
            <param name="id">The Id to a work item.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.Id">
            <summary>
            Gets the Id to a work item associated.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute">
            <summary>
            Timeout attribute; used to specify the timeout of a unit test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> class.
            </summary>
            <param name="timeout">
            The timeout in milliseconds.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> class with a preset timeout
            </summary>
            <param name="timeout">
            The timeout
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.Timeout">
            <summary>
            Gets the timeout in milliseconds.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult">
            <summary>
            TestResult object to be returned to adapter.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DisplayName">
            <summary>
            Gets or sets the display name of the result. Useful when returning multiple results.
            If null then Method name is used as DisplayName.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Outcome">
            <summary>
            Gets or sets the outcome of the test execution.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestFailureException">
            <summary>
            Gets or sets the exception thrown when test is failed.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogOutput">
            <summary>
            Gets or sets the output of the message logged by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogError">
            <summary>
            Gets or sets the output of the message logged by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DebugTrace">
            <summary>
            Gets or sets the debug traces by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestContextMessages">
            <summary>
            Gets or sets the debug traces by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ExecutionId">
            <summary>
            Gets or sets the execution id of the result.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ParentExecId">
            <summary>
            Gets or sets the parent execution id of the result.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.InnerResultsCount">
            <summary>
            Gets or sets the inner results count of the result.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Duration">
            <summary>
            Gets or sets the duration of test execution.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DatarowIndex">
            <summary>
            Gets or sets the data row index in data source. Set only for results of individual
            run of data row of a data driven test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ReturnValue">
            <summary>
            Gets or sets the return value of the test method. (Currently null always).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ResultFiles">
            <summary>
            Gets or sets the result files attached by the test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute">
            <summary>
            Specifies connection string, table name and row access method for data driven testing.
            </summary>
            <example>
            [DataSource("Provider=SQLOLEDB.1;Data Source=source;Integrated Security=SSPI;Initial Catalog=EqtCoverage;Persist Security Info=False", "MyTable")]
            [DataSource("dataSourceNameFromConfigFile")]
            </example>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultProviderName">
            <summary>
            The default provider name for DataSource.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultDataAccessMethod">
            <summary>
            The default data access method.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String,System.String,Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> class. This instance will be initialized with a data provider, connection string, data table and data access method to access the data source.
            </summary>
            <param name="providerInvariantName">Invariant data provider name, such as System.Data.SqlClient</param>
            <param name="connectionString">
            Data provider specific connection string.
            WARNING: The connection string can contain sensitive data (for example, a password).
            The connection string is stored in plain text in source code and in the compiled assembly.
            Restrict access to the source code and assembly to protect this sensitive information.
            </param>
            <param name="tableName">The name of the data table.</param>
            <param name="dataAccessMethod">Specifies the order to access data.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> class.This instance will be initialized with a connection string and table name.
            Specify connection string and data table to access OLEDB data source.
            </summary>
            <param name="connectionString">
            Data provider specific connection string.
            WARNING: The connection string can contain sensitive data (for example, a password).
            The connection string is stored in plain text in source code and in the compiled assembly.
            Restrict access to the source code and assembly to protect this sensitive information.
            </param>
            <param name="tableName">The name of the data table.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> class.  This instance will be initialized with a data provider and connection string associated with the setting name.
            </summary>
            <param name="dataSourceSettingName">The name of a data source found in the &lt;microsoft.visualstudio.qualitytools&gt; section in the app.config file.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ProviderInvariantName">
            <summary>
            Gets a value representing the data provider of the data source.
            </summary>
            <returns>
            The data provider name. If a data provider was not designated at object initialization, the default provider of System.Data.OleDb will be returned.
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ConnectionString">
            <summary>
            Gets a value representing the connection string for the data source.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.TableName">
            <summary>
            Gets a value indicating the table name providing data.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataAccessMethod">
             <summary>
             Gets the method used to access the data source.
             </summary>
            
             <returns>
             One of the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod"/> values. If the <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> is not initialized, this will return the default value <see cref="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random"/>.
             </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataSourceSettingName">
            <summary>
            Gets the name of a data source found in the &lt;microsoft.visualstudio.qualitytools&gt; section in the app.config file.
            </summary>
        </member>
    </members>
</doc>
