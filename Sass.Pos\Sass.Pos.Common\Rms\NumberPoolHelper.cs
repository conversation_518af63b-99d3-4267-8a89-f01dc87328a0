﻿using Saas.Pos.Common.Global.Configs.Rms;
using Saas.Pos.Common.Rms.NumberPool;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Saas.Pos.Common.Rms
{
    public class NumberPoolHelper
    {
        static object signLock = new object();
        static object indexLock = new object();
        static NumberPoolHelper _PoolService;
        static List<SystemConfigModel> ConfigList = new List<SystemConfigModel>();

        /// <summary>
        /// 初始化门店已经更新过的数据信息
        /// </summary>
        static Dictionary<int, List<KeyValuePair<string, DateTime>>> PublishedNumber = new Dictionary<int, List<KeyValuePair<string, DateTime>>>();

        public static NumberPoolHelper Single
        {
            get
            {
                if (_PoolService == null)
                {
                    lock (signLock)
                    {
                        if (_PoolService == null) _PoolService = new NumberPoolHelper();
                    }
                }
                return _PoolService;
            }
        }

        NumberPoolHelper()
        {
            InitData();

            //后台线程，每天删除过期数据
            Task.Factory.StartNew(() =>
            {
                while (true)
                {
                    var bookData = GetBookData();
                    //先删除，再创建
                    //File.Delete(GlobalConfig.Global.BookConfig.BookKey);

                    ////这里加一层判断是因为，防止在删除的时候有调用获取预约号，挤掉已经生成预约号的数据
                    //if (!File.Exists(GlobalConfig.Global.BookConfig.BookKey))
                    //    File.Create(GlobalConfig.Global.BookConfig.BookKey).Dispose();

                    //bookData.ForEach(w =>
                    //{
                    //    File.AppendAllLines(GlobalConfig.Global.BookConfig.BookKey, new string[] { w.ToJson() + "\n" });
                    //});

                    //每隔一天清理一次
                    Thread.Sleep(TimeSpan.FromHours(24));
                }
            }, TaskCreationOptions.LongRunning);
        }

        /// <summary>
        /// 获取预约号
        /// </summary>
        /// <param name="storeId">门店编号</param>
        /// <param name="bookDate">预约日期</param>
        /// <returns></returns>
        public string GenerateNumber(int storeId, DateTime bookDate)
        {
            Monitor.Enter(indexLock);//加锁
            try
            {
                var config = ConfigList.FirstOrDefault(w => w.ShopId == storeId);
                if (config == null)
                    throw new ExMessage("门店未配置生成预约号规则！");

                //实例化对象
                NumberPoolServiceBase service = null;
                switch (config.GenerateMode)
                {
                    case 1:
                        service = new NumberPoolDisOrderService(PublishedNumber[config.ShopId], config, bookDate.Date);
                        break;
                    case 2:
                        service = new NumberPoolOrderService(PublishedNumber[config.ShopId], config, bookDate.Date);
                        break;
                }
                if (service == null)
                    throw new Exception("未对接该种号码生成方式！");

                //获取规则下生成的编号
                var returnStr = service.GenerateNumber();
                InsertData(storeId, bookDate.Date, returnStr);
                return returnStr;
            }
            finally
            {
                Monitor.Exit(indexLock);//释放锁
            }
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitData()
        {
            //预约号记录数据
            var bookData = GetBookData();
            //配置持久化记录保存地址
            ConfigList = File.ReadAllText(GlobalConfig.Global.BookConfig.BookConfigPath).ToList<SystemConfigModel>();

            //初始化已生成的预约号数据
            ConfigList.ForEach(w =>
            {
                var shopBookData = bookData.Where(x => x.ShopId == w.ShopId && x.BookDate >= DateTime.Now.Date)
                                            .Select(x => new KeyValuePair<string, DateTime>(x.Number, x.BookDate)).ToList();

                PublishedNumber.Add(w.ShopId, shopBookData);
            });
        }

        /// <summary>
        /// 持久化数据
        /// </summary>
        /// <param name="storeId">门店编号</param>
        /// <param name="bookDate">预约日期</param>
        /// <param name="number">预约号</param>
        private void InsertData(int storeId, DateTime bookDate, string number)
        {
            //内存中添加
            PublishedNumber[storeId].Add(new KeyValuePair<string, DateTime>(number, bookDate));
            //本地记录中添加
            var data = new BookNumberModel()
            {
                ShopId = storeId,
                BookDate = bookDate,
                Number = number
            };
            //var jsonData = data.ToJson() + "\n";
            //if (!File.Exists(GlobalConfig.Global.BookConfig.BookKey))
            //    File.Create(GlobalConfig.Global.BookConfig.BookKey).Dispose();
            ////本地持久化
            //File.AppendAllLines(GlobalConfig.Global.BookConfig.BookKey, new string[] { jsonData });
        }

        /// <summary>
        /// 初始化预约数据
        /// </summary>
        private List<BookNumberModel> GetBookData()
        {
            var bookData = new List<BookNumberModel>();
            //如果存在本地记录就反序列化本地记录
            //if (File.Exists(GlobalConfig.Global.BookConfig.BookKey))
            //{
            //    var bookDataStr = File.ReadAllLines(GlobalConfig.Global.BookConfig.BookKey);
            //    foreach (var item in bookDataStr)
            //    {
            //        if (!string.IsNullOrEmpty(item))
            //        {
            //            var bookNumber = item.ToObject<BookNumberModel>();
            //            if (bookNumber.BookDate >= DateTime.Now.Date)
            //                bookData.Add(bookNumber);
            //        }
            //    }
            //}

            return bookData;
        }
    }
}
