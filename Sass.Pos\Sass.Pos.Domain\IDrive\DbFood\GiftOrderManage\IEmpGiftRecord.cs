﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.DbFood.GiftOrderManage
{
    public interface IEmpGiftRecord
    {
        /// <summary>
        /// 保存用户赠送记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<string> SaveGiftRecord(EmpGiftRecordContext context);

        /// <summary>
        /// 获取用户赠送记录列表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<RespPaginationModel<GetEmpGiftRecordModel>> GetGiftRecordList(GetEmpGiftRecordContext context);

        /// <summary>
        /// 获取用户赠送记录明细
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<GetEmpGiftRecordInfoModel> GetGiftRecordInfo(GetEmpGiftRecordInfoContext context);

        /// <summary>
        /// 撤销用户赠送记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<GiftOrderModel> CancelOrder(CancelOrderContext context);

        ResponseContext<RespPaginationModel<GetEmpGiftRecordModel>> GetGiftRecordListByStore(GetEmpGiftRecordContext context);

        ResponseContext<GetEmpGiftRecordInfoModel> GetGiftRecordInfoByStore(GetEmpGiftRecordInfoContext context);

        ResponseContext<GiftOrderModel> CancelOrderByStore(CancelOrderContext context);
    }
}
