﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.General;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IDrive.DbFood.NewFoodManage
{
    public interface INewFdTypeDrive
    {
        ResponseContext<RespPaginationModel<GetNewFdTypeDataModel>> GetNewFdTypeDataByStore(GetNewFdTypeDataContext context);

        ResponseContext<EditNewFdTypeDataModel> EditNewFdTypeDataByStore(EditNewFdTypeDataContext context);

        ResponseContext<RespPaginationModel<GetNewFdTypeDataModel>> GetNewFdTypeData(GetNewFdTypeDataContext context);

        ResponseContext<EditNewFdTypeDataModel> EditNewFdTypeData(EditNewFdTypeDataContext context);

    }
}
