﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IDrive.WineStockManage
{
    public interface IDrCheckManage
    {
        ResponseContext<SaveDrCheckDataModel> SaveDrCheckData(SaveDrCheckDataContext context);

        ResponseContext<RespPaginationModel<GetDrCheckDataModel>> GetDrCheckData(GetDrCheckDataContext context);

        ResponseContext<List<GetDrCheckDetailDataModel>> GetDrCheckDetailData(GetDrCheckDetailDataContext context);
    }
}
