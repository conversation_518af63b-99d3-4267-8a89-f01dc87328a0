﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    public interface IEmpGiftRecordService
    {
        [OperationContract]
        ResponseContext<RespPaginationModel<GetEmpGiftRecordModel>> GetGiftRecordList(GetEmpGiftRecordContext context);

        [OperationContract]
        ResponseContext<GetEmpGiftRecordInfoModel> GetGiftRecordInfo(GetEmpGiftRecordInfoContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetEmpGiftRecordModel>> GetGiftRecordListByStore(GetEmpGiftRecordContext context);

        [OperationContract]
        ResponseContext<GetEmpGiftRecordInfoModel> GetGiftRecordInfoByStore(GetEmpGiftRecordInfoContext context);
    }
}
