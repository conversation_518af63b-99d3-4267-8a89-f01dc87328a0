﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Global.Configs.Comm
{
    public class CommConfig
    {
        public CommConfig()
        {
            //如果全局文件资源不存在，就创建
            if (!Directory.Exists(ResourcePath))
                Directory.CreateDirectory(ResourcePath);
        }

        /// <summary>
        /// 公共文件夹保存地址
        /// </summary>
        public string ResourcePath = AppDomain.CurrentDomain.BaseDirectory + "\\Resource";

        /// <summary>
        /// 卡券券码加密密钥
        /// </summary>
        public string CouponSecretKey = "HypohHkpEf1MrA4z";
    }
}
