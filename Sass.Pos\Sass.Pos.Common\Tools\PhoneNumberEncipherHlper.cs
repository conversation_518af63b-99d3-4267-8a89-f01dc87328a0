﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Tools
{
    public class PhoneNumberEncipherHlper
    {
        public static string EncryptPhoneNumber(string phoneNumber)
        {
            if (phoneNumber.Length < 7)
            {
                return phoneNumber; // 如果电话号码长度小于等于6，不加密
            }

            int startIndex = 3;
            int length = phoneNumber.Length - 7;

            // 使用 * 替换中间部分的数字
            string encryptedPart = new String('*', length);

            // 将加密后的部分替换原始电话号码的中间部分
            string encryptedPhoneNumber = phoneNumber.Substring(0, startIndex) + encryptedPart + phoneNumber.Substring(startIndex + length);

            return encryptedPhoneNumber;
        }

    }
}
