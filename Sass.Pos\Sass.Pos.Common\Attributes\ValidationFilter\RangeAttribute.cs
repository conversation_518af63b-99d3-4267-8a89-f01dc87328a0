﻿using Saas.Pos.Common.Attributes.ValidationFilter.Context;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Attributes.ValidationFilter
{
    /// <summary>
    /// 范围校验
    /// </summary>
    public class RangeAttribute : ValidationFilterAttribute
    {
        public object Minimum { get; private set; }
        public object Maximum { get; private set; }
        public Type OperandType { get; private set; }
        private Func<object, object> Conversion { get; set; }

        public RangeAttribute(int minimum, int maximum)
        {
            Minimum = minimum;
            Maximum = maximum;
            OperandType = typeof(int);
        }

        public RangeAttribute(double minimum, double maximum)
        {
            Minimum = minimum;
            Maximum = maximum;
            OperandType = typeof(double);
        }

        public RangeAttribute(Type type, string minimum, string maximum)
        {
            Minimum = minimum;
            Maximum = maximum;
            OperandType = type;
        }

        public override void Inspect(object currentValue, ValidationContext context)
        {
            SetupConversion();
            string text = currentValue as string;
            if (text != null && string.IsNullOrEmpty(text))
                throw new Exception(string.Format(AppInterface.dat.lang["RangeMsg2"], context.PropertyName));

            object obj = null;
            try
            {
                obj = Conversion(currentValue);
            }
            catch (Exception ex)
            {
                throw new Exception("值转换失败！" + ex.Message);
            }

            IComparable comparable = (IComparable)Minimum;
            IComparable comparable2 = (IComparable)Maximum;
            if (comparable.CompareTo(obj) > 0 || comparable2.CompareTo(obj) < 0)
                throw new Exception(string.IsNullOrEmpty(ErrorMessage) ? string.Format(AppInterface.dat.lang["RangeMsg1"], context.PropertyName) : ErrorMessage);
        }

        private void SetupConversion()
        {
            if (Conversion != null)
                return;

            object minimum = Minimum;
            object maximum = Maximum;
            if (minimum == null || maximum == null)
                throw new InvalidOperationException("比较值范围填写无效！");

            Type type2 = minimum.GetType();
            if (type2 == typeof(int))
            {
                Initialize((int)minimum, (int)maximum, (object v) => Convert.ToInt32(v, CultureInfo.InvariantCulture));
                return;
            }

            if (type2 == typeof(double))
            {
                Initialize((double)minimum, (double)maximum, (object v) => Convert.ToDouble(v, CultureInfo.InvariantCulture));
                return;
            }

            Type type = OperandType;
            TypeConverter converter = TypeDescriptor.GetConverter(type);
            IComparable minimum2 = (IComparable)converter.ConvertFromString((string)minimum);
            IComparable maximum2 = (IComparable)converter.ConvertFromString((string)maximum);
            Func<object, object> conversion = (object value) => (value == null || !(value.GetType() == type)) ? converter.ConvertFrom(value) : value;
            Initialize(minimum2, maximum2, conversion);
        }

        private void Initialize(IComparable minimum, IComparable maximum, Func<object, object> conversion)
        {
            Minimum = minimum;
            Maximum = maximum;
            Conversion = conversion;
        }
    }
}
