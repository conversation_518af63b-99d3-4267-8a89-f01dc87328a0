﻿using ComponentApplicationServiceInterface.Context.Response;
using Newtonsoft.Json;
using Saas.Pos.Application.Lib.Bar;
using Saas.Pos.Appliction.Lib.Bar;
using Saas.Pos.Common;
using Saas.Pos.Common.Log;
using Saas.Pos.Common.Tools;
using Saas.Pos.Drive.Bar.WineStockManage.SmsPushMode;
using Saas.Pos.Drive.Common.Proxy.Middleware;
using Saas.Pos.Model.Bar.Model;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.Bar.WineStockManage
{
    /// <summary>
    /// 存酒管理
    /// </summary>
    public class WineStockDrive : WineStockManageDriveBase
    {
        /// <summary>
        /// 查询需要发送短信的存酒用户信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetWineStockData> GetWineStockData(List<custData> context)
        {
            try
            {
                //获取存酒数据
                var custWineData = context;

                var fristNoDate = DateTime.Now.AddDays(-55);//第一次不需要发送短信时间

                var custWineExData = custWineData.GroupBy(i => i.ReNew).Select(i => new GetWineStockData()
                {
                    ReNew = i.Key,
                    UserData = i.GroupBy(k => k.iKeyMsg).Select(n => new UserList()
                    {

                        CustName = n.FirstOrDefault().CustName,
                        CustTel = n.FirstOrDefault().CustTel,
                        DeDatetime = n.FirstOrDefault().DeDatetime,
                        DeShopId = n.FirstOrDefault().DeShopId,
                        ShopName = n.FirstOrDefault().ShopName,
                        iKeyMsg = n.FirstOrDefault().iKeyMsg,
                        DrinksData = n.Select(j => new DrinksData()
                        {
                            DrinksName = j.DrinksName,
                            DrinksQty = j.DrinksQty,
                            Unit = j.Unit
                        }).ToList()
                    }).ToList()
                }).ToList();

                //将第一次存酒到期前五天的数据去除
                foreach (var item in custWineExData)
                {
                    if (item.ReNew == 0 || item.ReNew == 2)
                        item.UserData = item.UserData.Where(i => i.DeDatetime > fristNoDate).ToList();
                }

                return custWineExData;
            }
            catch (Exception ex)
            {
                LogHelper.Info(DateTime.Now + "存酒管理->查询存酒用户信息\n参数:无参数\n错误原因:" + ex.Message);
                throw new ExMessage(ex.Message);
            }
        }

        /// <summary>
        /// 发送短信
        /// </summary>
        public int SendMessage(List<GetWineStockData> context)
        {
            try
            {
                if (context.Count < 0)
                    return 0;
                var reqCount = 0;//获取成功数量
                var totalCount = 0;//获取总数量
                var custWineData = context;
                var nowDate = DateTime.Now;

                foreach (var item in custWineData)
                {
                    try
                    {
                        totalCount += item.UserData.Count();
                        //模式
                        var basePush = SmsPushModeFactory.SendMessage((int)item.ReNew);
                        reqCount += basePush.SendMessage(item);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.TaskInfo("SendMessage", "发送短信异常(次数)\n参数:" + JsonConvert.SerializeObject(item) + "\n原因" + ex.Message, 2);
                    }
                }



                if (reqCount != totalCount)
                {
                    var messageContext = new PhoneSendMessageContext();
                    messageContext.No = 0;
                    messageContext.Title = "1";
                    messageContext.ReqType = 1;
                    messageContext.PhoneNumber = "13420870324";
                    messageContext.Message = "短信发送失败,详情请查看日志!";
                    MessageSendHelper.PhoneSendMessage(messageContext);
                }

                return reqCount;
            }
            catch (Exception ex)
            {
                LogHelper.TaskInfo("SendMessage", DateTime.Now + "存酒管理->发送短信\n参数:无参数\n错误原因:" + ex.Message, 2);
                throw new ExMessage(ex.Message);
            }
        }

        /// <summary>
        /// 定时发送短信
        /// </summary>
        public int CheckSendMessage()
        {
            var count = 0;
            WineStockApp w = new WineStockApp();

            LogHelper.Info(DateTime.Now + "存酒管理-发送短信:开始发送!");
            try
            {
                //需要发送短信数据(小于30天大于15天)
                var stockData = w.GetWineStockData();
                if (stockData.Count > 0)
                {
                    //处理后的存酒数据
                    var custData = GetWineStockData(stockData);
                    count += SendMessage(custData);//发送短信
                }


                //需要发送短信数据(小于15天大于0天)
                var stockDataEx = w.GetWineStockDataEx();
                if (stockDataEx.Count > 0)
                {
                    //处理后的存酒数据
                    var custData = GetWineStockData(stockDataEx);
                    count += SendMessage(custData);//发送短信
                }
            }
            catch (Exception ex)
            {
                LogHelper.Info(DateTime.Now + "存酒管理-发送短信\n错误原因:" + ex.Message);
            }
            LogHelper.Info(DateTime.Now + "存酒管理-发送短信:发送完毕!条数:" + count);

            return count;

        }
    }
}
