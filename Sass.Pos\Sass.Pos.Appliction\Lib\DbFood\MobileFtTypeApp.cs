﻿using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
    public partial class MobileFtTypeApp : AppBase<MobileFtType>
    {
        public List<GetOrderMenuData> GetOrderMenuData()
        {
            return Repository.MobileFtType.GetOrderMenuData();
        }

        public List<FdDataDetail> GetOrderMenuDataDetail(GetOrderMenuDataDetailContext context)
        {
            return Repository.MobileFtType.GetOrderMenuDataDetail(context);
        }

        public List<FdData> GetOrderMenuDataByFdName(GetDataByFdNameContext context)
        {
            return Repository.MobileFtType.GetOrderMenuDataByFdName(context);
        }

        public List<AddItemContext> GetAddItemData(GetAddItemContext context)
        {
            return Repository.MobileFtType.GetAddItemData(context);
        }

        public List<PackageFdInfo> GetPackageItem(List<string> packageFdNo)
        {
            return Repository.MobileFtType.GetPackageItem(packageFdNo);
        }
    }
}
