﻿using ComponentApplicationServiceInterface.Context.Response;
using external.open.library.TakeaWayPlatform.Model;
using Saas.Pos.Model.Coupons.Context;
using Saas.Pos.Model.Coupons.Model;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService
{
    [ServiceContract]
    public interface IOpenPlatformService
    {
        [OperationContract]
        ResponseContext<CouponsVerifyModel> Verify(CouponsVerifyContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetOpenPlatformProductModel>> GetProducts(GetOpenPlatformProductContext context);

        [OperationContract]
        ResponseContext<GetCustomCouponInfoModel> GetCustomCouponInfo(GetCustomCouponContext context);

        [OperationContract]
        ResponseContext<Model.Coupons.Model.ReverseConsumeModel> ReverseConsume(ReverseConsumeContext context);

        [OperationContract]
        ResponseContext<QuickOrderModel> QuickOrder(QuickOrderContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetRecordListModel>> GetRecordList(GetRecordContext context);

        [OperationContract]
        ResponseContext<List<GetRecordItemModel>> GetRecordItem(GetRecordItemContext context);

        [OperationContract]
        ResponseContext<bool> ConfirmNumber(ConfirmNumberContext context);

        [OperationContract]
        ResponseContext<GetNumberModel> GetNumbers(GetNumberContext context);

        [OperationContract]
        ResponseContext<List<GetVerifedInfoModel>> GetVerifedInfo(GetVerifedInfoContext context);

        [OperationContract]
        ResponseContext<List<KeyValueCommonModel>> GetPlatformEnumList(GetRecordPlatformContext context);

        [OperationContract]
        ResponseContext<GetRecordDetailModel> GetVerifyItem(GetVerifyItemContext context);

        [OperationContract]
        ResponseContext<List<List<string>>> ExportRecord(GetRecordContext context);

        [OperationContract]
        ResponseContext<List<List<string>>> ExportVerifyItem(GetRecordContext context);

        [OperationContract]
        ResponseContext<ReceiveModel> Receive(ReceiveContext context);
    }
}
