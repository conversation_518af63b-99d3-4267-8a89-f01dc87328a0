﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IDrive.OrderManage
{
    public interface ICommissionSchemes
    {
        /// <summary>
        /// 保存提成方案
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<ReturnBool> SaveSchemes(SaveCommissionSchemesContext context);

        /// <summary>
        /// 查询提成方案
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<RespPaginationModel<GetCommissionSchemesModel>> GetSchemesList(GetSchemesListContext context);
    }
}
