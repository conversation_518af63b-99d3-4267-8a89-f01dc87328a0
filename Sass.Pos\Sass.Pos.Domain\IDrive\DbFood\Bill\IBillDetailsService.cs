﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.DbFood.Bill
{
    public interface IBillDetailsService
    {
        ResponseContext<GetBillHeadModel> GetBill_Head(GetBillDataContext context);

        ResponseContext<List<GetBillDetailsModel>> GetBill_Details(GetBillDataContext context);

        ResponseContext<RmHeadData> GetHYBill_Head(GetBillDataContext context);

        ResponseContext<RmHeadData> GetHYBill_HeadByStore(GetBillDataContext context);

        ResponseContext<List<RmDetailData>> GetHYBill_Details(GetBillDataContext context);

        ResponseContext<List<RmDetailData>> GetHYBill_DetailsByStore(GetBillDataContext context);

        ResponseContext<RmHeadData> GetLSBill_Head(GetHistoryBillDataContext context);

        ResponseContext<RmHeadData> GetLSBill_HeadByStore(GetHistoryBillDataContext context);

        ResponseContext<List<RmDetailData>> GetLSBill_Details(GetHistoryBillDataContext context);

        ResponseContext<List<RmDetailData>> GetLSBill_DetailsByStore(GetHistoryBillDataContext context);
    }
}
