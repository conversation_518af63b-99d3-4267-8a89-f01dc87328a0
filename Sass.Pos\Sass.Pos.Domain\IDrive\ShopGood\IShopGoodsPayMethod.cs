﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive
{
    public interface  IShopGoodsPayMethod
    {
        ResponseContext<ReturnInt> SaveShopSkuPayMethod(SaveSkuPayMethodContext context);

        ResponseContext<ReturnInt> ChangeMethodStatus(ChangeStatusContext context);

        ResponseContext<ReturnInt> DeleteShopPayMethod(DeleteShopPayMethodContext context);

        ResponseContext<RespPaginationModel<Shop_GoodsPayMethodExModel>> GetShopSkuPayMethodList(GetShopPayMethodsListContext context);

        ResponseContext<Shop_GoodsPayMethod> GetShopSkuPayMethodInfo(GetShopPayMethodsInfoContext context);
    }
}
