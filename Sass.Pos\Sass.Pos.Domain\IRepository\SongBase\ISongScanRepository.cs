﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.SongBase;
using Saas.Pos.Model.SongBase.Context;
using Saas.Pos.Model.SongBase.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IRepository.SongBase
{
    public partial interface ISongScanRepository : IRepositoryBase<SongScan>
    {
        List<GetSongScanModel> GetSongScanList(GetSongScanContext context);
    }
}
