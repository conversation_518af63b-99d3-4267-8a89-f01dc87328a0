<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod">
            <summary>
            TestMethod для выполнения.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestMethodName">
            <summary>
            Получает имя метода теста.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestClassName">
            <summary>
            Получает имя тестового класса.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ReturnType">
            <summary>
            Получает тип возвращаемого значения метода теста.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ParameterTypes">
            <summary>
            Получает параметры метода теста.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.MethodInfo">
            <summary>
            Получает methodInfo для метода теста.
            </summary>
            <remarks>
            This is just to retrieve additional information about the method.
            Do not directly invoke the method using MethodInfo. Use ITestMethod.Invoke instead.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.Invoke(System.Object[])">
            <summary>
            Вызывает метод теста.
            </summary>
            <param name="arguments">
            Аргументы, передаваемые методу теста (например, для управляемых данными тестов).
            </param>
            <returns>
            Результат вызова метода теста.
            </returns>
            <remarks>
            This call handles asynchronous test methods as well.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAllAttributes(System.Boolean)">
            <summary>
            Получить все атрибуты метода теста.
            </summary>
            <param name="inherit">
            Допустим ли атрибут, определенный в родительском классе.
            </param>
            <returns>
            Все атрибуты.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAttributes``1(System.Boolean)">
            <summary>
            Получить атрибут указанного типа.
            </summary>
            <typeparam name="AttributeType"> System.Attribute type. </typeparam>
            <param name="inherit">
            Допустим ли атрибут, определенный в родительском классе.
            </param>
            <returns>
            Атрибуты указанного типа.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Helper">
            <summary>
            Вспомогательный метод.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNull(System.Object,System.String,System.String)">
            <summary>
            Параметр проверки не имеет значения NULL.
            </summary>
            <param name="param">
            Параметр.
            </param>
            <param name="parameterName">
            Имя параметра.
            </param>
            <param name="message">
            Сообщение.
            </param>
            <exception cref="T:System.ArgumentNullException"> Throws argument null exception when parameter is null. </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNullOrEmpty(System.String,System.String,System.String)">
            <summary>
            Параметр проверки не равен NULL или не пуст.
            </summary>
            <param name="param">
            Параметр.
            </param>
            <param name="parameterName">
            Имя параметра.
            </param>
            <param name="message">
            Сообщение.
            </param>
            <exception cref="T:System.ArgumentException"> Throws ArgumentException when parameter is null. </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod">
            <summary>
            Перечисление, описывающее способ доступа к строкам данных в тестах, управляемых данными.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Sequential">
            <summary>
            Строки возвращаются в последовательном порядке.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random">
            <summary>
            Строки возвращаются в случайном порядке.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute">
            <summary>
            Атрибут для определения встроенных данных для метода теста.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> .
            </summary>
            <param name="data1"> Объект данных. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object,System.Object[])">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> , принимающий массив аргументов.
            </summary>
            <param name="data1"> Объект данных. </param>
            <param name="moreData"> Дополнительные данные. </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.Data">
            <summary>
            Получает данные для вызова метода теста.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.DisplayName">
            <summary>
            Получает или задает отображаемое имя в результатах теста для настройки.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            <summary>
            Исключение утверждения с неопределенным результатом.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String,System.Exception)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
            <param name="msg"> Сообщение. </param>
            <param name="ex"> Исключение. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
            <param name="msg"> Сообщение. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException">
            <summary>
            Класс InternalTestFailureException. Используется для указания внутреннего сбоя для тестового случая
            </summary>
            <remarks>
            This class is only added to preserve source compatibility with the V1 framework.
            For all practical purposes either use AssertFailedException/AssertInconclusiveException.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String,System.Exception)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
            <param name="msg"> Сообщение об исключении. </param>
            <param name="ex"> Исключение. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
            <param name="msg"> Сообщение об исключении. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute">
            <summary>
            Атрибут, который указывает, что ожидается исключение указанного типа
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> ожидаемого типа
            </summary>
            <param name="exceptionType">Тип ожидаемого исключения</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type,System.String)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> 
            ожидаемого типа c сообщением для включения, когда тест не создает исключение.
            </summary>
            <param name="exceptionType">Тип ожидаемого исключения</param>
            <param name="noExceptionMessage">
            Сообщение для включения в результат теста, если тест не был пройден из-за того, что не создал исключение
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.ExceptionType">
            <summary>
            Получает значение, указывающее тип ожидаемого исключения
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.AllowDerivedTypes">
            <summary>
            Получает или задает значение, которое означает, являются ли ожидаемыми типы, производные
            от типа ожидаемого исключения
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.NoExceptionMessage">
            <summary>
            Получает сообщение, включаемое в результаты теста, если он не пройден из-за того, что не возникло исключение
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.Verify(System.Exception)">
            <summary>
            Проверяет, является ли ожидаемым тип исключения, созданного модульным тестом
            </summary>
            <param name="exception">Исключение, созданное модульным тестом</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute">
            <summary>
            Базовый класс для атрибутов, которые указывают ожидать исключения из модульного теста
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> с сообщением об отсутствии исключений по умолчанию
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor(System.String)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> с сообщением об отсутствии исключений
            </summary>
            <param name="noExceptionMessage">
            Сообщение для включения в результат теста, если тест не был пройден из-за того, что не создал
            исключение
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.NoExceptionMessage">
            <summary>
            Получает сообщение, включаемое в результаты теста, если он не пройден из-за того, что не возникло исключение
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.SpecifiedNoExceptionMessage">
            <summary>
            Получает сообщение, включаемое в результаты теста, если он не пройден из-за того, что не возникло исключение
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.GetDefaultNoExceptionMessage(System.String)">
            <summary>
            Получает сообщение по умолчанию об отсутствии исключений
            </summary>
            <param name="expectedExceptionAttributeTypeName">Название типа для атрибута ExpectedException</param>
            <returns>Сообщение об отсутствии исключений по умолчанию</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.Verify(System.Exception)">
            <summary>
            Определяет, ожидается ли исключение. Если метод возвращает управление, то
            считается, что ожидалось исключение. Если метод создает исключение, то
            считается, что исключение не ожидалось, и сообщение созданного исключения
            включается в результат теста. Для удобства можно использовать класс <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert"/>. 
            Если используется <see cref="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive"/> и утверждение завершается с ошибкой,
            то результат теста будет неопределенным.
            </summary>
            <param name="exception">Исключение, созданное модульным тестом</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.RethrowIfAssertException(System.Exception)">
            <summary>
            Повторно создать исключение при возникновении исключения AssertFailedException или AssertInconclusiveException
            </summary>
            <param name="exception">Исключение, которое необходимо создать повторно, если это исключение утверждения</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper">
            <summary>
            Этот класс предназначен для пользователей, выполняющих модульное тестирование для универсальных типов.
            GenericParameterHelper удовлетворяет некоторым распространенным ограничениям для универсальных типов,
             например.
            1. Открытый конструктор по умолчанию
            2. Реализует общий интерфейс: IComparable, IEnumerable
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> , который
            удовлетворяет ограничению newable в универсальных типах C#.
            </summary>
            <remarks>
            This constructor initializes the Data property to a random value.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor(System.Int32)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> , который
            инициализирует свойство Data в указанное пользователем значение.
            </summary>
            <param name="data">Любое целочисленное значение</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Data">
            <summary>
            Получает или задает данные
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Equals(System.Object)">
            <summary>
            Сравнить значения двух объектов GenericParameterHelper
            </summary>
            <param name="obj">объект, с которым будет выполнено сравнение</param>
            <returns>True, если obj имеет то же значение, что и объект "this" GenericParameterHelper.
            В противном случае False.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetHashCode">
            <summary>
            Возвращает хэш-код для этого объекта.
            </summary>
            <returns>Хэш-код.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.CompareTo(System.Object)">
            <summary>
            Сравнивает данные двух объектов <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </summary>
            <param name="obj">Объект для сравнения.</param>
            <returns>
            Число со знаком, указывающее относительные значения этого экземпляра и значения.
            </returns>
            <exception cref="T:System.NotSupportedException">
            Thrown when the object passed in is not an instance of <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetEnumerator">
            <summary>
            Возвращает объект IEnumerator, длина которого является производной
            от свойства Data.
            </summary>
            <returns>Объект IEnumerator</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Clone">
            <summary>
            Возвращает объект GenericParameterHelper, равный
            текущему объекту.
            </summary>
            <returns>Клонированный объект.</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger">
            <summary>
            Позволяет пользователям регистрировать/записывать трассировки от модульных тестов для диагностики.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessageHandler">
            <summary>
            Обработчик LogMessage.
            </summary>
            <param name="message">Сообщение для записи в журнал.</param>
        </member>
        <member name="E:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.OnLogMessage">
            <summary>
            Прослушиваемое событие. Возникает, когда средство записи модульных тестов записывает сообщение.
            Главным образом используется адаптером.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessage(System.String,System.Object[])">
            <summary>
            API, при помощи которого средство записи теста будет обращаться к сообщениям журнала.
            </summary>
            <param name="format">Строка формата с заполнителями.</param>
            <param name="args">Параметры для заполнителей.</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute">
            <summary>
            Атрибут TestCategory; используется для указания категории модульного теста.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.#ctor(System.String)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute"/> и применяет категорию к тесту.
            </summary>
            <param name="testCategory">
            Категория теста.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.TestCategories">
            <summary>
            Возвращает или задает категории теста, которые были применены к тесту.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute">
            <summary>
            Базовый класс для атрибута Category
            </summary>
            <remarks>
            The reason for this attribute is to let the users create their own implementation of test categories.
            - test framework (discovery, etc) deals with TestCategoryBaseAttribute.
            - The reason that TestCategories property is a collection rather than a string,
              is to give more flexibility to the user. For instance the implementation may be based on enums for which the values can be OR'ed
              in which case it makes sense to have single attribute rather than multiple ones on the same test.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.#ctor">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute"/> .
            Применяет к тесту категорию. Строки, возвращаемые TestCategories ,
            используются с командой /category для фильтрации тестов
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.TestCategories">
            <summary>
            Возвращает или задает категорию теста, которая была применена к тесту.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            <summary>
            Класс AssertFailedException. Используется для указания сбоя тестового случая
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
            <param name="msg"> Сообщение. </param>
            <param name="ex"> Исключение. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
            <param name="msg"> Сообщение. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert">
            <summary>
            Коллекция вспомогательных классов для тестирования различных условий в
            модульных тестах. Если проверяемое условие
            ложно, создается исключение.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.That">
            <summary>
            Получает одноэлементный экземпляр функции Assert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void IsOfType&lt;T&gt;(this Assert assert, object obj)"
            Users could then use a syntax similar to the default assertions which in this case is "Assert.That.IsOfType&lt;Dog&gt;(animal);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean)">
            <summary>
            Проверяет, является ли указанное условие истинным, и создает исключение,
            если условие ложно.
            </summary>
            <param name="condition">
            Условие, которое должно быть истинным с точки зрения теста.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String)">
            <summary>
            Проверяет, является ли указанное условие истинным, и создает исключение,
            если условие ложно.
            </summary>
            <param name="condition">
            Условие, которое должно быть истинным с точки зрения теста.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="condition"/>
            имеет значение False. Сообщение отображается в результатах теста.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String,System.Object[])">
            <summary>
            Проверяет, является ли указанное условие истинным, и создает исключение,
            если условие ложно.
            </summary>
            <param name="condition">
            Условие, которое должно быть истинным с точки зрения теста.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="condition"/>
            имеет значение False. Сообщение отображается в результатах теста.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean)">
            <summary>
            Проверяет, является ли указанное условие ложным, и создает исключение,
            если условие истинно.
            </summary>
            <param name="condition">
            Условие, которое с точки зрения теста должно быть ложным.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String)">
            <summary>
            Проверяет, является ли указанное условие ложным, и создает исключение,
            если условие истинно.
            </summary>
            <param name="condition">
            Условие, которое с точки зрения теста должно быть ложным.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="condition"/>
            имеет значение True. Сообщение отображается в результатах теста.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String,System.Object[])">
            <summary>
            Проверяет, является ли указанное условие ложным, и создает исключение,
            если условие истинно.
            </summary>
            <param name="condition">
            Условие, которое с точки зрения теста должно быть ложным.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="condition"/>
            имеет значение True. Сообщение отображается в результатах теста.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object)">
            <summary>
            Проверяет, имеет ли указанный объект значение NULL, и создает исключение,
            если он не равен NULL.
            </summary>
            <param name="value">
            Объект, который с точки зрения теста должен быть равен NULL.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String)">
            <summary>
            Проверяет, имеет ли указанный объект значение NULL, и создает исключение,
            если он не равен NULL.
            </summary>
            <param name="value">
            Объект, который с точки зрения теста должен быть равен NULL.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="value"/>
            имеет значение, отличное от NULL. Сообщение отображается в результатах теста.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String,System.Object[])">
            <summary>
            Проверяет, имеет ли указанный объект значение NULL, и создает исключение,
            если он не равен NULL.
            </summary>
            <param name="value">
            Объект, который с точки зрения теста должен быть равен NULL.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="value"/>
            имеет значение, отличное от NULL. Сообщение отображается в результатах теста.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object)">
            <summary>
            Проверяет, имеет ли указанный объект значение NULL, и создает исключение,
            если он равен NULL.
            </summary>
            <param name="value">
            Объект, который не должен быть равен NULL.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String)">
            <summary>
            Проверяет, имеет ли указанный объект значение NULL, и создает исключение,
            если он равен NULL.
            </summary>
            <param name="value">
            Объект, который не должен быть равен NULL.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="value"/>
            имеет значение NULL. Сообщение отображается в результатах теста.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String,System.Object[])">
            <summary>
            Проверяет, имеет ли указанный объект значение NULL, и создает исключение,
            если он равен NULL.
            </summary>
            <param name="value">
            Объект, который не должен быть равен NULL.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="value"/>
            имеет значение NULL. Сообщение отображается в результатах теста.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object)">
            <summary>
            Проверяет, ссылаются ли указанные объекты на один и тот же объект, и
            создает исключение, если два входных значения не ссылаются на один и тот же объект.
            </summary>
            <param name="expected">
            Первый сравниваемый объект. Это — ожидаемое тестом значение.
            </param>
            <param name="actual">
            Второй сравниваемый объект. Это — значение, созданное тестируемым кодом.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String)">
            <summary>
            Проверяет, ссылаются ли указанные объекты на один и тот же объект, и
            создает исключение, если два входных значения не ссылаются на один и тот же объект.
            </summary>
            <param name="expected">
            Первый сравниваемый объект. Это — ожидаемое тестом значение.
            </param>
            <param name="actual">
            Второй сравниваемый объект. Это — значение, созданное тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            не равен <paramref name="expected"/>. Сообщение отображается
            в результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Проверяет, ссылаются ли указанные объекты на один и тот же объект, и
            создает исключение, если два входных значения не ссылаются на один и тот же объект.
            </summary>
            <param name="expected">
            Первый сравниваемый объект. Это — ожидаемое тестом значение.
            </param>
            <param name="actual">
            Второй сравниваемый объект. Это — значение, созданное тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            не равен <paramref name="expected"/>. Сообщение отображается
            в результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object)">
            <summary>
            Проверяет, ссылаются ли указанные объекты на разные объекты, и
            создает исключение, если два входных значения ссылаются на один и тот же объект.
            </summary>
            <param name="notExpected">
            Первый сравниваемый объект. Это — значение, которое с точки зрения теста не должно
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Второй сравниваемый объект. Это — значение, созданное тестируемым кодом.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String)">
            <summary>
            Проверяет, ссылаются ли указанные объекты на разные объекты, и
            создает исключение, если два входных значения ссылаются на один и тот же объект.
            </summary>
            <param name="notExpected">
            Первый сравниваемый объект. Это — значение, которое с точки зрения теста не должно
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Второй сравниваемый объект. Это — значение, созданное тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Проверяет, ссылаются ли указанные объекты на разные объекты, и
            создает исключение, если два входных значения ссылаются на один и тот же объект.
            </summary>
            <param name="notExpected">
            Первый сравниваемый объект. Это — значение, которое с точки зрения теста не должно
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Второй сравниваемый объект. Это — значение, созданное тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0)">
            <summary>
            Проверяет указанные значения на равенство и создает исключение,
            если два значения не равны. Различные числовые типы
            считаются неравными, даже если логические значения равны. Например, 42L не равно 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Первое сравниваемое значение. Это — ожидаемое тестом значение.
            </param>
            <param name="actual">
            Второе сравниваемое значение. Это — значение, созданное тестируемым кодом.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String)">
            <summary>
            Проверяет указанные значения на равенство и создает исключение,
            если два значения не равны. Различные числовые типы
            считаются неравными, даже если логические значения равны. Например, 42L не равно 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Первое сравниваемое значение. Это — ожидаемое тестом значение.
            </param>
            <param name="actual">
            Второе сравниваемое значение. Это — значение, созданное тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            не равен <paramref name="expected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Проверяет указанные значения на равенство и создает исключение,
            если два значения не равны. Различные числовые типы
            считаются неравными, даже если логические значения равны. Например, 42L не равно 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Первое сравниваемое значение. Это — ожидаемое тестом значение.
            </param>
            <param name="actual">
            Второе сравниваемое значение. Это — значение, созданное тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            не равен <paramref name="expected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0)">
            <summary>
            Проверяет указанные значения на неравенство и создает исключение,
            если два значения равны. Различные числовые типы
            считаются неравными, даже если логические значения равны. Например, 42L не равно 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Первое сравниваемое значение. Это значение с точки зрения теста не должно 
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Второе сравниваемое значение. Это — значение, созданное тестируемым кодом.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String)">
            <summary>
            Проверяет указанные значения на неравенство и создает исключение,
            если два значения равны. Различные числовые типы
            считаются неравными, даже если логические значения равны. Например, 42L не равно 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Первое сравниваемое значение. Это значение с точки зрения теста не должно 
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Второе сравниваемое значение. Это — значение, созданное тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Проверяет указанные значения на неравенство и создает исключение,
            если два значения равны. Различные числовые типы
            считаются неравными, даже если логические значения равны. Например, 42L не равно 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Первое сравниваемое значение. Это значение с точки зрения теста не должно 
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Второе сравниваемое значение. Это — значение, созданное тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object)">
            <summary>
            Проверяет указанные объекты на равенство и создает исключение,
            если два объекта не равны. Различные числовые типы
            считаются неравными, даже если логические значения равны. Например, 42L не равно 42.
            </summary>
            <param name="expected">
            Первый сравниваемый объект. Это — ожидаемый тестом объект.
            </param>
            <param name="actual">
            Второй сравниваемый объект. Это — объект, созданный тестируемым кодом.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String)">
            <summary>
            Проверяет указанные объекты на равенство и создает исключение,
            если два объекта не равны. Различные числовые типы
            считаются неравными, даже если логические значения равны. Например, 42L не равно 42.
            </summary>
            <param name="expected">
            Первый сравниваемый объект. Это — ожидаемый тестом объект.
            </param>
            <param name="actual">
            Второй сравниваемый объект. Это — объект, созданный тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            не равен <paramref name="expected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Проверяет указанные объекты на равенство и создает исключение,
            если два объекта не равны. Различные числовые типы
            считаются неравными, даже если логические значения равны. Например, 42L не равно 42.
            </summary>
            <param name="expected">
            Первый сравниваемый объект. Это — ожидаемый тестом объект.
            </param>
            <param name="actual">
            Второй сравниваемый объект. Это — объект, созданный тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            не равен <paramref name="expected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object)">
            <summary>
            Проверяет указанные объекты на неравенство и создает исключение,
            если два объекта равны. Различные числовые типы
            считаются неравными, даже если логические значения равны. Например, 42L не равно 42.
            </summary>
            <param name="notExpected">
            Первый сравниваемый объект. Это — значение, которое с точки зрения теста не должно
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Второй сравниваемый объект. Это — объект, созданный тестируемым кодом.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String)">
            <summary>
            Проверяет указанные объекты на неравенство и создает исключение,
            если два объекта равны. Различные числовые типы
            считаются неравными, даже если логические значения равны. Например, 42L не равно 42.
            </summary>
            <param name="notExpected">
            Первый сравниваемый объект. Это — значение, которое с точки зрения теста не должно
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Второй сравниваемый объект. Это — объект, созданный тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Проверяет указанные объекты на неравенство и создает исключение,
            если два объекта равны. Различные числовые типы
            считаются неравными, даже если логические значения равны. Например, 42L не равно 42.
            </summary>
            <param name="notExpected">
            Первый сравниваемый объект. Это — значение, которое с точки зрения теста не должно
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Второй сравниваемый объект. Это — объект, созданный тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single)">
            <summary>
            Проверяет указанные числа с плавающей запятой на равенство и создает исключение,
            если они не равны.
            </summary>
            <param name="expected">
            Первое число с плавающей запятой для сравнения. Это — ожидаемое тестом число.
            </param>
            <param name="actual">
            Второе число с плавающей запятой для сравнения. Это — число, созданное тестируемым кодом.
            </param>
            <param name="delta">
            Требуемая точность. Исключение будет создано, только если
            <paramref name="actual"/> отличается от <paramref name="expected"/>
            более чем на <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Проверяет указанные числа с плавающей запятой на равенство и создает исключение,
            если они не равны.
            </summary>
            <param name="expected">
            Первое число с плавающей запятой для сравнения. Это — ожидаемое тестом число.
            </param>
            <param name="actual">
            Второе число с плавающей запятой для сравнения. Это — число, созданное тестируемым кодом.
            </param>
            <param name="delta">
            Требуемая точность. Исключение будет создано, только если
            <paramref name="actual"/> отличается от <paramref name="expected"/>
            более чем на <paramref name="delta"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            отличается от <paramref name="expected"/> более чем на
            <paramref name="delta"/>. Сообщение отображается в результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Проверяет указанные числа с плавающей запятой на равенство и создает исключение,
            если они не равны.
            </summary>
            <param name="expected">
            Первое число с плавающей запятой для сравнения. Это — ожидаемое тестом число.
            </param>
            <param name="actual">
            Второе число с плавающей запятой для сравнения. Это — число, созданное тестируемым кодом.
            </param>
            <param name="delta">
            Требуемая точность. Исключение будет создано, только если
            <paramref name="actual"/> отличается от <paramref name="expected"/>
            более чем на <paramref name="delta"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            отличается от <paramref name="expected"/> более чем на
            <paramref name="delta"/>. Сообщение отображается в результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single)">
            <summary>
            Проверяет указанные числа с плавающей запятой на неравенство и создает исключение,
            если они равны.
            </summary>
            <param name="notExpected">
            Первое число с плавающей запятой для сравнения. Это число с плавающей запятой с точки зрения теста не должно 
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Второе число с плавающей запятой для сравнения. Это — число, созданное тестируемым кодом.
            </param>
            <param name="delta">
            Требуемая точность. Исключение будет создано, только если
            <paramref name="actual"/> отличается от <paramref name="notExpected"/>
            не более чем на <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Проверяет указанные числа с плавающей запятой на неравенство и создает исключение,
            если они равны.
            </summary>
            <param name="notExpected">
            Первое число с плавающей запятой для сравнения. Это число с плавающей запятой с точки зрения теста не должно 
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Второе число с плавающей запятой для сравнения. Это — число, созданное тестируемым кодом.
            </param>
            <param name="delta">
            Требуемая точность. Исключение будет создано, только если
            <paramref name="actual"/> отличается от <paramref name="notExpected"/>
            не более чем на <paramref name="delta"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/> или отличается менее чем на
            <paramref name="delta"/>. Сообщение отображается в результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Проверяет указанные числа с плавающей запятой на неравенство и создает исключение,
            если они равны.
            </summary>
            <param name="notExpected">
            Первое число с плавающей запятой для сравнения. Это число с плавающей запятой с точки зрения теста не должно 
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Второе число с плавающей запятой для сравнения. Это — число, созданное тестируемым кодом.
            </param>
            <param name="delta">
            Требуемая точность. Исключение будет создано, только если
            <paramref name="actual"/> отличается от <paramref name="notExpected"/>
            не более чем на <paramref name="delta"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/> или отличается менее чем на
            <paramref name="delta"/>. Сообщение отображается в результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double)">
            <summary>
            Проверяет указанные числа с плавающей запятой двойной точности на равенство и создает исключение,
            если они не равны.
            </summary>
            <param name="expected">
            Первое число с плавающей запятой двойной точности для сравнения. Это — ожидаемое тестом число.
            </param>
            <param name="actual">
            Второе число с плавающей запятой двойной точности для сравнения. Это — число, созданное тестируемым кодом.
            </param>
            <param name="delta">
            Требуемая точность. Исключение будет создано, только если
            <paramref name="actual"/> отличается от <paramref name="expected"/>
            более чем на <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Проверяет указанные числа с плавающей запятой двойной точности на равенство и создает исключение,
            если они не равны.
            </summary>
            <param name="expected">
            Первое число с плавающей запятой двойной точности для сравнения. Это — ожидаемое тестом число.
            </param>
            <param name="actual">
            Второе число с плавающей запятой двойной точности для сравнения. Это — число, созданное тестируемым кодом.
            </param>
            <param name="delta">
            Требуемая точность. Исключение будет создано, только если
            <paramref name="actual"/> отличается от <paramref name="expected"/>
            более чем на <paramref name="delta"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            отличается от <paramref name="expected"/> более чем на
            <paramref name="delta"/>. Сообщение отображается в результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Проверяет указанные числа с плавающей запятой двойной точности на равенство и создает исключение,
            если они не равны.
            </summary>
            <param name="expected">
            Первое число с плавающей запятой двойной точности для сравнения. Это — ожидаемое тестом число.
            </param>
            <param name="actual">
            Второе число с плавающей запятой двойной точности для сравнения. Это — число, созданное тестируемым кодом.
            </param>
            <param name="delta">
            Требуемая точность. Исключение будет создано, только если
            <paramref name="actual"/> отличается от <paramref name="expected"/>
            более чем на <paramref name="delta"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            отличается от <paramref name="expected"/> более чем на
            <paramref name="delta"/>. Сообщение отображается в результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double)">
            <summary>
            Проверяет указанные числа с плавающей запятой двойной точности на неравенство и создает исключение,
            если они равны.
            </summary>
            <param name="notExpected">
            Первое число с плавающей запятой двойной точности для сравнения. Это число с точки зрения теста не должно
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Второе число с плавающей запятой двойной точности для сравнения. Это — число, созданное тестируемым кодом.
            </param>
            <param name="delta">
            Требуемая точность. Исключение будет создано, только если
            <paramref name="actual"/> отличается от <paramref name="notExpected"/>
            не более чем на <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Проверяет указанные числа с плавающей запятой двойной точности на неравенство и создает исключение,
            если они равны.
            </summary>
            <param name="notExpected">
            Первое число с плавающей запятой двойной точности для сравнения. Это число с точки зрения теста не должно
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Второе число с плавающей запятой двойной точности для сравнения. Это — число, созданное тестируемым кодом.
            </param>
            <param name="delta">
            Требуемая точность. Исключение будет создано, только если
            <paramref name="actual"/> отличается от <paramref name="notExpected"/>
            не более чем на <paramref name="delta"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/> или отличается менее чем на
            <paramref name="delta"/>. Сообщение отображается в результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Проверяет указанные числа с плавающей запятой двойной точности на неравенство и создает исключение,
            если они равны.
            </summary>
            <param name="notExpected">
            Первое число с плавающей запятой двойной точности для сравнения. Это число с точки зрения теста не должно
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Второе число с плавающей запятой двойной точности для сравнения. Это — число, созданное тестируемым кодом.
            </param>
            <param name="delta">
            Требуемая точность. Исключение будет создано, только если
            <paramref name="actual"/> отличается от <paramref name="notExpected"/>
            не более чем на <paramref name="delta"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/> или отличается менее чем на
            <paramref name="delta"/>. Сообщение отображается в результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean)">
            <summary>
            Проверяет, равны ли указанные строки, и создает исключение,
            если они не равны. При сравнении используются инвариантный язык и региональные параметры.
            </summary>
            <param name="expected">
            Первая сравниваемая строка. Это — ожидаемая тестом строка.
            </param>
            <param name="actual">
            Вторая сравниваемая строка. Это — строка, созданная тестируемым кодом.
            </param>
            <param name="ignoreCase">
            Логический параметр, означающий сравнение с учетом или без учета регистра. (True
            означает сравнение с учетом регистра.)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Проверяет, равны ли указанные строки, и создает исключение,
            если они не равны. При сравнении используются инвариантный язык и региональные параметры.
            </summary>
            <param name="expected">
            Первая сравниваемая строка. Это — ожидаемая тестом строка.
            </param>
            <param name="actual">
            Вторая сравниваемая строка. Это — строка, созданная тестируемым кодом.
            </param>
            <param name="ignoreCase">
            Логический параметр, означающий сравнение с учетом или без учета регистра. (True
            означает сравнение с учетом регистра.)
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            не равен <paramref name="expected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Проверяет, равны ли указанные строки, и создает исключение,
            если они не равны. При сравнении используются инвариантный язык и региональные параметры.
            </summary>
            <param name="expected">
            Первая сравниваемая строка. Это — ожидаемая тестом строка.
            </param>
            <param name="actual">
            Вторая сравниваемая строка. Это — строка, созданная тестируемым кодом.
            </param>
            <param name="ignoreCase">
            Логический параметр, означающий сравнение с учетом или без учета регистра. (True
            означает сравнение с учетом регистра.)
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            не равен <paramref name="expected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Проверяет указанные строки на равенство и создает исключение,
            если они не равны.
            </summary>
            <param name="expected">
            Первая сравниваемая строка. Это — ожидаемая тестом строка.
            </param>
            <param name="actual">
            Вторая сравниваемая строка. Это — строка, созданная тестируемым кодом.
            </param>
            <param name="ignoreCase">
            Логический параметр, означающий сравнение с учетом или без учета регистра. (True
            означает сравнение с учетом регистра.)
            </param>
            <param name="culture">
            Объект CultureInfo, содержащий данные о языке и региональных стандартах, которые используются при сравнении.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Проверяет указанные строки на равенство и создает исключение,
            если они не равны.
            </summary>
            <param name="expected">
            Первая сравниваемая строка. Это — ожидаемая тестом строка.
            </param>
            <param name="actual">
            Вторая сравниваемая строка. Это — строка, созданная тестируемым кодом.
            </param>
            <param name="ignoreCase">
            Логический параметр, означающий сравнение с учетом или без учета регистра. (True
            означает сравнение с учетом регистра.)
            </param>
            <param name="culture">
            Объект CultureInfo, содержащий данные о языке и региональных стандартах, которые используются при сравнении.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            не равен <paramref name="expected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Проверяет указанные строки на равенство и создает исключение,
            если они не равны.
            </summary>
            <param name="expected">
            Первая сравниваемая строка. Это — ожидаемая тестом строка.
            </param>
            <param name="actual">
            Вторая сравниваемая строка. Это — строка, созданная тестируемым кодом.
            </param>
            <param name="ignoreCase">
            Логический параметр, означающий сравнение с учетом или без учета регистра. (True
            означает сравнение с учетом регистра.)
            </param>
            <param name="culture">
            Объект CultureInfo, содержащий данные о языке и региональных стандартах, которые используются при сравнении.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            не равен <paramref name="expected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean)">
            <summary>
            Проверяет строки на неравенство и создает исключение,
            если они равны. При сравнении используются инвариантные язык и региональные параметры.
            </summary>
            <param name="notExpected">
            Первая сравниваемая строка. Эта строка не должна с точки зрения теста 
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Вторая сравниваемая строка. Это — строка, созданная тестируемым кодом.
            </param>
            <param name="ignoreCase">
            Логический параметр, означающий сравнение с учетом или без учета регистра. (True
            означает сравнение с учетом регистра.)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Проверяет строки на неравенство и создает исключение,
            если они равны. При сравнении используются инвариантные язык и региональные параметры.
            </summary>
            <param name="notExpected">
            Первая сравниваемая строка. Эта строка не должна с точки зрения теста 
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Вторая сравниваемая строка. Это — строка, созданная тестируемым кодом.
            </param>
            <param name="ignoreCase">
            Логический параметр, означающий сравнение с учетом или без учета регистра. (True
            означает сравнение с учетом регистра.)
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Проверяет строки на неравенство и создает исключение,
            если они равны. При сравнении используются инвариантные язык и региональные параметры.
            </summary>
            <param name="notExpected">
            Первая сравниваемая строка. Эта строка не должна с точки зрения теста 
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Вторая сравниваемая строка. Это — строка, созданная тестируемым кодом.
            </param>
            <param name="ignoreCase">
            Логический параметр, означающий сравнение с учетом или без учета регистра. (True
            означает сравнение с учетом регистра.)
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Проверяет указанные строки на неравенство и создает исключение,
            если они равны.
            </summary>
            <param name="notExpected">
            Первая сравниваемая строка. Эта строка не должна с точки зрения теста 
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Вторая сравниваемая строка. Это — строка, созданная тестируемым кодом.
            </param>
            <param name="ignoreCase">
            Логический параметр, означающий сравнение с учетом или без учета регистра. (True
            означает сравнение с учетом регистра.)
            </param>
            <param name="culture">
            Объект CultureInfo, содержащий данные о языке и региональных стандартах, которые используются при сравнении.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Проверяет указанные строки на неравенство и создает исключение,
            если они равны.
            </summary>
            <param name="notExpected">
            Первая сравниваемая строка. Эта строка не должна с точки зрения теста 
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Вторая сравниваемая строка. Это — строка, созданная тестируемым кодом.
            </param>
            <param name="ignoreCase">
            Логический параметр, означающий сравнение с учетом или без учета регистра. (True
            означает сравнение с учетом регистра.)
            </param>
            <param name="culture">
            Объект CultureInfo, содержащий данные о языке и региональных стандартах, которые используются при сравнении.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Проверяет указанные строки на неравенство и создает исключение,
            если они равны.
            </summary>
            <param name="notExpected">
            Первая сравниваемая строка. Эта строка не должна с точки зрения теста 
            соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Вторая сравниваемая строка. Это — строка, созданная тестируемым кодом.
            </param>
            <param name="ignoreCase">
            Логический параметр, означающий сравнение с учетом или без учета регистра. (True
            означает сравнение с учетом регистра.)
            </param>
            <param name="culture">
            Объект CultureInfo, содержащий данные о языке и региональных стандартах, которые используются при сравнении.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type)">
            <summary>
            Проверяет, является ли указанный объект экземпляром ожидаемого
            типа, и создает исключение, если ожидаемый тип отсутствует в
            иерархии наследования объекта.
            </summary>
            <param name="value">
            Объект, который с точки зрения теста должен иметь указанный тип.
            </param>
            <param name="expectedType">
            Ожидаемый тип <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Проверяет, является ли указанный объект экземпляром ожидаемого
            типа, и создает исключение, если ожидаемый тип отсутствует в
            иерархии наследования объекта.
            </summary>
            <param name="value">
            Объект, который с точки зрения теста должен иметь указанный тип.
            </param>
            <param name="expectedType">
            Ожидаемый тип <paramref name="value"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="value"/>
            не является экземпляром <paramref name="expectedType"/>. Сообщение
            отображается в результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Проверяет, является ли указанный объект экземпляром ожидаемого
            типа, и создает исключение, если ожидаемый тип отсутствует в
            иерархии наследования объекта.
            </summary>
            <param name="value">
            Объект, который с точки зрения теста должен иметь указанный тип.
            </param>
            <param name="expectedType">
            Ожидаемый тип <paramref name="value"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="value"/>
            не является экземпляром <paramref name="expectedType"/>. Сообщение
            отображается в результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type)">
            <summary>
            Проверяет, является ли указанный объект экземпляром неправильного
            типа, и создает исключение, если указанный тип присутствует в
            иерархии наследования объекта.
            </summary>
            <param name="value">
            Объект, который с точки зрения теста не должен иметь указанный тип.
            </param>
            <param name="wrongType">
            Тип, который параметр <paramref name="value"/> иметь не должен.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Проверяет, является ли указанный объект экземпляром неправильного
            типа, и создает исключение, если указанный тип присутствует в
            иерархии наследования объекта.
            </summary>
            <param name="value">
            Объект, который с точки зрения теста не должен иметь указанный тип.
            </param>
            <param name="wrongType">
            Тип, который параметр <paramref name="value"/> иметь не должен.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="value"/>
            является экземпляром класса <paramref name="wrongType"/>. Сообщение отображается
            в результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Проверяет, является ли указанный объект экземпляром неправильного
            типа, и создает исключение, если указанный тип присутствует в
            иерархии наследования объекта.
            </summary>
            <param name="value">
            Объект, который с точки зрения теста не должен иметь указанный тип.
            </param>
            <param name="wrongType">
            Тип, который параметр <paramref name="value"/> иметь не должен.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="value"/>
            является экземпляром класса <paramref name="wrongType"/>. Сообщение отображается
            в результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail">
            <summary>
            Создает исключение AssertFailedException.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String)">
            <summary>
            Создает исключение AssertFailedException.
            </summary>
            <param name="message">
            Сообщение, которое нужно добавить в исключение. Это сообщение отображается
            в результатах теста.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String,System.Object[])">
            <summary>
            Создает исключение AssertFailedException.
            </summary>
            <param name="message">
            Сообщение, которое нужно добавить в исключение. Это сообщение отображается
            в результатах теста.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive">
            <summary>
            Создает исключение AssertInconclusiveException.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String)">
            <summary>
            Создает исключение AssertInconclusiveException.
            </summary>
            <param name="message">
            Сообщение, которое нужно добавить в исключение. Это сообщение отображается
            в результатах теста.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String,System.Object[])">
            <summary>
            Создает исключение AssertInconclusiveException.
            </summary>
            <param name="message">
            Сообщение, которое нужно добавить в исключение. Это сообщение отображается
            в результатах теста.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Equals(System.Object,System.Object)">
            <summary>
            Статические переопределения равенства используются для сравнения экземпляров двух типов на равенство
            ссылок. Этот метод <b>не должен</b> использоваться для сравнения двух экземпляров на
            равенство. Этот объект <b>всегда</b> создает исключение с Assert.Fail. Используйте в ваших модульных тестах
            Assert.AreEqual и связанные переопределения.
            </summary>
            <param name="objA"> Объект A </param>
            <param name="objB"> Объект B </param>
            <returns> False (всегда). </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action)">
            <summary>
            Проверяет, создает ли код, указанный в делегате <paramref name="action"/> , заданное исключение типа <typeparamref name="T"/> (не производного),
            и создает исключение
            <code>
            AssertFailedException,
            </code>
            если код не создает исключение, или создает исключение типа, отличного от <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Делегат для проверяемого кода, который должен создать исключение.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Тип ожидаемого исключения.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String)">
            <summary>
            Проверяет, создает ли код, указанный в делегате <paramref name="action"/> , заданное исключение типа <typeparamref name="T"/> (не производного),
            и создает исключение
            <code>
            AssertFailedException,
            </code>
            если код не создает исключение, или создает исключение типа, отличного от <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Делегат для проверяемого кода, который должен создать исключение.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="action"/>
            не создает исключение типа <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Тип ожидаемого исключения.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object})">
            <summary>
            Проверяет, создает ли код, указанный в делегате <paramref name="action"/> , заданное исключение типа <typeparamref name="T"/> (не производного),
            и создает исключение
            <code>
            AssertFailedException,
            </code>
            если код не создает исключение, или создает исключение типа, отличного от <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Делегат для проверяемого кода, который должен создать исключение.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Тип ожидаемого исключения.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String)">
            <summary>
            Проверяет, создает ли код, указанный в делегате <paramref name="action"/> , заданное исключение типа <typeparamref name="T"/> (не производного),
            и создает исключение
            <code>
            AssertFailedException,
            </code>
            если код не создает исключение, или создает исключение типа, отличного от <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Делегат для проверяемого кода, который должен создать исключение.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="action"/>
            не создает исключение типа <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Тип ожидаемого исключения.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String,System.Object[])">
            <summary>
            Проверяет, создает ли код, указанный в делегате <paramref name="action"/> , заданное исключение типа <typeparamref name="T"/> (не производного),
            и создает исключение
            <code>
            AssertFailedException,
            </code>
            если код не создает исключение, или создает исключение типа, отличного от <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Делегат для проверяемого кода, который должен создать исключение.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="action"/>
            не создает исключение типа <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throw exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Тип ожидаемого исключения.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String,System.Object[])">
            <summary>
            Проверяет, создает ли код, указанный в делегате <paramref name="action"/> , заданное исключение типа <typeparamref name="T"/> (не производного),
            и создает исключение
            <code>
            AssertFailedException,
            </code>
            если код не создает исключение, или создает исключение типа, отличного от <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Делегат для проверяемого кода, который должен создать исключение.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="action"/>
            не создает исключение типа <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Тип ожидаемого исключения.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Проверяет, создает ли код, указанный в делегате <paramref name="action"/> , заданное исключение типа <typeparamref name="T"/> (не производного),
            и создает исключение
            <code>
            AssertFailedException,
            </code>
            если код не создает исключение, или создает исключение типа, отличного от <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Делегат для проверяемого кода, который должен создать исключение.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
             <see cref="T:System.Threading.Tasks.Task"/> выполнение делегата.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            Проверяет, создает ли код, указанный с помощью делегата <paramref name="action"/> , в точности заданное исключение типа <typeparamref name="T"/> (и не производного типа ),
            и создает исключение <code>AssertFailedException</code> , если код не создает исключение, или создает исключение типа, отличного от <typeparamref name="T"/>.
            </summary>
            <param name="action">Делегат для проверяемого кода, который должен создать исключение.</param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="action"/>
            не создает исключение типа <typeparamref name="T"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
             <see cref="T:System.Threading.Tasks.Task"/> выполнение делегата.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String,System.Object[])">
            <summary>
            Проверяет, создает ли код, указанный с помощью делегата <paramref name="action"/> , в точности заданное исключение типа <typeparamref name="T"/> (и не производного типа ),
            и создает исключение <code>AssertFailedException</code> , если код не создает исключение, или создает исключение типа, отличного от <typeparamref name="T"/>.
            </summary>
            <param name="action">Делегат для проверяемого кода, который должен создать исключение.</param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="action"/>
            не создает исключение типа <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
             <see cref="T:System.Threading.Tasks.Task"/> выполнение делегата.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNullChars(System.String)">
            <summary>
            Заменяет NULL-символы ("\0") символами "\\0".
            </summary>
            <param name="input">
            Искомая строка.
            </param>
            <returns>
            Преобразованная строка, в которой NULL-символы были заменены на "\\0".
            </returns>
            <remarks>
            This is only public and still present to preserve compatibility with the V1 framework.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.HandleFail(System.String,System.String,System.Object[])">
            <summary>
            Вспомогательная функция, которая создает и вызывает AssertionFailedException
            </summary>
            <param name="assertionName">
            имя утверждения, создавшего исключение
            </param>
            <param name="message">
            сообщение с описанием условий для сбоя утверждения
            </param>
            <param name="parameters">
            Параметры.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.CheckParameterNotNull(System.Object,System.String,System.String,System.String,System.Object[])">
            <summary>
            Проверяет параметр на допустимые условия
            </summary>
            <param name="param">
            Параметр.
            </param>
            <param name="assertionName">
            Имя утверждения.
            </param>
            <param name="parameterName">
            имя параметра
            </param>
            <param name="message">
            сообщение об исключении, связанном с недопустимым параметром
            </param>
            <param name="parameters">
            Параметры.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNulls(System.Object)">
            <summary>
            Безопасно преобразует объект в строку, обрабатывая значения NULL и NULL-символы.
            Значения NULL преобразуются в "(null)", NULL-символы — в "\\0".
            </summary>
            <param name="input">
            Объект для преобразования в строку.
            </param>
            <returns>
            Преобразованная строка.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert">
            <summary>
            Утверждение строки.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.That">
            <summary>
            Получает одноэлементный экземпляр функции CollectionAssert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void ContainsWords(this StringAssert cusomtAssert, string value, ICollection substrings)"
            Users could then use a syntax similar to the default assertions which in this case is "StringAssert.That.ContainsWords(value, substrings);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String)">
            <summary>
            Проверяет, содержит ли указанная строка заданную подстроку,
            и создает исключение, если подстрока не содержится
            в тестовой строке.
            </summary>
            <param name="value">
            Строка, которая должна содержать <paramref name="substring"/>.
            </param>
            <param name="substring">
            Строка, которая должна входить в <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String)">
            <summary>
            Проверяет, содержит ли указанная строка заданную подстроку,
            и создает исключение, если подстрока не содержится
            в тестовой строке.
            </summary>
            <param name="value">
            Строка, которая должна содержать <paramref name="substring"/>.
            </param>
            <param name="substring">
            Строка, которая должна входить в <paramref name="value"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="substring"/>
            не находится в <paramref name="value"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String,System.Object[])">
            <summary>
            Проверяет, содержит ли указанная строка заданную подстроку,
            и создает исключение, если подстрока не содержится
            в тестовой строке.
            </summary>
            <param name="value">
            Строка, которая должна содержать <paramref name="substring"/>.
            </param>
            <param name="substring">
            Строка, которая должна входить в <paramref name="value"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="substring"/>
            не находится в <paramref name="value"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String)">
            <summary>
            Проверяет, начинается ли указанная строка с указанной подстроки,
            и создает исключение, если тестовая строка не начинается
            с подстроки.
            </summary>
            <param name="value">
            Строка, которая должна начинаться с <paramref name="substring"/>.
            </param>
            <param name="substring">
            Строка, которая должна быть префиксом <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String)">
            <summary>
            Проверяет, начинается ли указанная строка с указанной подстроки,
            и создает исключение, если тестовая строка не начинается
            с подстроки.
            </summary>
            <param name="value">
            Строка, которая должна начинаться с <paramref name="substring"/>.
            </param>
            <param name="substring">
            Строка, которая должна быть префиксом <paramref name="value"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="value"/>
            не начинается с <paramref name="substring"/>. Сообщение
            отображается в результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Проверяет, начинается ли указанная строка с указанной подстроки,
            и создает исключение, если тестовая строка не начинается
            с подстроки.
            </summary>
            <param name="value">
            Строка, которая должна начинаться с <paramref name="substring"/>.
            </param>
            <param name="substring">
            Строка, которая должна быть префиксом <paramref name="value"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="value"/>
            не начинается с <paramref name="substring"/>. Сообщение
            отображается в результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String)">
            <summary>
            Проверяет, заканчивается ли указанная строка заданной подстрокой,
            и создает исключение, если тестовая строка не заканчивается
            заданной подстрокой.
            </summary>
            <param name="value">
            Строка, которая должна заканчиваться на <paramref name="substring"/>.
            </param>
            <param name="substring">
            Строка, которая должна быть суффиксом <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String)">
            <summary>
            Проверяет, заканчивается ли указанная строка заданной подстрокой,
            и создает исключение, если тестовая строка не заканчивается
            заданной подстрокой.
            </summary>
            <param name="value">
            Строка, которая должна заканчиваться на <paramref name="substring"/>.
            </param>
            <param name="substring">
            Строка, которая должна быть суффиксом <paramref name="value"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="value"/>
            не заканчивается на <paramref name="substring"/>. Сообщение
            отображается в результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Проверяет, заканчивается ли указанная строка заданной подстрокой,
            и создает исключение, если тестовая строка не заканчивается
            заданной подстрокой.
            </summary>
            <param name="value">
            Строка, которая должна заканчиваться на <paramref name="substring"/>.
            </param>
            <param name="substring">
            Строка, которая должна быть суффиксом <paramref name="value"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="value"/>
            не заканчивается на <paramref name="substring"/>. Сообщение
            отображается в результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Проверяет, соответствует ли указанная строка регулярному выражению,
            и создает исключение, если строка не соответствует регулярному выражению.
            </summary>
            <param name="value">
            Строка, которая должна соответствовать <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Регулярное выражение, которому параметр <paramref name="value"/> должен
            соответствовать.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Проверяет, соответствует ли указанная строка регулярному выражению,
            и создает исключение, если строка не соответствует регулярному выражению.
            </summary>
            <param name="value">
            Строка, которая должна соответствовать <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Регулярное выражение, которому параметр <paramref name="value"/> должен
            соответствовать.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="value"/>
            не соответствует <paramref name="pattern"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Проверяет, соответствует ли указанная строка регулярному выражению,
            и создает исключение, если строка не соответствует регулярному выражению.
            </summary>
            <param name="value">
            Строка, которая должна соответствовать <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Регулярное выражение, которому параметр <paramref name="value"/> должен
            соответствовать.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="value"/>
            не соответствует <paramref name="pattern"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Проверяет, не соответствует ли указанная строка регулярному выражению,
            и создает исключение, если строка соответствует регулярному выражению.
            </summary>
            <param name="value">
            Строка, которая не должна соответствовать <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Регулярное выражение, которому параметр <paramref name="value"/> не должен
            соответствовать.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Проверяет, не соответствует ли указанная строка регулярному выражению,
            и создает исключение, если строка соответствует регулярному выражению.
            </summary>
            <param name="value">
            Строка, которая не должна соответствовать <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Регулярное выражение, которому параметр <paramref name="value"/> не должен
            соответствовать.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="value"/>
            соответствует <paramref name="pattern"/>. Сообщение отображается в результатах
            тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Проверяет, не соответствует ли указанная строка регулярному выражению,
            и создает исключение, если строка соответствует регулярному выражению.
            </summary>
            <param name="value">
            Строка, которая не должна соответствовать <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Регулярное выражение, которому параметр <paramref name="value"/> не должен
            соответствовать.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="value"/>
            соответствует <paramref name="pattern"/>. Сообщение отображается в результатах
            тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert">
            <summary>
            Коллекция вспомогательных классов для тестирования различных условий, связанных
            с коллекциями в модульных тестах. Если проверяемое условие
            ложно, создается исключение.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.That">
            <summary>
            Получает одноэлементный экземпляр функции CollectionAssert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void AreEqualUnordered(this CollectionAssert cusomtAssert, ICollection expected, ICollection actual)"
            Users could then use a syntax similar to the default assertions which in this case is "CollectionAssert.That.AreEqualUnordered(list1, list2);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object)">
            <summary>
            Проверяет, содержит ли заданная коллекция указанный элемент,
            и создает исключение, если элемент не входит в коллекцию.
            </summary>
            <param name="collection">
            Коллекция, в которой выполняется поиск элемента.
            </param>
            <param name="element">
            Элемент, который должен входить в коллекцию.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Проверяет, содержит ли заданная коллекция указанный элемент,
            и создает исключение, если элемент не входит в коллекцию.
            </summary>
            <param name="collection">
            Коллекция, в которой выполняется поиск элемента.
            </param>
            <param name="element">
            Элемент, который должен входить в коллекцию.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="element"/>
            не находится в <paramref name="collection"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Проверяет, содержит ли заданная коллекция указанный элемент,
            и создает исключение, если элемент не входит в коллекцию.
            </summary>
            <param name="collection">
            Коллекция, в которой выполняется поиск элемента.
            </param>
            <param name="element">
            Элемент, который должен входить в коллекцию.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="element"/>
            не находится в <paramref name="collection"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object)">
            <summary>
            Проверяет, содержит ли коллекция указанный элемент,
            и создает исключение, если элемент входит в коллекцию.
            </summary>
            <param name="collection">
            Коллекция, в которой выполняется поиск элемента.
            </param>
            <param name="element">
            Элемент, который не должен входить в коллекцию.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Проверяет, содержит ли коллекция указанный элемент,
            и создает исключение, если элемент входит в коллекцию.
            </summary>
            <param name="collection">
            Коллекция, в которой выполняется поиск элемента.
            </param>
            <param name="element">
            Элемент, который не должен входить в коллекцию.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="element"/>
            находится в <paramref name="collection"/>. Сообщение отображается в результатах
            тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Проверяет, содержит ли коллекция указанный элемент,
            и создает исключение, если элемент входит в коллекцию.
            </summary>
            <param name="collection">
            Коллекция, в которой выполняется поиск элемента.
            </param>
            <param name="element">
            Элемент, который не должен входить в коллекцию.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="element"/>
            находится в <paramref name="collection"/>. Сообщение отображается в результатах
            тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection)">
            <summary>
            Проверяет, все ли элементы в указанной коллекции имеют значения, отличные от NULL,
            и создает исключение, если какой-либо элемент имеет значение NULL.
            </summary>
            <param name="collection">
            Коллекция, в которой выполняется поиск элементов, имеющих значение NULL.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String)">
            <summary>
            Проверяет, все ли элементы в указанной коллекции имеют значения, отличные от NULL,
            и создает исключение, если какой-либо элемент имеет значение NULL.
            </summary>
            <param name="collection">
            Коллекция, в которой выполняется поиск элементов, имеющих значение NULL.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="collection"/>
            содержит элемент, равный NULL. Сообщение отображается в результатах теста.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Проверяет, все ли элементы в указанной коллекции имеют значения, отличные от NULL,
            и создает исключение, если какой-либо элемент имеет значение NULL.
            </summary>
            <param name="collection">
            Коллекция, в которой выполняется поиск элементов, имеющих значение NULL.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="collection"/>
            содержит элемент, равный NULL. Сообщение отображается в результатах теста.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection)">
            <summary>
            Проверяет, уникальны ли все элементы в указанной коллекции,
            и создает исключение, если любые два элемента в коллекции равны.
            </summary>
            <param name="collection">
            Коллекция, в которой выполняется поиск дубликатов элементов.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String)">
            <summary>
            Проверяет, уникальны ли все элементы в указанной коллекции,
            и создает исключение, если любые два элемента в коллекции равны.
            </summary>
            <param name="collection">
            Коллекция, в которой выполняется поиск дубликатов элементов.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="collection"/>
            содержит как минимум один элемент-дубликат. Это сообщение отображается в
            результатах теста.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Проверяет, уникальны ли все элементы в указанной коллекции,
            и создает исключение, если любые два элемента в коллекции равны.
            </summary>
            <param name="collection">
            Коллекция, в которой выполняется поиск дубликатов элементов.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="collection"/>
            содержит как минимум один элемент-дубликат. Это сообщение отображается в
            результатах теста.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Проверяет, является ли коллекция подмножеством другой коллекции, и
            создает исключение, если любой элемент подмножества не является также элементом
            супермножества.
            </summary>
            <param name="subset">
            Коллекция, которая должна быть подмножеством <paramref name="superset"/>.
            </param>
            <param name="superset">
            Коллекция, которая должна быть супермножеством <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Проверяет, является ли коллекция подмножеством другой коллекции, и
            создает исключение, если любой элемент подмножества не является также элементом
            супермножества.
            </summary>
            <param name="subset">
            Коллекция, которая должна быть подмножеством <paramref name="superset"/>.
            </param>
            <param name="superset">
            Коллекция, которая должна быть супермножеством <paramref name="subset"/>
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если элемент в
            <paramref name="subset"/> не обнаружен в <paramref name="superset"/>.
            Сообщение отображается в результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Проверяет, является ли коллекция подмножеством другой коллекции, и
            создает исключение, если любой элемент подмножества не является также элементом
            супермножества.
            </summary>
            <param name="subset">
            Коллекция, которая должна быть подмножеством <paramref name="superset"/>.
            </param>
            <param name="superset">
            Коллекция, которая должна быть супермножеством <paramref name="subset"/>
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если элемент в
            <paramref name="subset"/> не обнаружен в <paramref name="superset"/>.
            Сообщение отображается в результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Проверяет, не является ли коллекция подмножеством другой коллекции, и
            создает исключение, если все элементы подмножества также входят в
            супермножество.
            </summary>
            <param name="subset">
            Коллекция, которая не должна быть подмножеством <paramref name="superset"/>.
            </param>
            <param name="superset">
            Коллекция, которая не должна быть супермножеством <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Проверяет, не является ли коллекция подмножеством другой коллекции, и
            создает исключение, если все элементы подмножества также входят в
            супермножество.
            </summary>
            <param name="subset">
            Коллекция, которая не должна быть подмножеством <paramref name="superset"/>.
            </param>
            <param name="superset">
            Коллекция, которая не должна быть супермножеством <paramref name="subset"/>
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если каждый элемент в
            <paramref name="subset"/> также обнаружен в <paramref name="superset"/>.
            Сообщение отображается в результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Проверяет, не является ли коллекция подмножеством другой коллекции, и
            создает исключение, если все элементы подмножества также входят в
            супермножество.
            </summary>
            <param name="subset">
            Коллекция, которая не должна быть подмножеством <paramref name="superset"/>.
            </param>
            <param name="superset">
            Коллекция, которая не должна быть супермножеством <paramref name="subset"/>
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если каждый элемент в
            <paramref name="subset"/> также обнаружен в <paramref name="superset"/>.
            Сообщение отображается в результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Проверяет, содержат ли две коллекции одинаковые элементы, и создает
            исключение, если в любой из коллекций есть непарные
            элементы.
            </summary>
            <param name="expected">
            Первая сравниваемая коллекция. Она содержит ожидаемые тестом
            элементы.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Проверяет, содержат ли две коллекции одинаковые элементы, и создает
            исключение, если в любой из коллекций есть непарные
            элементы.
            </summary>
            <param name="expected">
            Первая сравниваемая коллекция. Она содержит ожидаемые тестом
            элементы.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если элемент был обнаружен
            в одной коллекции, но не обнаружен в другой. Это сообщение отображается
            в результатах теста.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Проверяет, содержат ли две коллекции одинаковые элементы, и создает
            исключение, если в любой из коллекций есть непарные
            элементы.
            </summary>
            <param name="expected">
            Первая сравниваемая коллекция. Она содержит ожидаемые тестом
            элементы.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если элемент был обнаружен
            в одной коллекции, но не обнаружен в другой. Это сообщение отображается
            в результатах теста.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Проверяет, содержат ли две коллекции разные элементы, и создает
            исключение, если две коллекции содержат одинаковые элементы (без учета
            порядка).
            </summary>
            <param name="expected">
            Первая сравниваемая коллекция. Она содержит элементы, которые должны
            отличаться от фактической коллекции с точки зрения теста.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Проверяет, содержат ли две коллекции разные элементы, и создает
            исключение, если две коллекции содержат одинаковые элементы (без учета
            порядка).
            </summary>
            <param name="expected">
            Первая сравниваемая коллекция. Она содержит элементы, которые должны
            отличаться от фактической коллекции с точки зрения теста.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            содержит такие же элементы, что и <paramref name="expected"/>. Сообщение
            отображается в результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Проверяет, содержат ли две коллекции разные элементы, и создает
            исключение, если две коллекции содержат одинаковые элементы (без учета
            порядка).
            </summary>
            <param name="expected">
            Первая сравниваемая коллекция. Она содержит элементы, которые должны
            отличаться от фактической коллекции с точки зрения теста.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            содержит такие же элементы, что и <paramref name="expected"/>. Сообщение
            отображается в результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type)">
            <summary>
            Проверяет, все ли элементы в указанной коллекции являются экземплярами
            ожидаемого типа, и создает исключение, если ожидаемый тип
            не входит в иерархию наследования одного или нескольких элементов.
            </summary>
            <param name="collection">
            Содержащая элементы коллекция, которые с точки зрения теста должны иметь
            указанный тип.
            </param>
            <param name="expectedType">
            Ожидаемый тип каждого элемента <paramref name="collection"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String)">
            <summary>
            Проверяет, все ли элементы в указанной коллекции являются экземплярами
            ожидаемого типа, и создает исключение, если ожидаемый тип
            не входит в иерархию наследования одного или нескольких элементов.
            </summary>
            <param name="collection">
            Содержащая элементы коллекция, которые с точки зрения теста должны иметь
            указанный тип.
            </param>
            <param name="expectedType">
            Ожидаемый тип каждого элемента <paramref name="collection"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если элемент в
            <paramref name="collection"/> не является экземпляром
            <paramref name="expectedType"/>. Сообщение отображается в результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String,System.Object[])">
            <summary>
            Проверяет, все ли элементы в указанной коллекции являются экземплярами
            ожидаемого типа, и создает исключение, если ожидаемый тип
            не входит в иерархию наследования одного или нескольких элементов.
            </summary>
            <param name="collection">
            Содержащая элементы коллекция, которые с точки зрения теста должны иметь
            указанный тип.
            </param>
            <param name="expectedType">
            Ожидаемый тип каждого элемента <paramref name="collection"/>.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если элемент в
            <paramref name="collection"/> не является экземпляром
            <paramref name="expectedType"/>. Сообщение отображается в результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Проверяет указанные коллекции на равенство и создает исключение,
            если две коллекции не равны. Равенство определяется как наличие одинаковых
            элементов в том же порядке и количестве. Различные ссылки на одно и то же
            значение считаются равными.
            </summary>
            <param name="expected">
            Первая сравниваемая коллекция. Это — ожидаемая тестом коллекция.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Проверяет указанные коллекции на равенство и создает исключение,
            если две коллекции не равны. Равенство определяется как наличие одинаковых
            элементов в том же порядке и количестве. Различные ссылки на одно и то же
            значение считаются равными.
            </summary>
            <param name="expected">
            Первая сравниваемая коллекция. Это — ожидаемая тестом коллекция.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            не равен <paramref name="expected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Проверяет указанные коллекции на равенство и создает исключение,
            если две коллекции не равны. Равенство определяется как наличие одинаковых
            элементов в том же порядке и количестве. Различные ссылки на одно и то же
            значение считаются равными.
            </summary>
            <param name="expected">
            Первая сравниваемая коллекция. Это — ожидаемая тестом коллекция.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            не равен <paramref name="expected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Проверяет указанные коллекции на неравенство и создает исключение,
            если две коллекции равны. Равенство определяется как наличие одинаковых
            элементов в том же порядке и количестве. Различные ссылки на одно и то же
            значение считаются равными.
            </summary>
            <param name="notExpected">
            Первая сравниваемая коллекция. Эта коллекция с точки зрения теста не
            должна соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Проверяет указанные коллекции на неравенство и создает исключение,
            если две коллекции равны. Равенство определяется как наличие одинаковых
            элементов в том же порядке и количестве. Различные ссылки на одно и то же
            значение считаются равными.
            </summary>
            <param name="notExpected">
            Первая сравниваемая коллекция. Эта коллекция с точки зрения теста не
            должна соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Проверяет указанные коллекции на неравенство и создает исключение,
            если две коллекции равны. Равенство определяется как наличие одинаковых
            элементов в том же порядке и количестве. Различные ссылки на одно и то же
            значение считаются равными.
            </summary>
            <param name="notExpected">
            Первая сравниваемая коллекция. Эта коллекция с точки зрения теста не
            должна соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Проверяет указанные коллекции на равенство и создает исключение,
            если две коллекции не равны. Равенство определяется как наличие одинаковых
            элементов в том же порядке и количестве. Различные ссылки на одно и то же
            значение считаются равными.
            </summary>
            <param name="expected">
            Первая сравниваемая коллекция. Это — ожидаемая тестом коллекция.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <param name="comparer">
            Реализация сравнения для сравнения элементов коллекции.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Проверяет указанные коллекции на равенство и создает исключение,
            если две коллекции не равны. Равенство определяется как наличие одинаковых
            элементов в том же порядке и количестве. Различные ссылки на одно и то же
            значение считаются равными.
            </summary>
            <param name="expected">
            Первая сравниваемая коллекция. Это — ожидаемая тестом коллекция.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <param name="comparer">
            Реализация сравнения для сравнения элементов коллекции.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            не равен <paramref name="expected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Проверяет указанные коллекции на равенство и создает исключение,
            если две коллекции не равны. Равенство определяется как наличие одинаковых
            элементов в том же порядке и количестве. Различные ссылки на одно и то же
            значение считаются равными.
            </summary>
            <param name="expected">
            Первая сравниваемая коллекция. Это — ожидаемая тестом коллекция.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <param name="comparer">
            Реализация сравнения для сравнения элементов коллекции.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            не равен <paramref name="expected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Проверяет указанные коллекции на неравенство и создает исключение,
            если две коллекции равны. Равенство определяется как наличие одинаковых
            элементов в том же порядке и количестве. Различные ссылки на одно и то же
            значение считаются равными.
            </summary>
            <param name="notExpected">
            Первая сравниваемая коллекция. Эта коллекция с точки зрения теста не
            должна соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <param name="comparer">
            Реализация сравнения для сравнения элементов коллекции.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Проверяет указанные коллекции на неравенство и создает исключение,
            если две коллекции равны. Равенство определяется как наличие одинаковых
            элементов в том же порядке и количестве. Различные ссылки на одно и то же
            значение считаются равными.
            </summary>
            <param name="notExpected">
            Первая сравниваемая коллекция. Эта коллекция с точки зрения теста не
            должна соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <param name="comparer">
            Реализация сравнения для сравнения элементов коллекции.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Проверяет указанные коллекции на неравенство и создает исключение,
            если две коллекции равны. Равенство определяется как наличие одинаковых
            элементов в том же порядке и количестве. Различные ссылки на одно и то же
            значение считаются равными.
            </summary>
            <param name="notExpected">
            Первая сравниваемая коллекция. Эта коллекция с точки зрения теста не
            должна соответствовать <paramref name="actual"/>.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция. Это — коллекция, созданная
            тестируемым кодом.
            </param>
            <param name="comparer">
            Реализация сравнения для сравнения элементов коллекции.
            </param>
            <param name="message">
            Сообщение, которое будет добавлено в исключение, если <paramref name="actual"/>
            равен <paramref name="notExpected"/>. Сообщение отображается в
            результатах тестирования.
            </param>
            <param name="parameters">
            Массив параметров для использования при форматировании <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOfHelper(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Определяет, является ли первая коллекция подмножеством второй
            коллекции. Если любое из множеств содержит одинаковые элементы, то число
            вхождений элемента в подмножестве должно быть меньше или
            равно количеству вхождений в супермножестве.
            </summary>
            <param name="subset">
            Коллекция, которая с точки зрения теста должна содержаться в <paramref name="superset"/>.
            </param>
            <param name="superset">
            Коллекция, которая с точки зрения теста должна содержать <paramref name="subset"/>.
            </param>
            <returns>
            Значение True, если <paramref name="subset"/> является подмножеством
            <paramref name="superset"/>, в противном случае — False.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.GetElementCounts(System.Collections.ICollection,System.Int32@)">
            <summary>
            Создает словарь с числом вхождений каждого элемента
            в указанной коллекции.
            </summary>
            <param name="collection">
            Обрабатываемая коллекция.
            </param>
            <param name="nullCount">
            Число элементов, имеющих значение NULL, в коллекции.
            </param>
            <returns>
            Словарь с числом вхождений каждого элемента
            в указанной коллекции.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.FindMismatchedElement(System.Collections.ICollection,System.Collections.ICollection,System.Int32@,System.Int32@,System.Object@)">
            <summary>
            Находит несоответствующий элемент между двумя коллекциями. Несоответствующий
            элемент — это элемент, количество вхождений которого в ожидаемой коллекции отличается
            от фактической коллекции. В качестве коллекций
            ожидаются различные ссылки, отличные от null, с одинаковым
            количеством элементов. За этот уровень проверки отвечает
            вызывающий объект. Если несоответствующих элементов нет, функция возвращает
            False, и выходные параметры использовать не следует.
            </summary>
            <param name="expected">
            Первая сравниваемая коллекция.
            </param>
            <param name="actual">
            Вторая сравниваемая коллекция.
            </param>
            <param name="expectedCount">
            Ожидаемое число вхождений
            <paramref name="mismatchedElement"/> или 0, если несоответствующие элементы
            отсутствуют.
            </param>
            <param name="actualCount">
            Фактическое число вхождений
            <paramref name="mismatchedElement"/> или 0, если несоответствующие элементы
            отсутствуют.
            </param>
            <param name="mismatchedElement">
            Несоответствующий элемент (может иметь значение NULL) или значение NULL, если несоответствующий
            элемент отсутствует.
            </param>
            <returns>
            Значение True, если был найден несоответствующий элемент, в противном случае — False.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.ObjectComparer">
            <summary>
            сравнивает объекты при помощи object.Equals
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException">
            <summary>
            Базовый класс для исключений платформы.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String,System.Exception)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
            <param name="msg"> Сообщение. </param>
            <param name="ex"> Исключение. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
            <param name="msg"> Сообщение. </param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages">
            <summary>
              Строго типизированный класс ресурса для поиска локализованных строк и т. д.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ResourceManager">
            <summary>
              Возвращает кэшированный экземпляр ResourceManager, использованный этим классом.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Culture">
            <summary>
              Переопределяет свойство CurrentUICulture текущего потока для всех операций
              поиска ресурсов, в которых используется этот строго типизированный класс.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AccessStringInvalidSyntax">
            <summary>
              Ищет локализованную строку, похожую на "Синтаксис строки доступа неверен".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ActualHasMismatchedElements">
            <summary>
              Ищет локализованную строку, похожую на "Ожидаемая коллекция содержит {1} вхождений &lt;{2}&gt;. Фактическая коллекция содержит {3} вхождений. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AllItemsAreUniqueFailMsg">
            <summary>
              Ищет локализованную строку, похожую на "Обнаружен элемент-дубликат: &lt;{1}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualCaseFailMsg">
            <summary>
              Ищет локализованную строку, похожую на "Ожидаемое: &lt;{1}&gt;. Фактическое значение имеет другой регистр: &lt;{2}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDeltaFailMsg">
            <summary>
              Ищет локализованную строку, похожую на "Различие между ожидаемым значением &lt;{1}&gt; и фактическим значением &lt;{2}&gt; должно было составлять не больше &lt;{3}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDifferentTypesFailMsg">
            <summary>
              Ищет локализованную строку, похожую на "Ожидаемое: &lt;{1} ({2})&gt;. Фактическое: &lt;{3} ({4})&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualFailMsg">
            <summary>
              Ищет локализованную строку, похожую на "Ожидаемое: &lt;{1}&gt;. Фактическое: &lt;{2}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualDeltaFailMsg">
            <summary>
              Ищет локализованную строку, похожую на "Различие между ожидаемым значением &lt;{1}&gt; и фактическим значением &lt;{2}&gt; должно было составлять больше &lt;{3}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualFailMsg">
            <summary>
              Ищет локализованную строку, похожую на "Ожидалось любое значение, кроме: &lt;{1}&gt;. Фактическое значение: &lt;{2}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreSameGivenValues">
            <summary>
              Ищет локализованную строку, похожую на "Не передавайте типы значений в AreSame(). Значения, преобразованные в объекты, никогда не будут одинаковыми. Воспользуйтесь методом AreEqual(). {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AssertionFailed">
            <summary>
              Ищет локализованную строку, похожую на "Сбой {0}. {1}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AsyncUITestMethodNotSupported">
            <summary>
              Ищет локализованную строку, аналогичную "Асинхронный метод TestMethod с UITestMethodAttribute не поддерживается. Удалите async или используйте TestMethodAttribute".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsEmpty">
            <summary>
              Ищет локализованную строку, похожую на "Обе коллекции пусты. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameElements">
            <summary>
              Ищет локализованную строку, похожую на "Обе коллекции содержат одинаковые элементы".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameReference">
            <summary>
              Ищет локализованную строку, похожую на "Ссылки на обе коллекции указывают на один объект коллекции. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothSameElements">
            <summary>
              Ищет локализованную строку, похожую на "Обе коллекции содержат одинаковые элементы. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.CollectionEqualReason">
            <summary>
              Ищет локализованную строку, похожую на "{0}({1})".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_NullInMessages">
            <summary>
              Ищет локализованную строку, похожую на "(NULL)".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_ObjectString">
            <summary>
              Ищет локализованную строку, похожую на "(объект)".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ContainsFail">
            <summary>
              Ищет локализованную строку, похожую на "Строка "{0}" не содержит строку "{1}". {2}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DataDrivenResultDisplayName">
            <summary>
              Ищет локализованную строку, похожую на "{0} ({1})".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DoNotUseAssertEquals">
            <summary>
              Ищет локализованную строку, похожую на "Assert.Equals не следует использовать для Assertions. Используйте Assert.AreEqual и переопределения".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementNumbersDontMatch">
            <summary>
              Ищет локализованную строку, похожую на "Число элементов в коллекциях не совпадает. Ожидаемое число: &lt;{1}&gt;. Фактическое: &lt;{2}&gt;.{0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementsAtIndexDontMatch">
            <summary>
              Ищет локализованную строку, похожую на "Элемент с индексом {0} не соответствует".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch">
            <summary>
              Ищет локализованную строку, похожую на "Элемент с индексом {1} имеет непредвиденный тип. Ожидаемый тип: &lt;{2}&gt;. Фактический тип: &lt;{3}&gt;.{0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch2">
            <summary>
              Ищет локализованную строку, похожую на "Элемент с индексом {1} имеет значение (NULL). Ожидаемый тип: &lt;{2}&gt;.{0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EndsWithFail">
            <summary>
              Ищет локализованную строку, похожую на "Строка "{0}" не заканчивается строкой "{1}". {2}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EqualsTesterInvalidArgs">
            <summary>
              Ищет локализованную строку, похожую на "Недопустимый аргумент — EqualsTester не может использовать значения NULL".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ErrorInvalidCast">
            <summary>
              Ищет локализованную строку, похожую на "Невозможно преобразовать объект типа {0} в {1}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InternalObjectNotValid">
            <summary>
              Ищет локализованную строку, похожую на "Внутренний объект, на который была сделана ссылка, более не действителен".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidParameterToAssert">
            <summary>
              Ищет локализованную строку, похожую на "Параметр "{0}" недопустим. {1}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidPropertyType">
            <summary>
              Ищет локализованную строку, похожую на "Свойство {0} имеет тип {1}; ожидаемый тип: {2}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsInstanceOfFailMsg">
            <summary>
              Ищет локализованную строку, похожую на "{0} Ожидаемый тип: &lt;{1}&gt;. Фактический тип: &lt;{2}&gt;".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsMatchFail">
            <summary>
              Ищет локализованную строку, похожую на "Строка "{0}" не соответствует шаблону "{1}". {2}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotInstanceOfFailMsg">
            <summary>
              Ищет локализованную строку, похожую на "Неправильный тип: &lt;{1}&gt;. Фактический тип: &lt;{2}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotMatchFail">
            <summary>
              Ищет локализованную строку, похожую на "Строка "{0}" соответствует шаблону "{1}". {2}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoDataRow">
            <summary>
              Ищет локализованную строку, похожую на "Не указан атрибут DataRowAttribute. Необходимо указать как минимум один атрибут DataRowAttribute с атрибутом DataTestMethodAttribute".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoExceptionThrown">
            <summary>
              Ищет локализованную строку, похожую на "Исключение не было создано. Ожидалось исключение {1}. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NullParameterToAssert">
            <summary>
              Ищет локализованную строку, похожую на "Параметр "{0}" недопустим. Значение не может быть равно NULL. {1}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NumberOfElementsDiff">
            <summary>
              Ищет локализованную строку, похожую на "Число элементов различается".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorConstructorNotFound">
            <summary>
              Ищет локализованную строку, похожую на 
                 "Не удалось найти конструктор с указанной сигнатурой. Возможно, потребуется повторно создать закрытый метод доступа,
                 или элемент может быть закрытым и определяться в базовом классе. В последнем случае необходимо передать тип,
                  определяющий элемент, в конструктор класса PrivateObject".
               .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorMemberNotFound">
            <summary>
              Ищет локализованную строку, похожую на 
                 "Не удалось найти указанный элемент ({0}). Возможно, потребуется повторно создать закрытый метод доступа,
                 или элемент может быть закрытым и определяться в базовом классе. В последнем случае необходимо передать тип,
                  определяющий элемент, в конструктор PrivateObject".
               .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.StartsWithFail">
            <summary>
              Ищет локализованную строку, похожую на "Строка "{0}" не начинается со строки "{1}". {2}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_ExpectedExceptionTypeMustDeriveFromException">
            <summary>
              Ищет локализованную строку, похожую на "Ожидаемое исключение должно иметь тип System.Exception или производный от него тип".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_FailedToGetExceptionMessage">
            <summary>
              Ищет локализованную строку, похожую на "(Не удалось получить сообщение для исключения типа {0} из-за исключения.)".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoException">
            <summary>
              Ищет локализованную строку, похожую на "Метод теста не создал ожидаемое исключение {0}. {1}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoExceptionDefault">
            <summary>
              Ищет локализованную строку, похожую на "Метод теста не создал исключение. Исключение ожидалось атрибутом {0}, определенным в методе теста".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongException">
            <summary>
              Ищет локализованную строку, похожую на "Метод теста создан исключение {0}, а ожидалось исключение {1}. Сообщение исключения: {2}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongExceptionDerivedAllowed">
            <summary>
              Ищет локализованную строку, похожую на "Метод теста создал исключение {0}, а ожидалось исключение {1} или производный от него тип. Сообщение исключения: {2}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.WrongExceptionThrown">
             <summary>
               Ищет локализованную строку, похожую на "Создано исключение {2}, а ожидалось исключение {1}. {0}
            Сообщение исключения: {3}
            Стек трассировки: {4}".
             </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome">
            <summary>
            результаты модульного теста
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Failed">
            <summary>
            Тест был выполнен, но при его выполнении возникли проблемы.
            Эти проблемы могут включать исключения или сбой утверждений.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Inconclusive">
            <summary>
            Тест завершен, но результат его завершения неизвестен.
            Может использоваться для прерванных тестов.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Passed">
            <summary>
            Тест был выполнен без проблем.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.InProgress">
            <summary>
            Тест выполняется в данный момент.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Error">
            <summary>
            При попытке выполнения теста возникла ошибка в системе.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Timeout">
            <summary>
            Время ожидания для теста истекло.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Aborted">
            <summary>
            Тест прерван пользователем.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Unknown">
            <summary>
            Тест находится в неизвестном состоянии
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper">
            <summary>
            Предоставляет вспомогательные функции для платформы модульных тестов
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper.GetExceptionMsg(System.Exception)">
            <summary>
            Получает сообщения с исключениями, включая сообщения для всех внутренних исключений
            (рекурсивно)
            </summary>
            <param name="ex">Исключение, для которого следует получить сообщения</param>
            <returns>строка с сообщением об ошибке</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout">
            <summary>
            Перечисление для времен ожидания, которое можно использовать с классом <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> .
            Тип перечисления должен соответствовать
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout.Infinite">
            <summary>
            Бесконечно.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute">
            <summary>
            Атрибут тестового класса.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute.GetTestMethodAttribute(Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute)">
            <summary>
            Получает атрибут метода теста, включающий выполнение этого теста.
            </summary>
            <param name="testMethodAttribute">Для этого метода определен экземпляр атрибута метода теста.</param>
            <returns>
             <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/> для использования для выполнения этого теста.</returns>
            <remarks>Extensions can override this method to customize how all methods in a class are run.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute">
            <summary>
            Атрибут метода теста.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Выполняет метод теста.
            </summary>
            <param name="testMethod">Выполняемый метод теста.</param>
            <returns>Массив объектов TestResult, представляющих результаты теста.</returns>
            <remarks>Extensions can override this method to customize running a TestMethod.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestInitializeAttribute">
            <summary>
            Атрибут инициализации теста.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCleanupAttribute">
            <summary>
            Атрибут очистки теста.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute">
            <summary>
            Атрибут игнорирования.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute">
            <summary>
            Атрибут свойства теста.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.#ctor(System.String,System.String)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute"/>.
            </summary>
            <param name="name">
            Имя.
            </param>
            <param name="value">
            Значение.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Name">
            <summary>
            Получает имя.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Value">
            <summary>
            Получает значение.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute">
            <summary>
            Атрибут инициализации класса.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute">
            <summary>
            Атрибут очистки класса.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyInitializeAttribute">
            <summary>
            Атрибут инициализации сборки.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyCleanupAttribute">
            <summary>
            Атрибут очистки сборки.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute">
            <summary>
            Владелец теста
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.#ctor(System.String)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute"/> .
            </summary>
            <param name="owner">
            Владелец.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.Owner">
            <summary>
            Получает владельца.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute">
            <summary>
            Атрибут Priority; используется для указания приоритета модульного теста.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.#ctor(System.Int32)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute"/> .
            </summary>
            <param name="priority">
            Приоритет.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.Priority">
            <summary>
            Получает приоритет.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute">
            <summary>
            Описание теста
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.#ctor(System.String)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute"/> для описания теста.
            </summary>
            <param name="description">Описание.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.Description">
            <summary>
            Получает описание теста.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute">
            <summary>
            URI структуры проекта CSS
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.#ctor(System.String)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute"/> для URI структуры проекта CSS.
            </summary>
            <param name="cssProjectStructure">URI структуры проекта CSS.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.CssProjectStructure">
            <summary>
            Получает URI структуры проекта CSS.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute">
            <summary>
            URI итерации CSS
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.#ctor(System.String)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute"/> для URI итерации CSS.
            </summary>
            <param name="cssIteration">URI итерации CSS.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.CssIteration">
            <summary>
            Получает URI итерации CSS.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute">
            <summary>
            Атрибут WorkItem; используется для указания рабочего элемента, связанного с этим тестом.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.#ctor(System.Int32)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute"/> для атрибута WorkItem.
            </summary>
            <param name="id">Идентификатор рабочего элемента.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.Id">
            <summary>
            Получает идентификатор связанного рабочего элемента.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute">
            <summary>
            Атрибут Timeout; используется для указания времени ожидания модульного теста.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(System.Int32)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> .
            </summary>
            <param name="timeout">
            Время ожидания.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> с заданным временем ожидания
            </summary>
            <param name="timeout">
            Время ожидания
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.Timeout">
            <summary>
            Получает время ожидания.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult">
            <summary>
            Объект TestResult, который возвращается адаптеру.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.#ctor">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/> .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DisplayName">
            <summary>
            Получает или задает отображаемое имя результата. Удобно для возврата нескольких результатов.
            Если параметр равен NULL, имя метода используется в качестве DisplayName.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Outcome">
            <summary>
            Получает или задает результат выполнения теста.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestFailureException">
            <summary>
            Получает или задает исключение, создаваемое, если тест не пройден.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogOutput">
            <summary>
            Получает или задает выходные данные сообщения, записываемого кодом теста.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogError">
            <summary>
            Получает или задает выходные данные сообщения, записываемого кодом теста.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DebugTrace">
            <summary>
            Получает или задает трассировки отладки для кода теста.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestContextMessages">
            <summary>
            Gets or sets the debug traces by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Duration">
            <summary>
            Получает или задает продолжительность выполнения теста.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DatarowIndex">
            <summary>
            Возвращает или задает индекс строки данных в источнике данных. Задается только для результатов выполнения
            отдельных строк данных для теста, управляемого данными.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ReturnValue">
            <summary>
            Получает или задает возвращаемое значение для метода теста. (Сейчас всегда равно NULL.)
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ResultFiles">
            <summary>
            Возвращает или задает файлы результатов, присоединенные во время теста.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute">
            <summary>
            Задает строку подключения, имя таблицы и метод доступа к строкам для тестов, управляемых данными.
            </summary>
            <example>
            [DataSource("Provider=SQLOLEDB.1;Data Source=source;Integrated Security=SSPI;Initial Catalog=EqtCoverage;Persist Security Info=False", "MyTable")]
            [DataSource("dataSourceNameFromConfigFile")]
            </example>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultProviderName">
            <summary>
            Имя поставщика по умолчанию для DataSource.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultDataAccessMethod">
            <summary>
            Метод доступа к данным по умолчанию.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String,System.String,Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> . Этот экземпляр инициализируется с поставщиком данных, строкой подключения, таблицей данных и методом доступа к данным для доступа к источнику данных.
            </summary>
            <param name="providerInvariantName">Имя инвариантного поставщика данных, например System.Data.SqlClient</param>
            <param name="connectionString">
            Строка подключения для поставщика данных. 
            Внимание! Строка подключения может содержать конфиденциальные данные (например, пароль).
            Строка подключения хранится в виде открытого текста в исходном коде и в скомпилированной сборке. 
            Ограничьте доступ к исходному коду и сборке для защиты конфиденциальных данных.
            </param>
            <param name="tableName">Имя таблицы данных.</param>
            <param name="dataAccessMethod">Задает порядок доступа к данным.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> . Этот экземпляр будет инициализирован с строкой подключения и именем таблицы.
            Укажите строку подключения и таблицу данных для доступа к источнику данных OLEDB.
            </summary>
            <param name="connectionString">
            Строка подключения для поставщика данных. 
            Внимание! Строка подключения может содержать конфиденциальные данные (например, пароль).
            Строка подключения хранится в виде открытого текста в исходном коде и в скомпилированной сборке. 
            Ограничьте доступ к исходному коду и сборке для защиты конфиденциальных данных.
            </param>
            <param name="tableName">Имя таблицы данных.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> .  Этот экземпляр инициализируется с поставщиком данных и строкой подключения, связанной с именем параметра.
            </summary>
            <param name="dataSourceSettingName">Имя источника данных, обнаруженного в разделе &lt;microsoft.visualstudio.qualitytools&gt; файла app.config.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ProviderInvariantName">
            <summary>
            Получает значение, представляющее поставщик данных для источника данных.
            </summary>
            <returns>
            Имя поставщика данных. Если поставщик данных не был определен при инициализации объекта, будет возвращен поставщик по умолчанию, System.Data.OleDb.
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ConnectionString">
            <summary>
            Получает значение, представляющее строку подключения для источника данных.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.TableName">
            <summary>
            Получает значение с именем таблицы, содержащей данные.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataAccessMethod">
             <summary>
            Возвращает метод, используемый для доступа к источнику данных.
            </summary>
            
             <returns>
             Один из <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod"/> значений. Если <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> не инициализировано, возвращается значение по умолчанию <see cref="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random"/>.
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataSourceSettingName">
            <summary>
            Возвращает имя источника данных, обнаруженное в разделе &lt;microsoft.visualstudio.qualitytools&gt; файла app.config.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute">
            <summary>
            Атрибут для тестов, управляемых данными, в которых данные могут быть встроенными.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Найти все строки данных и выполнить.
            </summary>
            <param name="testMethod">
            Метод теста.
            </param>
            <returns>
            Массив <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.RunDataDrivenTest(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod,Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute[])">
            <summary>
            Выполнение метода теста, управляемого данными.
            </summary>
            <param name="testMethod"> Выполняемый метод теста. </param>
            <param name="dataRows"> Строка данных. </param>
            <returns> Результаты выполнения. </returns>
        </member>
    </members>
</doc>
