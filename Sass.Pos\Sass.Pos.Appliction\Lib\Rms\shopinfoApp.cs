﻿using Saas.Pos.Model.Rms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Appliction.Lib.Rms
{
    public partial class shopinfoApp : AppBase<shopinfo>
    {
        /// <summary>
        /// 获取可预约门店信息,根据某个时间段进行过滤
        /// </summary>
        /// <param name="time">某个时间段</param>
        /// <returns></returns>
        public List<shopinfo> GetBookShop(DateTime time)
        {
            return this.IQueryable().ToList();
        }
    }
}
