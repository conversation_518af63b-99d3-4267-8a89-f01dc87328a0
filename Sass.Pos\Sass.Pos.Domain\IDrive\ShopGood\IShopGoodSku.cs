﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive
{
    public interface IShopGoodSku
    {
        ResponseContext<RespPaginationModel<GetSkuDataModel>> GetSkuData(GetSkuDataContext context);

        int SaveGoodSku(List<SaveShopGoodSku> context, int spuId);

        ResponseContext<int> DeleteShopSku(DeleteShopGoodSkuContext context);

        /// <summary>
        /// 查询所有商品关联信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        //List<int> GetItemGoodSku(List<OrderItemDataContext> OrderItemDatas);

        /// <summary>
        /// 判断所有商品是不是同一种消费模式
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        bool JudgeItemGoodSku(List<OrderItemDataContext> OrderItemDatas);

        /// <summary>
        /// 查询所有商品是否预付
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        GetPermitMode GetItemisAdvance(List<OrderItemDataContext> OrderItemDatas);
    }
}
