﻿using Saas.Pos.Common.MemberInfo.Benefits;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.MemberInfo.Context
{
    public class BenefitConfigContext<T>
    {
        public T Data { get; set; }

        public int Val1 { get; set; }

        public string Val2 { get; set; }
    }

    public class BenefitsHold<T>
    {
        public List<BenefitsBase<T>> BenefitsList { get; set; }

        public string Val { get; set; }
    }
}
