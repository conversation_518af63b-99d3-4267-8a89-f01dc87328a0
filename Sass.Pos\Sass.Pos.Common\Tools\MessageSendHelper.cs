﻿using ComponentCore.Threading.Impl;
using Newtonsoft.Json;
using Saas.Pos.Common.Extend;
using Saas.Pos.Common.Log;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Web;

namespace Saas.Pos.Common.Tools
{
    public class MessageSendHelper
    {
        static object obj = new object();

        /// <summary>
        /// 发送短信
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public static bool PhoneSendMessage(PhoneSendMessageContext context)
        {
            try
            {
                var rspData = false;

                var message = context.Clone<MessageContext>();

                // 设置最大尝试次数和已尝试次数
                int maxAttempts = 3;
                int attempts = 0;

                // 发送短信直到成功或达到最大尝试次数
                while (attempts < maxAttempts)
                {
                    try
                    {
                        if (context.ReqType == 1)//http发送
                        {
                            if (/*HttpSendSMS(message)*/true)
                            {
                                rspData = /*HttpSendSMS(message)*/Send(context);
                                break; // 如果发送成功，跳出循环
                            }
                        }
                        else if (context.ReqType == 2)//mq发送
                            if (/*MqSendSMS(message)*/true)
                            {
                                rspData = /*MqSendSMS(message)*/true;
                                break; // 如果发送成功，跳出循环
                            }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Info(DateTime.Now + "发送短信\n错误:" + ex.Message);
                    }

                    attempts++; // 增加尝试次数
                }

                // 如果达到最大尝试次数仍未成功，则输出失败消息
                if (attempts == maxAttempts)
                {
                    LogHelper.Info(DateTime.Now + "发送短信\n错误:尝试补发失败数据\n参数:" + JsonConvert.SerializeObject(context));
                }

                return rspData;
            }
            catch (Exception ex)
            {
                throw new ExMessage(ex.Message);
            }
        }

        public static bool Send(PhoneSendMessageContext context)
        {
            ThreadHttpHandle threadHttp = new ThreadHttpHandle();

            StringBuilder builder;

            var isBool = false;

            builder = new StringBuilder("http://msg.tang-hui.com.cn/API/Phone/NSendMsg.ashx");
            builder.Append("?no=");
            builder.Append(context.No);
            if (!string.IsNullOrEmpty(context.Title))
            {
                builder.Append("&m=");
                builder.Append(context.Title);
            }
            builder.Append("&phone=");
            builder.Append(context.PhoneNumber);
            //builder.Append("13416176281");
            //builder.Append("13420870324");
            builder.Append("&msg=");
            //builder.Append("尊敬的贵宾，感谢您一直以来对堂会番禺店的支持与信赖！番禺华丰汇购物中心（原汇珑新天地）全面升级改造中，堂会番禺店正常营业，特赠您 4 免 1 K%2b自助餐券 1 张，点击 {0} 领取，欢迎到店体验！详询堂会预约中心 020-83333999！转发无效。拒收请回复R");
            builder.Append(context.Message);
            //1是营销，0是通知
            if (context.Title == "1")
                builder.Append("拒收请回复R");

            var rspData = threadHttp.Post(builder.ToString());
            if (!string.IsNullOrEmpty(rspData))
            {
                try
                {
                    var repJson = rspData.ToJObject();
                    var data = repJson["data"]["resmsg"];
                    var resmsg = data.ToString().ToJObject();
                    isBool = resmsg["description"].ToString() == "发送成功";
                }
                catch (Exception)
                {
                    isBool = false;
                }

            }

            return isBool;
        }
    }

    public class PhoneSendMessageContext
    {
        /// <summary>
        /// 请求方式
        /// </summary>
        public int ReqType { get; set; }

        /// <summary>
        /// 抬头(1:营销;0:通知)
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 电话号
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// 信息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 签名
        /// </summary>
        public int No { get; set; }
    }

    public class MessageContext
    {
        /// <summary>
        /// 抬头
        /// </summary>
        public int Title { get; set; }

        /// <summary>
        /// 电话号
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// 信息
        /// </summary>
        public string Message { get; set; }
    }
}
