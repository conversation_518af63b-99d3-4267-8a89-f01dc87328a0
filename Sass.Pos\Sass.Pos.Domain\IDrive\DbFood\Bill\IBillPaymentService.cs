﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.DbFood.Bill
{
    public interface IBillPaymentService
    {
        /// <summary>
        /// 获取账单支付信息汇总数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<GetBillPaymentSummary> GetBillPaymentData(GetHistoryBillDataContext context);

        /// <summary>
        /// 获取账单支付信息详情
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<List<GetBillPaymentDetail>> GetBillPaymentDetail(GetHistoryBillDataContext context);

        ResponseContext<List<GetBillPaymentDetail>> GetBillPaymentDetailByStore(GetHistoryBillDataContext context);

        ResponseContext<GetBillPaymentSummary> GetBillPaymentDataByStore(GetHistoryBillDataContext context);
    }
}
