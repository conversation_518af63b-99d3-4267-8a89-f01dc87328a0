﻿using Saas.Pos.Common.Log;
using System;
using System.Collections.Generic;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.Validation;
using System.Diagnostics;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Exceptions
{
    public static class ExceptionHandler
    {
        /// <summary>
        /// 异常提示转换
        /// </summary>
        /// <param name="ex">异常信息</param>
        public static void HandlerException(this Exception ex)
        {
            string actionName = string.Empty;
            StackTrace trace = new StackTrace();
            //获取上层调用的方法名
            actionName = trace.GetFrame(1).GetMethod().Name;
            var message = string.Empty;
            message = string.Format("调用{0}发生异常，异常信息：{1}，异常详细信息：", actionName, ex.Message);
            if (ex is DbUpdateException)
            {
                var exception = (DbUpdateException)ex;
                message += exception.InnerException == null ? string.Empty : exception.InnerException.Message;
                //记录相关信息到日志
                LogHelper.Info(message);
                throw new Exception("更新数据失败，失败原因：" + ex.Message);
            }
            else if (ex is DbUpdateConcurrencyException)
            {
                var exception = (DbUpdateConcurrencyException)ex;
                message += exception.InnerException == null ? string.Empty : exception.InnerException.Message;
                //记录相关信息到日志
                LogHelper.Info(message);
                throw new Exception("更新数据失败，失败原因：" + ex.Message);
            }
            else if (ex is DbEntityValidationException)
            {
                var exception = (DbEntityValidationException)ex;
                exception.EntityValidationErrors.ToList().ForEach(x =>
                {
                    x.ValidationErrors.ToList().ForEach(w =>
                    {
                        message += "错误字段：" + w.PropertyName + "；错误原因：" + w.ErrorMessage;
                    });
                });
                LogHelper.Info(message);
                throw new Exception("数据校验未通过！");
            }
            else if (ex is ObjectDisposedException)
            {
                var exception = (ObjectDisposedException)ex;
                LogHelper.Info(message);
                throw new Exception("数据库未连接，无法获取数据！");
            }
            else
            {
                message += ex.InnerException == null ? string.Empty : ex.InnerException.Message;
                LogHelper.Info(message);
                throw new Exception(ex.Message);
            }
        }
    }
}
