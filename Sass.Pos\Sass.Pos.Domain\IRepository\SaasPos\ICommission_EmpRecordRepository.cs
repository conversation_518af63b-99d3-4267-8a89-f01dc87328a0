﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IRepository.SaasPos
{
    public partial interface ICommission_EmpRecordRepository : IRepositoryBase<Commission_EmpRecord>
    {
        List<GetOrderEmpRecordModel> GetOrderSchemesList(GetOrderSchemesContext context);

        List<GetOrderEmpRecordModel> GetExcelEmpRecord(ExcelEmpRecordContext context);
    }
}
