﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IDrive.WineStockManage
{
    public interface IWineData
    {
        ResponseContext<GetWineDataModel> GetWineData(GetWineDataContext context);

        ResponseContext<RespPaginationModel<GetfdListEx>> GetFdList(GetfdListContext context);

        ResponseContext<EditFdDataModel> InsertFdData(List<EditFdDataContext> context);

        ResponseContext<EditFdDataModel> EditFdData(EditFdDataContext context);

        ResponseContext<DeleteFdDataModel> DeleteFdData(DeleteFdDataContext context);

        ResponseContext<RespPaginationModel<GetfdTypeList>> GetFtList(GetftListContext context);

        ResponseContext<List<GetfdTypeList>> GetfdTypeLists(GetfdTypeListsContext context);

        ResponseContext<EditFtDataModel> InsertFtData(List<EditFtDataContext> context);

        ResponseContext<EditFtDataModel> EditFtData(EditFtDataContext context);

        ResponseContext<DeleteFtDataModel> DeleteFtData(DeleteFtDataContext context);

        ResponseContext<RespPaginationModel<GetUnitList>> GetUnitList(GetUnitListContext context);

        ResponseContext<EditUnitDataModel> EditUnitData(EditUnitDataContext context);

        ResponseContext<DeleteUnitModel> DeleteUnit(DeleteUnitContext context);

    }
}
