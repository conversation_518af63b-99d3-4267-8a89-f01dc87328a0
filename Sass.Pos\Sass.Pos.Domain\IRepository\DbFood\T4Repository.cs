﻿

using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.DbFood
{
 public partial interface IAddItemRepository : IRepositoryBase<AddItem> {}
  

 public partial interface IAiTypeRepository : IRepositoryBase<AiType> {}
  

 public partial interface IAmountLogRepository : IRepositoryBase<AmountLog> {}
  

 public partial interface IBankInfoRepository : IRepositoryBase<BankInfo> {}
  

 public partial interface ICarLeaveLogRepository : IRepositoryBase<CarLeaveLog> {}
  

 public partial interface ICashierRepository : IRepositoryBase<Cashier> {}
  

 public partial interface IClearDataLogRepository : IRepositoryBase<ClearDataLog> {}
  

 public partial interface IDbFooddtpropertiesRepository : IRepositoryBase<DbFooddtproperties> {}
  

 public partial interface IDbFoodTh_RoomCommissionAllotRepository : IRepositoryBase<DbFoodTh_RoomCommissionAllot> {}
  

 public partial interface IDeadLockLogRepository : IRepositoryBase<DeadLockLog> {}
  

 public partial interface IDepositInfoRepository : IRepositoryBase<DepositInfo> {}
  

 public partial interface IDeptRepository : IRepositoryBase<Dept> {}
  

 public partial interface IDeptBanSetRepository : IRepositoryBase<DeptBanSet> {}
  

 public partial interface IEmpGift_CustRecordRepository : IRepositoryBase<EmpGift_CustRecord> {}
  

 public partial interface IEmpGift_ItemRepository : IRepositoryBase<EmpGift_Item> {}
  

 public partial interface IEmpGift_RecordRepository : IRepositoryBase<EmpGift_Record> {}
  

 public partial interface IFdCashRepository : IRepositoryBase<FdCash> {}
  

 public partial interface IFdCash_TrackRepository : IRepositoryBase<FdCash_Track> {}
  

 public partial interface IFdCashBakRepository : IRepositoryBase<FdCashBak> {}
  

 public partial interface IFdCashBak_BRepository : IRepositoryBase<FdCashBak_B> {}
  

 public partial interface IFdCashBak_BakRepository : IRepositoryBase<FdCashBak_Bak> {}
  

 public partial interface IFdCashOrderRepository : IRepositoryBase<FdCashOrder> {}
  

 public partial interface IFdCashPackRecordRepository : IRepositoryBase<FdCashPackRecord> {}
  

 public partial interface IFdDetTypeRepository : IRepositoryBase<FdDetType> {}
  

 public partial interface IFdImageRepository : IRepositoryBase<FdImage> {}
  

 public partial interface IFdInvRepository : IRepositoryBase<FdInv> {}
  

 public partial interface IFdInv_BRepository : IRepositoryBase<FdInv_B> {}
  

 public partial interface IFdInv_BakRepository : IRepositoryBase<FdInv_Bak> {}
  

 public partial interface IFdInv_ExchangeLogRepository : IRepositoryBase<FdInv_ExchangeLog> {}
  

 public partial interface IFdInvCashItemRepository : IRepositoryBase<FdInvCashItem> {}
  

 public partial interface IFdInvDescRepository : IRepositoryBase<FdInvDesc> {}
  

 public partial interface IFdTicketRepository : IRepositoryBase<FdTicket> {}
  

 public partial interface IFdTimePriceRepository : IRepositoryBase<FdTimePrice> {}
  

 public partial interface IFdTimeZoneRepository : IRepositoryBase<FdTimeZone> {}
  

 public partial interface IFdTypeRepository : IRepositoryBase<FdType> {}
  

 public partial interface IFdUserRepository : IRepositoryBase<FdUser> {}
  

 public partial interface IFdUserGradeRepository : IRepositoryBase<FdUserGrade> {}
  

 public partial interface IFdUserRightsRepository : IRepositoryBase<FdUserRights> {}
  

 public partial interface IFestivalTimeRepository : IRepositoryBase<FestivalTime> {}
  

 public partial interface IFoodRepository : IRepositoryBase<Food> {}
  

 public partial interface IFoodCalRepository : IRepositoryBase<FoodCal> {}
  

 public partial interface IFoodLabelRepository : IRepositoryBase<FoodLabel> {}
  

 public partial interface IFoodOrderMRepository : IRepositoryBase<FoodOrderM> {}
  

 public partial interface IFPrnRepository : IRepositoryBase<FPrn> {}
  

 public partial interface IFPrnDataRepository : IRepositoryBase<FPrnData> {}
  

 public partial interface IFPrnData_BakRepository : IRepositoryBase<FPrnData_Bak> {}
  

 public partial interface IFreePackageCoupon_RecordRepository : IRepositoryBase<FreePackageCoupon_Record> {}
  

 public partial interface IFtInfoRepository : IRepositoryBase<FtInfo> {}
  

 public partial interface IGDDB20InfoRepository : IRepositoryBase<GDDB20Info> {}
  

 public partial interface IGDDBInfoRepository : IRepositoryBase<GDDBInfo> {}
  

 public partial interface IGiftAccountRepository : IRepositoryBase<GiftAccount> {}
  

 public partial interface IGiftAccountOperationRecordRepository : IRepositoryBase<GiftAccountOperationRecord> {}
  

 public partial interface IGiftAccountSceneAllocationRepository : IRepositoryBase<GiftAccountSceneAllocation> {}
  

 public partial interface IGiftRoleRepository : IRepositoryBase<GiftRole> {}
  

 public partial interface IGiftSceneRepository : IRepositoryBase<GiftScene> {}
  

 public partial interface IGiftSceneEquityConfigRepository : IRepositoryBase<GiftSceneEquityConfig> {}
  

 public partial interface IGiftSceneRoleBindingRepository : IRepositoryBase<GiftSceneRoleBinding> {}
  

 public partial interface IHappyRabRepository : IRepositoryBase<HappyRab> {}
  

 public partial interface IHolidayRepository : IRepositoryBase<Holiday> {}
  

 public partial interface IHotFdTypeRepository : IRepositoryBase<HotFdType> {}
  

 public partial interface IHotFoodRepository : IRepositoryBase<HotFood> {}
  

 public partial interface IInv_MemberOperationRepository : IRepositoryBase<Inv_MemberOperation> {}
  

 public partial interface IInv_TimeSectionRepository : IRepositoryBase<Inv_TimeSection> {}
  

 public partial interface IInvRollBackRepository : IRepositoryBase<InvRollBack> {}
  

 public partial interface ILanIdRepository : IRepositoryBase<LanId> {}
  

 public partial interface ILanStringRepository : IRepositoryBase<LanString> {}
  

 public partial interface ILastInvNoRepository : IRepositoryBase<LastInvNo> {}
  

 public partial interface ILastRefNoRepository : IRepositoryBase<LastRefNo> {}
  

 public partial interface ILimit_ConfigInfoRepository : IRepositoryBase<Limit_ConfigInfo> {}
  

 public partial interface Imeal_distribution_infoRepository : IRepositoryBase<meal_distribution_info> {}
  

 public partial interface Imeal_infoRepository : IRepositoryBase<meal_info> {}
  

 public partial interface IMembAmountEditLogRepository : IRepositoryBase<MembAmountEditLog> {}
  

 public partial interface IMemberRepository : IRepositoryBase<Member> {}
  

 public partial interface IMemberCheckoutInfoRepository : IRepositoryBase<MemberCheckoutInfo> {}
  

 public partial interface IMemberDeductionInfoRepository : IRepositoryBase<MemberDeductionInfo> {}
  

 public partial interface IMemberGiveSetRepository : IRepositoryBase<MemberGiveSet> {}
  

 public partial interface IMembSetRepository : IRepositoryBase<MembSet> {}
  

 public partial interface IMGradeFdDiscRepository : IRepositoryBase<MGradeFdDisc> {}
  

 public partial interface IMobileFdGiveRepository : IRepositoryBase<MobileFdGive> {}
  

 public partial interface IMobileFoodRepository : IRepositoryBase<MobileFood> {}
  

 public partial interface IMobileFoodDiscRepository : IRepositoryBase<MobileFoodDisc> {}
  

 public partial interface IMobileFtTypeRepository : IRepositoryBase<MobileFtType> {}
  

 public partial interface IMobilePackGiveRepository : IRepositoryBase<MobilePackGive> {}
  

 public partial interface IMobilOrderItemRepository : IRepositoryBase<MobilOrderItem> {}
  

 public partial interface IMobilOrderTitleRepository : IRepositoryBase<MobilOrderTitle> {}
  

 public partial interface IMobilUserOrderTitleRepository : IRepositoryBase<MobilUserOrderTitle> {}
  

 public partial interface INewFdGiveRepository : IRepositoryBase<NewFdGive> {}
  

 public partial interface INewFdTypeRepository : IRepositoryBase<NewFdType> {}
  

 public partial interface INewFdTypeLinkRepository : IRepositoryBase<NewFdTypeLink> {}
  

 public partial interface INewMemberLogRepository : IRepositoryBase<NewMemberLog> {}
  

 public partial interface IParamSetRepository : IRepositoryBase<ParamSet> {}
  

 public partial interface Ipre_orderRepository : IRepositoryBase<pre_order> {}
  

 public partial interface IPreOrderSendMsgInfoRepository : IRepositoryBase<PreOrderSendMsgInfo> {}
  

 public partial interface IPrepaymentItemRepository : IRepositoryBase<PrepaymentItem> {}
  

 public partial interface IPrepaymentRecordRepository : IRepositoryBase<PrepaymentRecord> {}
  

 public partial interface IPriceNoRepository : IRepositoryBase<PriceNo> {}
  

 public partial interface IQrInfoRepository : IRepositoryBase<QrInfo> {}
  

 public partial interface IRecordRoomTimeRepository : IRepositoryBase<RecordRoomTime> {}
  

 public partial interface IRefToZDLogRepository : IRepositoryBase<RefToZDLog> {}
  

 public partial interface IRightSetRepository : IRepositoryBase<RightSet> {}
  

 public partial interface IRmAccountInfoRepository : IRepositoryBase<RmAccountInfo> {}
  

 public partial interface IRmAreaRepository : IRepositoryBase<RmArea> {}
  

 public partial interface IRmClearLogRepository : IRepositoryBase<RmClearLog> {}
  

 public partial interface IRmCloseInfoRepository : IRepositoryBase<RmCloseInfo> {}
  

 public partial interface IRmCloseInfo_CollectRepository : IRepositoryBase<RmCloseInfo_Collect> {}
  

 public partial interface IRmExchangeDetailRepository : IRepositoryBase<RmExchangeDetail> {}
  

 public partial interface IRmExchangeLogRepository : IRepositoryBase<RmExchangeLog> {}
  

 public partial interface IRmFtPrnIndexRepository : IRepositoryBase<RmFtPrnIndex> {}
  

 public partial interface IRmOrderRepository : IRepositoryBase<RmOrder> {}
  

 public partial interface IRmOrderDelLogRepository : IRepositoryBase<RmOrderDelLog> {}
  

 public partial interface IRmOrderLogRepository : IRepositoryBase<RmOrderLog> {}
  

 public partial interface IRmsRoomRepository : IRepositoryBase<RmsRoom> {}
  

 public partial interface IRmTypeRepository : IRepositoryBase<RmType> {}
  

 public partial interface IRoomRepository : IRepositoryBase<Room> {}
  

 public partial interface IRoom_Consume_NumberRepository : IRepositoryBase<Room_Consume_Number> {}
  

 public partial interface IRoom_Consume_Number_ItmeRepository : IRepositoryBase<Room_Consume_Number_Itme> {}
  

 public partial interface IRoomCloseLabelRepository : IRepositoryBase<RoomCloseLabel> {}
  

 public partial interface IRoomCloseLabelTypeDetailRepository : IRepositoryBase<RoomCloseLabelTypeDetail> {}
  

 public partial interface IRoomCommissionRepository : IRepositoryBase<RoomCommission> {}
  

 public partial interface IRoomExtendRepository : IRepositoryBase<RoomExtend> {}
  

 public partial interface IRoomtestRepository : IRepositoryBase<Roomtest> {}
  

 public partial interface IRtAutoRepository : IRepositoryBase<RtAuto> {}
  

 public partial interface IRtAutoZDRepository : IRepositoryBase<RtAutoZD> {}
  

 public partial interface IRtTimePriceRepository : IRepositoryBase<RtTimePrice> {}
  

 public partial interface IS_AccTypeRepository : IRepositoryBase<S_AccType> {}
  

 public partial interface IS_CashItemRepository : IRepositoryBase<S_CashItem> {}
  

 public partial interface IS_PrnTypeRepository : IRepositoryBase<S_PrnType> {}
  

 public partial interface IS_RmStatusRepository : IRepositoryBase<S_RmStatus> {}
  

 public partial interface ISceneRole_ConfigRepository : IRepositoryBase<SceneRole_Config> {}
  

 public partial interface ISceneRole_Config_ExtraRepository : IRepositoryBase<SceneRole_Config_Extra> {}
  

 public partial interface ISchedulingRecordRepository : IRepositoryBase<SchedulingRecord> {}
  

 public partial interface ISDateRepository : IRepositoryBase<SDate> {}
  

 public partial interface IShareSetInfoRepository : IRepositoryBase<ShareSetInfo> {}
  

 public partial interface IShiftInfoRepository : IRepositoryBase<ShiftInfo> {}
  

 public partial interface IStarInfoRepository : IRepositoryBase<StarInfo> {}
  

 public partial interface ITestTableRepository : IRepositoryBase<TestTable> {}
  

 public partial interface ItriggerRecordRepository : IRepositoryBase<triggerRecord> {}
  

 public partial interface IUserAmountRepository : IRepositoryBase<UserAmount> {}
  

 public partial interface IUserAmountDetailRepository : IRepositoryBase<UserAmountDetail> {}
  

 public partial interface IUserFtZDRepository : IRepositoryBase<UserFtZD> {}
  

 public partial interface IUserInfo_BindingRepository : IRepositoryBase<UserInfo_Binding> {}
  

 public partial interface IUserIORepository : IRepositoryBase<UserIO> {}
  

 public partial interface IUserZDItemRepository : IRepositoryBase<UserZDItem> {}
  

 public partial interface IUserZDItemDetailRepository : IRepositoryBase<UserZDItemDetail> {}
  

 public partial interface IUserZDSetRepository : IRepositoryBase<UserZDSet> {}
  

 public partial interface IVesaRepository : IRepositoryBase<Vesa> {}
  

 public partial interface IWebOrderTableRepository : IRepositoryBase<WebOrderTable> {}
  

 public partial interface IWeChatFoodOrderMsgRepository : IRepositoryBase<WeChatFoodOrderMsg> {}
  

 public partial interface IWeChatFoodOrderMsg2Repository : IRepositoryBase<WeChatFoodOrderMsg2> {}
  

 public partial interface IWindTicketRepository : IRepositoryBase<WindTicket> {}
  

 public partial interface Iwx_shopmall_worktimeRepository : IRepositoryBase<wx_shopmall_worktime> {}
  

 public partial interface Iwxpay_FdCashOrderRepository : IRepositoryBase<wxpay_FdCashOrder> {}
  

 public partial interface IwxPayCheckInfoRepository : IRepositoryBase<wxPayCheckInfo> {}
  

 public partial interface IwxPayInfoRepository : IRepositoryBase<wxPayInfo> {}
  

 public partial interface IwxPayInfo_DelRepository : IRepositoryBase<wxPayInfo_Del> {}
  

}
