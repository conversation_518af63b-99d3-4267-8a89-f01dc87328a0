﻿using ComponentApplicationServiceInterface.Repository;
using ComponentCore.extend;
using Saas.Pos.RepositoryFactory.T4.Rms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;

namespace Saas.Pos.Appliction.Lib.Rms
{
    public abstract class AppBase<T> where T : class, new()
    {
        public AppBase()
        {
            ibase = SetRepository(_Repository);
        }
        protected abstract IRepositoryBase<T> SetRepository(RepositorySession Session);
        RepositorySession _Repository = RepositorySessionFactory.CreateRepositorySession();
        public RepositorySession Repository { get { return _Repository; } }

        IRepositoryBase<T> ibase;
        public virtual int Delete(System.Linq.Expressions.Expression<Func<T, bool>> predicate)
        {
            ibase.Delete(predicate);
            return SaveChanges();
        }

        public virtual int Delete(T entity)
        {
            ibase.Delete(entity);
            return SaveChanges();
        }

        public virtual T FindEntity(System.Linq.Expressions.Expression<Func<T, bool>> predicate)
        {
            return ibase.FindEntity(predicate);

        }

        public virtual T FindEntity(object keyValue)
        {
            return ibase.FindEntity(keyValue);

        }

        public virtual List<T> FindList(System.Linq.Expressions.Expression<Func<T, bool>> predicate, ComponentApplicationServiceInterface.Web.Pagination pagination)
        {
            return ibase.FindList(predicate, pagination);

        }

        public virtual List<T> FindList(ComponentApplicationServiceInterface.Web.Pagination pagination)
        {
            return ibase.FindList(pagination);

        }

        public virtual IQueryable<T> IQueryable(System.Linq.Expressions.Expression<Func<T, bool>> predicate)
        {
            return ibase.IQueryable(predicate);

        }

        public virtual IQueryable<T> IQueryable()
        {
            return ibase.IQueryable();

        }

        public virtual int Insert(List<T> entitys)
        {
            ibase.Insert(entitys);
            return SaveChanges();
        }

        public virtual int Insert(T entity)
        {
            ibase.Insert(entity);
            return SaveChanges();
        }

        public virtual int Update(T entity)
        {
            ibase.Update(entity);
            return SaveChanges();
        }
        public int SaveChanges()
        {
            return ibase.SaveChanges();
        }

        public Expression<Func<T, bool>> Expression()
        {
            return ExtLinq.True<T>();

        }
    }
}
