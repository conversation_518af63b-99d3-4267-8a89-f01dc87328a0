﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.StoreManage
{
    public interface IStoreManageBusiness
    {
        /// <summary>
        /// 获取门店管理营业时间数据(常规营业时间)
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        List<BusinessTime> GetStoreManageTimeDatas(GetStoreManageContext context);

        /// <summary>
        /// 获取门店管理营业时间数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        List<BusinessTime> GetStoreManageTimeDatasEx(GetStoreManageContext context);

        /// <summary>
        /// 获取门店管理营业时间数据切换查询商品时调用
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        List<BusinessTime> GetStoreManageTimeDatasEx(GetStoreManageExContext context);

        /// <summary>
        /// 添加营业时间
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        ResponseContext<ReturnInt> InsertBusinessTimeData(List<EditBusinessTimeContext> context);

        /// <summary>
        /// 删除营业时间信息(软删除)
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<ReturnInt> DeleteBusinessTimeData(DeleteBusinessTimeContext context);

        /// <summary>
        /// 修改营业时间信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<ReturnInt> EditBusinessTimeData(EditBusinessByIdContext context);

        BeginEndBusinessTime GetShopBeginEndTime(BeginEndBusinessTimeContext context);

        /// <summary>
        /// 根据有门店、日期获取绑定的星期
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Tuple<int, string, bool> GetBtSpecialWeekTime(BeginEndBusinessTimeContext context);
    }
}
