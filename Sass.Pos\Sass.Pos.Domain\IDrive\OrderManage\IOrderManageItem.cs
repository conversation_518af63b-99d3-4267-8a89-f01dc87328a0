﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.OrderManage
{
    /// <summary>
    /// 订单商品管理
    /// </summary>
    public interface IOrderManageItem
    {
        /// <summary>
        /// 根据支付方式获取商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        List<GetOrderBookSkuExModel> GetOrderItemData(List<OrderItemDataContext> context);

        /// <summary>
        /// 获取订单商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<List<GetShopBookCacheOrderDetailModel>> GetItemDatas(GetOrderDetailDataExContext context);
    }
}
