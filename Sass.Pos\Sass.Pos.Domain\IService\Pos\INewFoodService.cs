﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.General;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    public interface INewFoodService
    {
        [OperationContract]
        ResponseContext<RespPaginationModel<GetFdTypeLinkDataModel>> GetFdTypeLinkDataByStore(GetFdTypeLinkDataContext context);

        [OperationContract]
        ResponseContext<EditFdTypeLinkDataModel> EditFdTypeLinkDataByStore(EditFdTypeLinkDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetFoodLabelDataModel>> GetFoodLabelDataByStore(GetFoodLabelDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetNewFdTypeDataModel>> GetNewFdTypeDataByStore(GetNewFdTypeDataContext context);

        [OperationContract]
        ResponseContext<EditNewFdTypeDataModel> EditNewFdTypeDataByStore(EditNewFdTypeDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetNewFdGiveDataModel>> GetNewFdGiveDataByStore(GetNewFdGiveDataContext context);

        [OperationContract]
        ResponseContext<EditNewFdGiveDataModel> EditNewFdGiveDataByStore(EditNewFdGiveDataContext context);

        [OperationContract]
        ResponseContext<List<KeyValueModel>> GetSelectFdGiveDataByStore(GetSelectFdGiveDataContext context);

        [OperationContract]
        ResponseContext<List<KeyValueModel>> GetSelectFdGiveData(GetSelectFdGiveDataContext context);


        [OperationContract]
        ResponseContext<RespPaginationModel<GetFdTypeLinkDataModel>> GetFdTypeLinkData(GetFdTypeLinkDataContext context);

        [OperationContract]
        ResponseContext<EditFdTypeLinkDataModel> EditFdTypeLinkData(EditFdTypeLinkDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetFoodLabelDataModel>> GetFoodLabelData(GetFoodLabelDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetNewFdTypeDataModel>> GetNewFdTypeData(GetNewFdTypeDataContext context);

        [OperationContract]
        ResponseContext<EditNewFdTypeDataModel> EditNewFdTypeData(EditNewFdTypeDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetNewFdGiveDataModel>> GetNewFdGiveData(GetNewFdGiveDataContext context);

        [OperationContract]
        ResponseContext<EditNewFdGiveDataModel> EditNewFdGiveData(EditNewFdGiveDataContext context);
    }
}
