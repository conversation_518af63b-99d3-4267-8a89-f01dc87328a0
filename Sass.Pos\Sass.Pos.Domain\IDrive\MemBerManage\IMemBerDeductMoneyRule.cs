﻿using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.MemBerManage
{
    public interface IMemBerDeductMoneyRule
    {
        /// <summary>
        /// 账户规则积分、返还、积点
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        MemBerAcc MemBerRule(MemBerRuleContext context);
    }
}
