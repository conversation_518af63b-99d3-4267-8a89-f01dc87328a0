﻿using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Remoting.Contexts;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
    public partial class FdCashApp : AppBase<FdCash>
    {
        public List<GetBillDetailsModel> GetBillDetail(GetBillDataContext context)
        {
            return Repository.FdCash.GetBillDetail(context);
        }

        public bool Bill_Fd_DelOrder(Bill_Fd_DelOrderContext context)
        {
            return Repository.FdCash.Bill_Fd_DelOrder(context);
        }

        public bool Bill_RefToZD(Bill_RefToZDContext context)
        {
            return Repository.FdCash.Bill_RefToZD(context);
        }

        public bool Bill_RefReCall(Bill_RefReCallContext context)
        {
            return Repository.FdCash.Bill_RefReCall(context);
        }

        public bool Bill_AddQrInfo(Bill_AddQrInfoContext context)
        {
            return Repository.FdCash.Bill_AddQrInfo(context);
        }

        public GetFdCashInfoModel GetFdCashInfo(string refNo)
        {
            return Repository.FdCash.GetFdCashInfo(refNo);
        }
    }
}
