﻿
using ComponentApplicationServiceInterface.Web;
using Saas.Pos.Model.MIMS;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.MIMS
{
 public partial class ActivityApp : AppBase<Activity> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Activity> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.Activity;
        }
   
        
 
 }
  

 public partial class ADemoApp : AppBase<ADemo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ADemo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.ADemo;
        }
   
        
 
 }
  

 public partial class CheckoutInfoApp : AppBase<CheckoutInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<CheckoutInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.CheckoutInfo;
        }
   
        
 
 }
  

 public partial class dtpropertiesApp : AppBase<dtproperties> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<dtproperties> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.dtproperties;
        }
   
        
 
 }
  

 public partial class ExGiftInfoApp : AppBase<ExGiftInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ExGiftInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.ExGiftInfo;
        }
   
        
 
 }
  

 public partial class FdUserWeChatApp : AppBase<FdUserWeChat> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdUserWeChat> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.FdUserWeChat;
        }
   
        
 
 }
  

 public partial class FdUserWeChatJurisdictionApp : AppBase<FdUserWeChatJurisdiction> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdUserWeChatJurisdiction> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.FdUserWeChatJurisdiction;
        }
   
        
 
 }
  

 public partial class GiftInfoApp : AppBase<GiftInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GiftInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.GiftInfo;
        }
   
        
 
 }
  

 public partial class IC_FdUserApp : AppBase<IC_FdUser> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<IC_FdUser> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.IC_FdUser;
        }
   
        
 
 }
  

 public partial class IntegralInfoApp : AppBase<IntegralInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<IntegralInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.IntegralInfo;
        }
   
        
 
 }
  

 public partial class ManagerCardInfoApp : AppBase<ManagerCardInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ManagerCardInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.ManagerCardInfo;
        }
   
        
 
 }
  

 public partial class ManagerInfoApp : AppBase<ManagerInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ManagerInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.ManagerInfo;
        }
   
        
 
 }
  

 public partial class MemberCardInfoApp : AppBase<MemberCardInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberCardInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemberCardInfo;
        }
   
        
 
 }
  

 public partial class MemberCardInfoJoinApp : AppBase<MemberCardInfoJoin> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberCardInfoJoin> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemberCardInfoJoin;
        }
   
        
 
 }
  

 public partial class MemberCardInfoRemoveApp : AppBase<MemberCardInfoRemove> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberCardInfoRemove> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemberCardInfoRemove;
        }
   
        
 
 }
  

 public partial class MemberCardOrderRecordApp : AppBase<MemberCardOrderRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberCardOrderRecord> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemberCardOrderRecord;
        }
   
        
 
 }
  

 public partial class MemberCardPresentInfoApp : AppBase<MemberCardPresentInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberCardPresentInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemberCardPresentInfo;
        }
   
        
 
 }
  

 public partial class MemberCardTypeInfoApp : AppBase<MemberCardTypeInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberCardTypeInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemberCardTypeInfo;
        }
   
        
 
 }
  

 public partial class MemberEmpowerPayApp : AppBase<MemberEmpowerPay> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberEmpowerPay> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemberEmpowerPay;
        }
   
        
 
 }
  

 public partial class MemberGiftRecordApp : AppBase<MemberGiftRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberGiftRecord> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemberGiftRecord;
        }
   
        
 
 }
  

 public partial class MemberInfoApp : AppBase<MemberInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemberInfo;
        }
   
        
 
 }
  

 public partial class MemberInfo_copy1App : AppBase<MemberInfo_copy1> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberInfo_copy1> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemberInfo_copy1;
        }
   
        
 
 }
  

 public partial class MemberInfoExtendApp : AppBase<MemberInfoExtend> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberInfoExtend> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemberInfoExtend;
        }
   
        
 
 }
  

 public partial class MemberInfoRemoveApp : AppBase<MemberInfoRemove> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberInfoRemove> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemberInfoRemove;
        }
   
        
 
 }
  

 public partial class MemberOpenUpRuleApp : AppBase<MemberOpenUpRule> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberOpenUpRule> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemberOpenUpRule;
        }
   
        
 
 }
  

 public partial class MemBerRiskControlApp : AppBase<MemBerRiskControl> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemBerRiskControl> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemBerRiskControl;
        }
   
        
 
 }
  

 public partial class MemBerRiskRuleApp : AppBase<MemBerRiskRule> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemBerRiskRule> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemBerRiskRule;
        }
   
        
 
 }
  

 public partial class MemBerRuleApp : AppBase<MemBerRule> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemBerRule> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemBerRule;
        }
   
        
 
 }
  

 public partial class MemBerSccountRecordApp : AppBase<MemBerSccountRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemBerSccountRecord> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemBerSccountRecord;
        }
   
        
 
 }
  

 public partial class MemberSystemEditionApp : AppBase<MemberSystemEdition> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberSystemEdition> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MemberSystemEdition;
        }
   
        
 
 }
  

 public partial class MimsRoomCommissionApp : AppBase<MimsRoomCommission> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MimsRoomCommission> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.MimsRoomCommission;
        }
   
        
 
 }
  

 public partial class PointInfoApp : AppBase<PointInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<PointInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.PointInfo;
        }
   
        
 
 }
  

 public partial class PointInfoTempApp : AppBase<PointInfoTemp> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<PointInfoTemp> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.PointInfoTemp;
        }
   
        
 
 }
  

 public partial class RechargeInfoApp : AppBase<RechargeInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RechargeInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.RechargeInfo;
        }
   
        
 
 }
  

 public partial class RechargePurviewInfoApp : AppBase<RechargePurviewInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RechargePurviewInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.RechargePurviewInfo;
        }
   
        
 
 }
  

 public partial class RegMemberCardInfoApp : AppBase<RegMemberCardInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RegMemberCardInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.RegMemberCardInfo;
        }
   
        
 
 }
  

 public partial class ReturnAnnualClosingApp : AppBase<ReturnAnnualClosing> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ReturnAnnualClosing> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.ReturnAnnualClosing;
        }
   
        
 
 }
  

 public partial class ReturnInfoApp : AppBase<ReturnInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ReturnInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.ReturnInfo;
        }
   
        
 
 }
  

 public partial class ScoreDelRecordApp : AppBase<ScoreDelRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ScoreDelRecord> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.ScoreDelRecord;
        }
   
        
 
 }
  

 public partial class ShopInfoApp : AppBase<ShopInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ShopInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.ShopInfo;
        }
   
        
 
 }
  

 public partial class sysdiagramsApp : AppBase<sysdiagrams> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<sysdiagrams> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.sysdiagrams;
        }
   
        
 
 }
  

 public partial class Th_RoomCommissionAllotApp : AppBase<Th_RoomCommissionAllot> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Th_RoomCommissionAllot> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.Th_RoomCommissionAllot;
        }
   
        
 
 }
  

 public partial class UserCardInfoApp : AppBase<UserCardInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<UserCardInfo> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.UserCardInfo;
        }
   
        
 
 }
  

 public partial class vip_report_singlecou_projectApp : AppBase<vip_report_singlecou_project> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<vip_report_singlecou_project> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.vip_report_singlecou_project;
        }
   
        
 
 }
  

 public partial class vip_report_singlecou_project_itemApp : AppBase<vip_report_singlecou_project_item> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<vip_report_singlecou_project_item> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.vip_report_singlecou_project_item;
        }
   
        
 
 }
  

 public partial class vip_report_singlecou_project_item_record_dayApp : AppBase<vip_report_singlecou_project_item_record_day> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<vip_report_singlecou_project_item_record_day> SetRepository(RepositoryFactory.T4.MIMS.RepositorySession Session)
        {
            return Session.vip_report_singlecou_project_item_record_day;
        }
   
        
 
 }
  

}
