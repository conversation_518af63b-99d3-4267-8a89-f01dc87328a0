﻿using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
    public partial class UserInfo_BindingApp : AppBase<UserInfo_Binding>
    {
        public List<GetUserBindingModel> GetUserBindingList(GetUserBindingListContext context)
        {
            return Repository.UserInfo_Binding.GetUserBindingList(context);
        }

        public List<GetUserScenceDataModel> GetUserScenceList(GetUserScenceDataContext context)
        {
            return Repository.UserInfo_Binding.GetUserScenceList(context);
        }
    }
}
