﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace external.open.library.TakeaWayPlatform.MeiTuan.Response
{
    public class MeituanQueryCouponResponse
    {
        /// <summary>
        /// 可验证的张数
        /// </summary>
        public int count { get; set; }
        /// <summary>
        /// 验证券码
        /// </summary>
        public string receipt_code { get; set; }
        /// <summary>
        /// 套餐id（若验证的券所对应的商品为团购时，该字段必返回）
        /// </summary>
        public long deal_id { get; set; }
        /// <summary>
        /// 团购id,团购id与套餐id是一对多的关系（若验证的券所对应的商品为团购时，该字段必返回）
        /// </summary>
        public long dealgroup_id { get; set; }
        /// <summary>
        /// 商品id（若验证的券所对应的商品非团购时，该字段必返回，product_item_id含义参考商品管理API）
        /// </summary>
        public Nullable<long> product_item_id { get; set; }
        /// <summary>
        /// 商品类型 1、泛商品如丽人派样活动商品等（若验证的券所对应的商品非团购时，该字段必返回）	
        /// </summary>
        public Nullable<int> product_type { get; set; }
        /// <summary>
        /// 商品名称
        /// </summary>
        public string deal_title { get; set; }
        /// <summary>
        /// 商品售卖价格
        /// </summary>
        public Nullable<double> deal_price { get; set; }
        /// <summary>
        /// 商品市场价
        /// </summary>
        public Nullable<double> deal_marketprice { get; set; }
        /// <summary>
        /// 业务类型 0:普通团购  203:拼团 205:次卡 217:通兑标品
        /// </summary>
        public Nullable<int> biz_type { get; set; }
        /// <summary>
        /// 券过期时间
        /// </summary>
        public long receiptEndDate { get; set; }
        /// <summary>
        /// 支付明细
        /// </summary>
        public List<PaymentDetailDTO> payment_detail { get; set; }
        /// <summary>
        /// 用户手机号
        /// </summary>
        public string mobile { get; set; }
        /// <summary>
        /// 多团单维度券信息，如果为null则为单团单,key为product_item_id
        /// </summary>
        public object receipt_info_map { get; set; }
        /// <summary>
        /// 是否团购次卡
        /// </summary>
        public bool tgTimesCardFlag { get; set; }
        /// <summary>
        /// 团购次卡可用总次数
        /// </summary>
        public string purchaseToConsumeRatio { get; set; }
    }
}
