﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace external.open.library.TakeaWayPlatform.Model
{
    public class ConsumeModel
    {
        /// <summary>
        /// 团购或者预订券码
        /// </summary>
        public string ReceiptCode { get; set; }
        /// <summary>
        /// 美团或者抖音平台的店铺编号
        /// </summary>
        public string OpenShopId { get; set; }
        /// <summary>
        /// 当前平台操作人账号
        /// </summary>
        public string ShopAccount { get; set; }
        /// <summary>
        /// 当前平台操作人
        /// </summary>
        public string ShopName { get; set; }
        /// <summary>
        /// 核销数量
        /// </summary>
        public int Count { get; set; }
    }
}
