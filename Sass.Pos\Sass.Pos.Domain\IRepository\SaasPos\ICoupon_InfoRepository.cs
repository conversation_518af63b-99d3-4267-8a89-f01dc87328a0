﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.SaasPos
{
    public partial interface ICoupon_InfoRepository : IRepositoryBase<Coupon_Info>
    {
        List<GetCouponInfoDataModel> GetUserCouponData(GetCouponExDataContext context);

        List<GetCouponExDataModel> CouponVerifyPermit(CouponVerifyContext context);

        GetCouponExDataModel CouponVerifyPermitBest(CouponVerifyContext context);

        List<GetCouponExDataModel> CouponVerifyPermitEx(CouponVerifyExContext context);

        List<User_Coupon> CouponDataCancel(CouponDataCancelContext context);

        List<GetUseRecordDataModel> GetMerchantOrderList(GetUseRecordDataContext context);

        bool GetUseCountByBool(UseCountByBoolContext context);

        List<CouponData> GetCouponData(GetCouponDataExContext context);

        List<User_Coupon> GetOpenClaimNum(string OpenId);

        List<Coupon_Rule> GetCouRuleData(List<string> middleIds);

        GetCoupon_CampaignModel GetCouponCampaign(GetCouponCampaignContext context);

        GetCoupon_CampaignCountModel GetCoupon_CampaignCount(string CamId, string OpenId);

        List<GetCoupon_InfoCountModel> GetCoupon_InfoCount(List<string> MiddleIds, string OpenId);

        List<GetDistributeRecordDataModel> GetDistributeRecordData(GetDistributeRecordDataContext context);

        List<GetMiddleDataModel> GetMiddleData(GetMiddleDataContext context);

        List<GetMiddleRuleDataModel> GetMiddleRuleData(List<string> MiddleIds);
    }
}
