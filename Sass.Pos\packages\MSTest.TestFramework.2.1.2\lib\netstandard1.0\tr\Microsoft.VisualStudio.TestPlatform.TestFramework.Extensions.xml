<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute">
            <summary>
            Test başına dağıtım için dağıtım ö<PERSON> (dosya veya dizin) belirtmek üzere kullanılır.
            Test sınıfında veya test metodunda belirtilebilir.
            Birden fazla öğe belirtmek için özniteliğin birden fazla örneğini içerebilir.
            Öğe yolu mutlak veya göreli olabilir; göreli ise RunConfig.RelativePathRoot ile görelidir.
            </summary>
            <example>
            [DeploymentItem("file1.xml")]
            [DeploymentItem("file2.xml", "DataFiles")]
            [DeploymentItem("bin\Debug")]
            </example>
            <remarks>
            DeploymentItemAttribute is currently not supported in .Net Core. This is just a placehodler for support in the future.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="path">Dağıtılacak dosya veya dizin. Yol, derleme çıktı dizinine göredir. Öğe, dağıtılan test bütünleştirilmiş kodlarıyla aynı dizine kopyalanır.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String,System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/> sınıfının yeni bir örneğini başlatır
            </summary>
            <param name="path">Dağıtılacak dosya veya dizinin göreli ya da mutlak yolu. Yol, derleme çıktı dizinine göredir. Öğe, dağıtılan test bütünleştirilmiş kodlarıyla aynı dizine kopyalanır.</param>
            <param name="outputDirectory">Öğelerin kopyalanacağı dizinin yolu. Dağıtım dizinine göre mutlak veya göreli olabilir. Tüm dosyalar ve dizinler şuna göre tanımlanır: <paramref name="path"/> bu dizine kopyalanacak.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.Path">
            <summary>
            Kopyalanacak kaynak dosya veya klasörün yolunu alır.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.OutputDirectory">
            <summary>
            Öğenin kopyalandığı dizinin yolunu alır.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext">
            <summary>
            TestContext sınıfı. Bu sınıf tamamen soyut olmalı ve herhangi bir üye
            içermemelidir. Üyeler bağdaştırıcı tarafından uygulanır. Çerçevedeki kullanıcılar
            buna yalnızca iyi tanımlanmış bir arabirim üzerinden erişmelidir.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.Properties">
            <summary>
            Bir testin test özelliklerini alır.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.FullyQualifiedTestClassName">
            <summary>
            O anda yürütülen test metodunu içeren sınıfın tam adını alır
            </summary>
            <remarks>
            This property can be useful in attributes derived from ExpectedExceptionBaseAttribute.
            Those attributes have access to the test context, and provide messages that are included
            in the test results. Users can benefit from messages that include the fully-qualified
            class name in addition to the name of the test method currently being executed.
            </remarks>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestName">
            <summary>
            Yürütülmekte olan test metodunun Adını alır
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.CurrentTestOutcome">
            <summary>
            Geçerli test sonucunu alır.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String)">
            <summary>
            Used to write trace messages while the test is running
            </summary>
            <param name="message">formatted message string</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String,System.Object[])">
            <summary>
            Used to write trace messages while the test is running
            </summary>
            <param name="format">format string</param>
            <param name="args">the arguments</param>
        </member>
    </members>
</doc>
