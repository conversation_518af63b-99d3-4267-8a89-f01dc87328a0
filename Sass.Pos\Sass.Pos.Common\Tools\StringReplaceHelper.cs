﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Saas.Pos.Common.Tools
{
    public static class StringReplaceHelper
    {
        /// <summary>
        /// 动态填充字符串内容
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="template">模板内容，需填充地方需要用{*}*表示字段名字</param>
        /// <param name="obj">填充对象</param>
        /// <returns></returns>
        public static string DynamicReplace<T>(this string template, T obj)
        {
            //使用正则表达式匹配出对应的{*}
            var type = typeof(T);
            Regex regex = new Regex("{[^{}]+}");
            var matchs = regex.Matches(template);
            //循环所有的{*}
            foreach (var match in matchs)
            {
                try
                {
                    var property = match.ToString();
                    //去除{*}中的大括号
                    var item = property.Substring(1, property.Length - 1);
                    item = item.Substring(0, item.Length - 1);
                    //如果没有嵌套类
                    if (!item.Contains("."))
                    {
                        //直接取传入的obj中的值
                        var value = type.GetProperty(item);
                        if (value != null)
                            template = template.Replace(property, value.GetValue(obj, null).ToString());
                    }
                    else
                    {
                        //先找到第一层，然后慢慢往下找
                        var classInfo = item.Split('.');
                        var value = type.GetProperty(classInfo[0]);
                        if (value != null)
                        {
                            var childType = value.PropertyType;//获取嵌套类的type
                            var childValue = value.GetValue(obj, null);//获取嵌套类的值
                                                                       //如果存在多层嵌套，就循环去处理
                            for (int i = 1; i < classInfo.Length; i++)
                            {
                                try
                                {
                                    //最后一行默认是点到对应的属性
                                    if (classInfo.Length == i + 1)
                                    {
                                        if (childType.GetProperty(classInfo[i]) != null)
                                            template = template.Replace(property, childType.GetProperty(classInfo[i]).GetValue(childValue, null).ToString());
                                    }
                                    else
                                    {
                                        childValue = childType.GetProperty(classInfo[i]).GetValue(childValue, null);//重新赋值新的嵌套类的值
                                        childType = childType.GetProperty(classInfo[i]).PropertyType;//重新赋值新的嵌套类的type
                                    }
                                }
                                catch (Exception)
                                {
                                    throw;
                                }
                            }
                        }
                    }
                }
                catch (Exception)
                {
                }
            }

            return template;
        }
    }
}
