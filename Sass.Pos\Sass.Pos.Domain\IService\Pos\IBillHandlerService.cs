﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Pos
{

    [ServiceContract]

    /// <summary>
    /// 天王功能接口
    /// </summary>
    public interface IBillHandlerService
    {
        /// <summary>
        /// 获取活跃账单信息
        /// </summary>
        [OperationContract]
        ResponseContext<GetRmBillData> FdGetRmBillData(GetHistoryBillDataContext context);

        [OperationContract]
        ResponseContext<GetRmBillDataV1> FdGetRmBillDataV1(GetBillDataContext context);

        [OperationContract]
        ResponseContext<GetRmBillData> FdGetRmBillDataByStore(GetBillDataContext context);

        [OperationContract]
        ResponseContext<GetRmBillDataV1> FdGetRmBillDataByStoreV1(GetBillDataContext context);

        [OperationContract]
        ResponseContext<ReturnBool> EditRmBillDataByStore(EditRmBillDataContext context);

        [OperationContract]
        ResponseContext<ReturnBool> EditRmBillData(EditRmBillDataContext context);

        /// <summary>
        /// 获取历史账单信息
        /// </summary>
        [OperationContract]
        ResponseContext<GetRmBillData> FdGetLSRmBillData(GetHistoryBillDataContext context);

        [OperationContract]
        ResponseContext<GetRmBillData> FdGetLSRmBillDataByStore(GetHistoryBillDataContext context);

        [OperationContract]
        ResponseContext<string> CreatePdfBill(GetHistoryBillDataContext context);

        [OperationContract]
        ResponseContext<RmHeadData> GetActiveBillHead(GetBillDataContext context);

        [OperationContract]
        ResponseContext<RmHeadData> GetActiveBillHeadByStore(GetBillDataContext context);

        [OperationContract]
        ResponseContext<List<RmDetailData>> GetActiveBillDetails(GetBillDataContext context);

        [OperationContract]
        ResponseContext<List<RmDetailData>> GetActiveBillDetailsByStore(GetBillDataContext context);

        [OperationContract]
        ResponseContext<GetBillPaymentSummary> GetBillPaymentData(GetHistoryBillDataContext context);

        [OperationContract]
        ResponseContext<GetBillPaymentSummary> GetBillPaymentDataByStore(GetHistoryBillDataContext context);

        [OperationContract]
        ResponseContext<List<GetBillPaymentDetail>> GetBillPaymentDetail(GetHistoryBillDataContext context);

        [OperationContract]
        ResponseContext<List<GetBillPaymentDetail>> GetBillPaymentDetailByStore(GetHistoryBillDataContext context);

        [OperationContract]
        ResponseContext<RmHeadData> GetHistoryBill_Head(GetHistoryBillDataContext context);

        [OperationContract]
        ResponseContext<RmHeadData> GetHistoryBill_HeadByStore(GetHistoryBillDataContext context);

        [OperationContract]
        ResponseContext<List<RmDetailData>> GetHistoryBill_Details(GetHistoryBillDataContext context);

        [OperationContract]
        ResponseContext<List<RmDetailData>> GetHistoryBill_DetailsByStore(GetHistoryBillDataContext context);

        [OperationContract]
        ResponseContext<PaymentInfo> GetInvPayData(GetPaymentBillInfoContext context);

        [OperationContract]
        ResponseContext<PaymentInfo> GetInvPayDataByStore(GetPaymentBillInfoContext context);

        [OperationContract]
        ResponseContext<string> GetBillPayUrl(GetBillDataContext context);

        [OperationContract]
        ResponseContext<List<FdDataDetail>> GetWaitOrderPackageItem(GetWaitOrderPackageItemContext context);

        [OperationContract]
        ResponseContext<List<FdDataDetail>> GetWaitOrderPackageItemByStore(GetWaitOrderPackageItemContext context);


        [OperationContract]
        ResponseContext<GetPrepaymentRecordModel> GetPrepaymentRecordByStory(GetPrepaymentRecordContext context);

        [OperationContract]
        ResponseContext<GetPrepaymentRecordModel> GetPrepaymentRecord(GetPrepaymentRecordContext context);

        [OperationContract]
        ResponseContext<ReturnBool> SavePreRecordByStore(SavePreRecordContext context);

        [OperationContract]
        ResponseContext<ReturnBool> SavePreRecord(SavePreRecordContext context);

        [OperationContract]
        ResponseContext<ReturnBool> PrintBill(PrintBillContext context);

        [OperationContract]
        ResponseContext<ReturnBool> PrintBillByStore(PrintBillContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetFdInvListModel>> GetFdInvList(GetFdInvListContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetFdInvListModel>> GetFdInvListByStore(GetFdInvListContext context);

        [OperationContract]
        ResponseContext<ReturnBool> AutoIntegration(AutoIntegrationContext context);
    }
}
