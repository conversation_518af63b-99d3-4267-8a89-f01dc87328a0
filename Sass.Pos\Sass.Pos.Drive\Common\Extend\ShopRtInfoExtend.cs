﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Drive.Rms;
using Saas.Pos.Drive.SaasPos.ShopGood;
using Saas.Pos.Model.Rms.Context;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.Common.Extend
{
    /// <summary>
    /// SaasPos房型拓展方法
    /// </summary>
    public class ShopRtInfoExtend
    {
        /// <summary>
        /// 通过人数找到SaasPos最佳房型，然后通过绑定的Rms房型找到对应的Rms的房型
        /// </summary>
        /// <param name="bookRtInfos"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public static string GetBestRtNo(GetBestRmsRtNoContext context)
        {
            var rtNo = string.Empty;
            RmsBookingDrive bookingDrive = new RmsBookingDrive();
            var bookRtQuery = AppSingle.App.Storage.DbDat.ShopBookRtList.Where(w => w.ShopId == context.ShopId);
            var bookRtData = bookRtQuery.Where(w => w.NumberMax >= context.Numbers).ToList();
            if (!string.IsNullOrEmpty(context.ShopBookRtNo))
                bookRtData = bookRtData.Where(w => w.Id == context.ShopBookRtNo).ToList();
            else
            {
                var goodsDrive = new ShopGoodsDrive();
                var rtNos = goodsDrive.BookGoodPermit.GetPeimitRtNos(context.PermitId);
                if (rtNos.Count > 0)
                    bookRtData = bookRtData.Where(w => rtNos.Contains(w.Id)).ToList();
            }

            //如果人数超出，那么就按照最大人数倒序排序
            if (bookRtData.Count <= 0)
                bookRtData = bookRtQuery.OrderByDescending(w => w.NumberMax).ToList();

            var rtResp = bookingDrive.GetRtList(new GetRtListContext()
            {
                ShopId = context.ShopId,
                Date = context.BookDate,
                Platform = 1,
                TimeNo = context.TimeNo
            });

            if (rtResp.state != ResponseType.success)
                throw new ExMessage("获取房型失败！" + rtResp.message);

            foreach (var item in bookRtData)
            {
                if (!string.IsNullOrEmpty(rtNo))
                    break;
                var rtNos = item.RtNos.Split(',').ToList();
                var rmsRtData = rtResp.data.Where(w => rtNos.Contains(w.Id) && w.RbMax >= context.Numbers).OrderBy(w => w.RbMax).ToList();
                if (rmsRtData.Count <= 0)
                    rmsRtData = rtResp.data.OrderByDescending(w => w.RbMax).ToList();

                foreach (var rmsRtInfo in rmsRtData)
                {
                    if (rmsRtInfo.ReQty - 1 > 0 && !rmsRtInfo.Stop)
                    {
                        rtNo = rmsRtInfo.Id;
                        break;
                    }
                }
            }

            return rtNo;
        }
    }
}
