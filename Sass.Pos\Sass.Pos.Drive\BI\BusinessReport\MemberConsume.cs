﻿using ComponentApplicationServiceInterface.Context.Response;
using Newtonsoft.Json;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Saas.Pos.Common.Log;
using Saas.Pos.Drive.SaasPos;
using Saas.Pos.Model.Enum;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.BI.BusinessReport
{
    /// <summary>
    /// 会员消费数据分析
    /// </summary>
    public class MemberConsume : DriveBase
    {
        /// <summary>
        /// 会员月度消费汇总
        /// </summary>
        public ResponseContext<List<MonthlyConsumptionSumModel>> MonthlyConsumptionSum(MonthlyConsumptionSumContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (context.ShopId <= 0)
                        throw new ExMessage("请选择门店!");
                    if (context.StartTime <= DateTime.MinValue || context.EndTime <= DateTime.MinValue)
                        throw new ExMessage("请选择日期!");

                    var endDate = context.EndTime.AddDays(1).Date;
                    //充值数据
                    var rechargeData = app.MIMS.RechargeInfo
                    .IQueryable(i => i.RechargeType == -1 && i.RechargeShopId == context.ShopId &&
                    i.RechargeDate >= context.StartTime.Date && i.RechargeDate <= endDate).ToList();
                    //返还数据
                    var returnData = app.MIMS.ReturnInfo
                    .IQueryable(i => i.ReturnType == -1 && i.ReturnShopId == context.ShopId &&
                    i.ReturnDate >= context.StartTime.Date && i.ReturnDate <= endDate).ToList();

                    //获取选择的日期区间相差的天数
                    var dateEx = (context.EndTime.Date - context.StartTime.Date).TotalDays + 1;

                    var data = new List<MonthlyConsumptionSumModel>();
                    //循环合并
                    for (int i = 0; i < dateEx; i++)
                    {
                        var startDate = context.StartTime.AddDays(i);
                        data.Add(new MonthlyConsumptionSumModel()
                        {
                            DateTime = startDate.ToString("MM月dd日"),
                            WeekTime = Enum.GetName(typeof(ShopWeekTimeEnum), (int)startDate.DayOfWeek),//获取星期
                            RechargeSum = rechargeData.Where(j => j.RechargeDate.Date == startDate.Date).Sum(j => j.RechargeValue),
                            ReturnSum = returnData.Where(j => j.ReturnDate.Date == startDate.Date).Sum(j => j.ReturnValue),
                        });
                    }

                    return data;
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "会员消费数据分析-会员月度消费汇总\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage("查询会员月度消费汇总失败!" + ex.Message);
                }
            });
        }

        /// <summary>
        /// 导出会员月度消费汇总
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<ExcelMonthlyConsumptionSumModel> ExcelMonthlyConsumptionSum(ExcelMonthlyConsumptionSumContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    //汇总数据
                    var data = MonthlyConsumptionSum(context);
                    if (data.state != ResponseType.success)
                        throw new ExMessage("导出会员月度消费汇总失败!");
                    var shopData = app.SaasPos.Shop.FindEntity(i => i.ShopId == context.ShopId);
                    if (shopData == null || shopData.ShopId <= 0)
                        throw new ExMessage("找不到门店!");

                    string fileName = $"会员月度消费汇总" + DateTime.Now.ToString("yyyyMMdd") + ".xlsx";

                    // 创建一个新的工作簿
                    IWorkbook workbook = new XSSFWorkbook();
                    ISheet sheet = workbook.CreateSheet(shopData.ShopName);

                    int index = 0;
                    //表头
                    IRow tatleRow = sheet.CreateRow(index); 
                    string[] tabTatle = { "日期", "星期", "使用充值", "使用返还" };
                    for (int i = 0; i < tabTatle.Length; i++)
                    {
                        tatleRow.CreateCell(i).SetCellValue(tabTatle[i]);
                    }

                    foreach (var item in data.data)
                    {
                        index++;
                        IRow dataRow = sheet.CreateRow(index);
                        dataRow.CreateCell(0).SetCellValue(item.DateTime);
                        dataRow.CreateCell(1).SetCellValue(item.WeekTime);
                        dataRow.CreateCell(2).SetCellValue(item.RechargeSum);
                        dataRow.CreateCell(3).SetCellValue(item.ReturnSum);
                    }

                    ShopMakeExport h = new ShopMakeExport();
                    var isExcel = h.ExportExcel(workbook, new ExportExcelContext() { fileName = fileName, BookUserId = context.BookUserId, BookUserName = context.BookUserName, DealType = 1 });
                    if (!isExcel)
                        throw new ExMessage("导出失败!");

                    return new ExcelMonthlyConsumptionSumModel();
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "会员消费数据分析-导出会员月度消费汇总\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage("导出会员月度消费汇总失败!" + ex.Message);
                }
            });
        }
    }
}
