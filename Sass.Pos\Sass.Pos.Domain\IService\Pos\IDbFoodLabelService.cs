using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    public interface IDbFoodLabelService
    {
        [OperationContract]
        ResponseContext<RespPaginationModel<GetFoodLabelListModel>> GetFoodLabelList(FoodLabelContext context);

        [OperationContract]
        ResponseContext<int> BatchImport(List<BatchImportContext> context);

        [OperationContract]
        ResponseContext<GetFoodLabelInfoModel> GetLabelInfo(GetFoodLabelInfoContext context);

        [OperationContract]
        ResponseContext<int> Delete(DeleteFoodLabelContext context);

        [OperationContract]
        ResponseContext<List<KeyValuePair<string, string>>> GetFtDropDown(GetFtDropDownContext context);

        [OperationContract]
        ResponseContext<int> CreateLabel(CreateLabelContext context);

        [OperationContract]
        ResponseContext<List<KeyValuePair<int, string>>> GetTypes(GetFtDropDownContext context);

        [OperationContract]
        ResponseContext<List<KeyValuePair<int, string>>> GetCates(GetFtDropDownContext context);

        [OperationContract]
        ResponseContext<List<GetCheckOutDataModel>> GetCheckOutData(GetCheckOutDataContext context);

        [OperationContract]
        ResponseContext<bool> CheckOutOrder(CheckOutOrderContext context);

        [OperationContract]
        ResponseContext<List<GetCheckOutDataModel>> GetCheOutList(GetCheckOutDataContext context);

        [OperationContract]
        ResponseContext<bool> CheckOut(CheckOutOrderContext context);
    }
}
