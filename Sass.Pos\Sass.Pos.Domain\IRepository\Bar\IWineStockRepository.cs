﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using Saas.Pos.Model.Enum;
using Saas.Pos.Model.SaasPos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Saas.Pos.Model.Bar.Context.WineStockContext;
using static Saas.Pos.Model.Bar.Model.WineStockModel;

namespace Saas.Pos.Domain.IRepository.Bar
{
    public partial interface IWineStockRepository
    {
        List<custData> GetWineStockData();

        List<custData> GetWineStockDataEx();

        int EditWineStockData(List<EditSendReardContext> context);

        List<UserDrinksData> GetDrinkList(string MsgPassWord, int DrShopId);

        List<GetFoodNosModel> GetFoodNos(GetFoodNosContext context);

        int UpdateMsgInfoState(string MsgPassWord, string BarName, string DrMemory, int ShopId, string RmNo);

        int InsertWineUser(SaveWineUserContext context, WineUserStatusEnum MsgStatus);

        List<GetFdTypeDataModel> GetFdTypeDatas(GetFdTypeDataContext context);

        List<GetFdDataModel> GetFdDatas(GetFdDataContext context);

        List<WineStorkReportModel> GetWineStorkReportData(WineStorkReportContext context);

        int EditDataBySql(string sql);

        GetSelectDataCount GetSelectDataCount(string sql);

        List<GetDrCheckDataModel> GetDrCheckData(GetDrCheckDataContext context);

        List<GetDrCheckDetailDataModel> GetDrCheckDetailData(GetDrCheckDetailDataContext context);

        List<GetfdList> GetfdLists(int ShopId, string FdCName = null);

        List<GetfdListEx> GetfdListsEx(GetfdListContext context);

        List<GetfdTypeList> GetfdTypeLists(int ShopId, string FtCName = null);

        List<GetfdTypeList> GetfdTypeListsEx(GetftListContext context);

        List<GetUnitList> GetUnitLists(string Unit = null);

        List<GetUnitList> GetUnitListsEx(GetUnitListContext context);
    }
}
