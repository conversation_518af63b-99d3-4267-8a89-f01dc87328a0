﻿using Saas.Pos.Domain.IService.GrouponBase;
using Saas.Pos.Domain.IService.SaasPos;
using System.ServiceModel;

namespace Saas.Pos.Domain.IService
{
    [ServiceContract]

    public interface ISaasPos : ISaasPosReport, IStoreManageService, IInternalCollaborationService, IOpenPlatformService, IShopGoodService,
        IShopBookGoodService, IOrderManageService, ICouponManageService, IShopBookCacheInfoService, ISystemUniversalService, IGrouponInfoService, IWeChatService, IWineStockService,
        IMemBerManageService, IUserActivateManageService, IBookingNewAttDayService, IPosFoodManageService,IMemberService, ISongScanBillService
    {


    }
}
