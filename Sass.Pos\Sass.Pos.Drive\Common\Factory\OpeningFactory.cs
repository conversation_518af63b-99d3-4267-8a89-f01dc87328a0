﻿using Saas.Pos.Common.Tools;
using Saas.Pos.Drive.Rms.Opening;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Factory
{
    public class OpeningFactory
    {
        private static Dictionary<string, OpeningBase> OpeningDic = new Dictionary<string, OpeningBase>();

        public static OpeningBase CreateInstance(string platform)
        {
            if (string.IsNullOrEmpty(platform))
                throw new ExMessage("未选择初始模块！");

            //请求量比较大，缓存两个实现，避免每次都通过反射去实例化
            if (!OpeningDic.ContainsKey(platform))
            {
                var openingBase = AssemblyHelper.CreateInstance<OpeningBase>("Saas.Pos.Drive", "Saas.Pos.Drive.Rms.Opening", platform + "Opening");
                OpeningDic.Add(platform, openingBase);
                return openingBase;
            }
            else
                return OpeningDic[platform];
        }
    }
}
