-- =====================================================
-- KTV营业数据跨库查询测试脚本
-- 测试时间范围：2025-05-01 到 2025-05-07
-- 测试店铺：ShopId = 11
-- 执行服务器：192.168.2.5 (operatedata数据库)
-- =====================================================

USE operatedata
GO

PRINT '=========================================='
PRINT 'KTV营业数据跨库查询测试开始'
PRINT '测试时间: ' + CONVERT(NVARCHAR(20), GETDATE(), 120)
PRINT '测试范围: 2025-05-01 到 2025-05-07, ShopId = 11'
PRINT '=========================================='

-- =====================================================
-- 第一步：数据库连接和链接服务器验证
-- =====================================================
PRINT ''
PRINT '第一步：验证数据库连接和链接服务器配置...'

-- 检查链接服务器是否存在
IF NOT EXISTS(SELECT * FROM sys.servers WHERE name = 'RMS2019_LINK')
BEGIN
    PRINT '✗ 错误：链接服务器 RMS2019_LINK 不存在'
    PRINT '请先执行链接服务器配置脚本'
    RETURN
END
ELSE
    PRINT '✓ 链接服务器 RMS2019_LINK 已配置'

-- 测试链接服务器连接
BEGIN TRY
    DECLARE @TestCount INT
    SELECT @TestCount = COUNT(*) FROM [RMS2019_LINK].rms2019.dbo.opencacheinfo WHERE 1=0
    PRINT '✓ 链接服务器连接测试成功'
END TRY
BEGIN CATCH
    PRINT '✗ 链接服务器连接测试失败: ' + ERROR_MESSAGE()
    PRINT '请检查网络连接和认证配置'
    RETURN
END CATCH

-- =====================================================
-- 第二步：基础数据查询测试
-- =====================================================
PRINT ''
PRINT '第二步：基础数据查询测试...'

-- 测试开台数据查询
DECLARE @OpenDataCount INT
SELECT @OpenDataCount = COUNT(*)
FROM [RMS2019_LINK].rms2019.dbo.opencacheinfo 
WHERE BookDateTime >= '2025-05-01' 
    AND BookDateTime < '2025-05-08'
    AND ShopId = 11
    AND Invno IS NOT NULL 
    AND Invno != ''

PRINT '开台数据记录数: ' + CAST(@OpenDataCount AS NVARCHAR(10))

-- 测试结账数据查询
DECLARE @CloseDataCount INT
SELECT @CloseDataCount = COUNT(*)
FROM dbo.rmcloseinfo

PRINT '结账数据总记录数: ' + CAST(@CloseDataCount AS NVARCHAR(10))

-- 测试数据关联
DECLARE @MatchedCount INT
SELECT @MatchedCount = COUNT(*)
FROM [RMS2019_LINK].rms2019.dbo.opencacheinfo o
INNER JOIN dbo.rmcloseinfo c ON o.Invno = c.InvNo
WHERE o.BookDateTime >= '2025-05-01' 
    AND o.BookDateTime < '2025-05-08'
    AND o.ShopId = 11
    AND o.Invno IS NOT NULL 
    AND o.Invno != ''

PRINT '关联匹配记录数: ' + CAST(@MatchedCount AS NVARCHAR(10))

IF @OpenDataCount > 0
    PRINT '数据关联率: ' + CAST(CAST(@MatchedCount * 100.0 / @OpenDataCount AS DECIMAL(5,2)) AS NVARCHAR(10)) + '%'

-- =====================================================
-- 第三步：创建测试专用存储过程
-- =====================================================
PRINT ''
PRINT '第三步：创建测试专用存储过程...'

-- 删除已存在的测试存储过程
IF OBJECT_ID('dbo.sp_TestKtvBusinessData', 'P') IS NOT NULL
    DROP PROCEDURE dbo.sp_TestKtvBusinessData

GO

CREATE PROCEDURE [dbo].[sp_TestKtvBusinessData]
    @StartDate DATE,
    @EndDate DATE,
    @ShopId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = @StartDate
    DECLARE @EndTime DATETIME = DATEADD(DAY, 1, @EndDate)
    DECLARE @StartExecTime DATETIME = GETDATE()
    
    BEGIN TRY
        -- 创建临时表存储测试数据
        CREATE TABLE #TestBusinessData (
            InvNo NVARCHAR(50) NOT NULL,
            ShopId INT,
            RmNo NVARCHAR(20),
            OpenTime DATETIME,
            CustName NVARCHAR(100),
            Numbers INT,
            CashAmount DECIMAL(18,2) DEFAULT 0,
            VesaAmount DECIMAL(18,2) DEFAULT 0,
            WXPayAmount DECIMAL(18,2) DEFAULT 0,
            TotalAmount DECIMAL(18,2) DEFAULT 0,
            PaymentMethod NVARCHAR(100),
            IsDirect BIT DEFAULT 0,
            ChannelType NVARCHAR(50),
            OrderUserName NVARCHAR(100),
            CtName NVARCHAR(100),
            TestDate DATE
        )
        
        CREATE CLUSTERED INDEX IX_TestBusinessData_InvNo ON #TestBusinessData(InvNo)
        
        -- 获取开台数据
        INSERT INTO #TestBusinessData (
            InvNo, ShopId, RmNo, OpenTime, CustName, Numbers, 
            OrderUserName, CtName, TestDate
        )
        SELECT 
            o.Invno,
            o.ShopId,
            o.RmNo,
            o.BookDateTime,
            o.CustName,
            ISNULL(o.Numbers, 0),
            o.OrderUserName,
            o.CtName,
            CAST(o.BookDateTime AS DATE)
        FROM [RMS2019_LINK].rms2019.dbo.opencacheinfo o
        WHERE o.BookDateTime >= @StartTime 
            AND o.BookDateTime < @EndTime
            AND o.ShopId = @ShopId
            AND o.Invno IS NOT NULL 
            AND o.Invno != ''
        
        DECLARE @OpenRecordCount INT = @@ROWCOUNT
        
        -- 更新结账数据
        UPDATE bd
        SET 
            CashAmount = ISNULL(c.Cash, 0),
            VesaAmount = ISNULL(c.Vesa, 0),
            WXPayAmount = ISNULL(c.WXPay, 0),
            TotalAmount = ISNULL(c.Cash, 0) + ISNULL(c.Vesa, 0) + ISNULL(c.WXPay, 0),
            IsDirect = 1,
            PaymentMethod = CASE 
                WHEN ISNULL(c.WXPay, 0) > 0 THEN '微信支付'
                WHEN ISNULL(c.Vesa, 0) > 0 THEN '会员卡'
                WHEN ISNULL(c.Cash, 0) > 0 THEN '现金'
                ELSE '未支付'
            END,
            ChannelType = CASE 
                WHEN ISNULL(c.WXPay, 0) > 0 THEN '线上'
                WHEN ISNULL(c.Vesa, 0) > 0 THEN '会员'
                WHEN ISNULL(c.Cash, 0) > 0 THEN '现金'
                ELSE '其他'
            END
        FROM #TestBusinessData bd
        INNER JOIN dbo.rmcloseinfo c ON bd.InvNo = c.InvNo
        
        DECLARE @CloseRecordCount INT = @@ROWCOUNT
        
        -- 返回结果集1：基础营业数据
        SELECT 
            '基础营业数据' as DataType,
            TestDate,
            InvNo,
            ShopId,
            RmNo,
            OpenTime,
            CustName,
            Numbers,
            CashAmount,
            VesaAmount,
            WXPayAmount,
            TotalAmount,
            PaymentMethod,
            IsDirect,
            ChannelType,
            OrderUserName,
            CtName
        FROM #TestBusinessData
        ORDER BY TestDate, OpenTime, InvNo
        
        -- 返回结果集2：按日期的直落统计
        SELECT 
            '直落统计' as DataType,
            TestDate,
            COUNT(*) as TotalOrders,
            SUM(CASE WHEN IsDirect = 1 THEN 1 ELSE 0 END) as DirectOrders,
            SUM(CASE WHEN IsDirect = 0 THEN 1 ELSE 0 END) as NonDirectOrders,
            CASE WHEN COUNT(*) > 0 
                THEN CAST(SUM(CASE WHEN IsDirect = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2))
                ELSE 0 
            END as DirectRate,
            SUM(CASE WHEN IsDirect = 1 THEN TotalAmount ELSE 0 END) as DirectAmount,
            SUM(CASE WHEN IsDirect = 0 THEN TotalAmount ELSE 0 END) as NonDirectAmount
        FROM #TestBusinessData
        GROUP BY TestDate
        ORDER BY TestDate
        
        -- 返回结果集3：渠道统计
        SELECT 
            '渠道统计' as DataType,
            ChannelType,
            COUNT(*) as OrderCount,
            SUM(TotalAmount) as TotalAmount,
            CASE WHEN COUNT(*) > 0 
                THEN CAST(SUM(TotalAmount) / COUNT(*) AS DECIMAL(18,2))
                ELSE 0 
            END as AvgAmount,
            CASE WHEN (SELECT COUNT(*) FROM #TestBusinessData) > 0 
                THEN CAST(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM #TestBusinessData) AS DECIMAL(5,2))
                ELSE 0 
            END as Percentage
        FROM #TestBusinessData
        GROUP BY ChannelType
        ORDER BY TotalAmount DESC
        
        -- 返回结果集4：时段统计
        SELECT 
            '时段统计' as DataType,
            CASE 
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 10 AND 13 THEN '上午(10-13)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 14 AND 17 THEN '下午(14-17)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 18 AND 21 THEN '晚上(18-21)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 22 AND 23 OR DATEPART(HOUR, OpenTime) BETWEEN 0 AND 2 THEN '深夜(22-02)'
                ELSE '其他时段'
            END as TimeSlot,
            COUNT(*) as OrderCount,
            SUM(TotalAmount) as TotalAmount,
            SUM(CASE WHEN IsDirect = 1 THEN 1 ELSE 0 END) as DirectCount,
            CASE WHEN COUNT(*) > 0 
                THEN CAST(SUM(TotalAmount) / COUNT(*) AS DECIMAL(18,2))
                ELSE 0 
            END as AvgAmount
        FROM #TestBusinessData
        WHERE OpenTime IS NOT NULL
        GROUP BY 
            CASE 
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 10 AND 13 THEN '上午(10-13)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 14 AND 17 THEN '下午(14-17)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 18 AND 21 THEN '晚上(18-21)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 22 AND 23 OR DATEPART(HOUR, OpenTime) BETWEEN 0 AND 2 THEN '深夜(22-02)'
                ELSE '其他时段'
            END
        ORDER BY TotalAmount DESC
        
        -- 返回结果集5：数据质量报告
        SELECT 
            '数据质量报告' as DataType,
            @StartDate as StartDate,
            @EndDate as EndDate,
            @ShopId as ShopId,
            @OpenRecordCount as OpenRecordCount,
            @CloseRecordCount as CloseRecordCount,
            CASE WHEN @OpenRecordCount > 0 
                THEN CAST(@CloseRecordCount * 100.0 / @OpenRecordCount AS DECIMAL(5,2))
                ELSE 0 
            END as MatchRate,
            DATEDIFF(MILLISECOND, @StartExecTime, GETDATE()) as ExecutionTimeMs,
            'SUCCESS' as Status
        
        DROP TABLE #TestBusinessData
        
    END TRY
    BEGIN CATCH
        IF OBJECT_ID('tempdb..#TestBusinessData') IS NOT NULL
            DROP TABLE #TestBusinessData
            
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
        SELECT 
            '错误信息' as DataType,
            @ErrorMessage as ErrorMessage,
            'ERROR' as Status
        
        RAISERROR(@ErrorMessage, 16, 1)
    END CATCH
END
GO

-- =====================================================
-- 第四步：执行完整测试
-- =====================================================
PRINT ''
PRINT '第四步：执行完整测试...'
PRINT '执行测试存储过程...'

-- 执行测试
EXEC sp_TestKtvBusinessData '2025-05-01', '2025-05-07', 11

PRINT ''
PRINT '=========================================='
PRINT 'KTV营业数据跨库查询测试完成'
PRINT '完成时间: ' + CONVERT(NVARCHAR(20), GETDATE(), 120)
PRINT '=========================================='
