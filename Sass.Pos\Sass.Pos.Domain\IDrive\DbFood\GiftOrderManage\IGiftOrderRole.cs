﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.DbFood.GiftOrderManage
{
    /// <summary>
    /// 赠送下单角色管理
    /// </summary>
    public interface IGiftOrderRole
    {
        ResponseContext<RespPaginationModel<GetGiftRoleDataModel>> GetGiftRoleData(GetGiftRoleDataContext context);

        ResponseContext<List<GetRoleSelectDataModel>> GetRoleSelectData(GetRoleSelectDataContext context);

        ResponseContext<EditGiftRoleDataModel> EditGiftRoleData(EditGiftRoleDataContext context);

        ResponseContext<List<GetTreeRoleDataModel>> GetTreeRoleData(GetTreeRoleDataContext context);

        ResponseContext<RespPaginationModel<GetGiftRoleDataModel>> GetGiftRoleDataByStore(GetGiftRoleDataContext context);

        ResponseContext<List<GetRoleSelectDataModel>> GetRoleSelectDataByStore(GetRoleSelectDataContext context);

        ResponseContext<EditGiftRoleDataModel> EditGiftRoleDataByStore(EditGiftRoleDataContext context);

        ResponseContext<List<GetTreeRoleDataModel>> GetTreeRoleDataByStore(GetTreeRoleDataContext context);
    }
}
