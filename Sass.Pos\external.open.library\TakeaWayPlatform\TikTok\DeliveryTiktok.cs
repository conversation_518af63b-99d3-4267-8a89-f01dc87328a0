﻿using external.open.library.TakeaWayPlatform.Model;
using external.open.library.TakeaWayPlatform.Tiktok.Request;
using external.open.library.TakeaWayPlatform.Tiktok.Response;
using external.open.library.TakeaWayPlatform.TikTok.Request;
using external.open.library.TakeaWayPlatform.TikTok.Response;
using Saas.Pos.Common.Log;
using Saas.Pos.Common.Tools;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Web;

namespace external.open.library.TakeaWayPlatform.TikTok
{
    public class DeliveryTiktok : LocalLifeBase
    {
        private static GetAccesstokenResponse AccesstokenResponse = null;
        private static readonly string ClientKey = "awalvtm53hqlnwde";
        private static readonly string Secret = "c62e39216ea160bef77ae8227e5e4e70";
        private static readonly string AccountId = "6944992734902011918";
        private static readonly string BaseUrl = "https://open.douyin.com/";
        private static readonly string SavePath = AppDomain.CurrentDomain.BaseDirectory + "\\tiktok_accesstoken.txt";

        public DeliveryTiktok()
        {
            if (!File.Exists(SavePath))
                AccesstokenResponse = GetAccessToken().data;
            else
                AccesstokenResponse = File.ReadAllText(SavePath).ToObject<GetAccesstokenResponse>();
        }

        public override List<ConsumeResponseModel> Consume(ConsumeModel consume)
        {
            var response = new TiktokResponseBase<TiktokConsumeResponse>();
            //核销准备动作
            var queryResponse = Prepare(new QueryCouponModel()
            {
                Code = consume.ReceiptCode,
                OpenShopId = consume.OpenShopId
            });

            string url = "goodlife/v1/fulfilment/certificate/verify/";
            var header = new Dictionary<string, string>();
            header.Add("access-token", AccesstokenResponse.access_token);

            var encrypted_codes = new List<string>();

            //对次卡的处理
            queryResponse.certificates.ForEach(w =>
            {
                //如果是次卡
                if (w.IsTimeCard)
                {
                    for (int i = 0; i < consume.Count; i++)
                    {
                        encrypted_codes.Add(w.EncryptedCode);
                    }
                }
                else
                    encrypted_codes.Add(w.EncryptedCode);
            });

            encrypted_codes = encrypted_codes.Take(consume.Count).ToList();
            var request = new TiktokConsumeRequest()
            {
                order_id = queryResponse.OrderId,
                poi_id = consume.OpenShopId,
                verify_token = queryResponse.VerifyToken,
                encrypted_codes = encrypted_codes
            };

            Retry(() =>
            {
                response = SendRequest<TiktokConsumeRequest, TiktokResponseBase<TiktokConsumeResponse>>(BaseUrl + url, request, header: header);
                if (response.extra.error_code != 0 || response.extra.error_code != 0)
                    throw new WayPlatformException(response.extra.description);
                if (response.data == null || response.data.verify_results.Count <= 0)
                    throw new WayPlatformException("核验失败，未返回相关数据！");
            }, () =>
            {
                AccesstokenResponse = GetAccessToken().data;
                //重新对token赋值
                header["access-token"] = AccesstokenResponse.access_token;
            }, new List<string>() { "access_token过期,请刷新或重新授权" }, 2);

            //关联核销准备卡券获取商品信息
            var verify_results = (from vr in response.data.verify_results
                                  join resp in queryResponse.certificates on vr.code equals resp.EncryptedCode
                                  select new
                                  {
                                      product_id = resp.ProductId,
                                      code = vr.code,
                                      msg = vr.msg,
                                      result = vr.result,
                                      account_id = vr.account_id,
                                      certificate_id = vr.certificate_id,
                                      certificate_no = vr.certificate_no,
                                      id_card = vr.id_card,
                                      order_id = vr.order_id,
                                      origin_code = vr.origin_code,
                                      verify_id = vr.verify_id,
                                      resp.PayAmount,
                                      resp.OriginalAmount,
                                      resp.MerchantAmount,
                                      resp.BusDis,
                                      resp.PlatformDis,
                                      resp.Title
                                  }).ToList();

            var result = verify_results.Select(w => new ConsumeResponseModel()
            {
                OrderId = w.order_id,
                ProductItemId = w.product_id,
                CertificateId = w.certificate_id,
                VerifyId = w.verify_id,
                OpenShopId = w.account_id,
                DealGroupId = w.product_id,
                ReceiptCode = w.origin_code,
                Result = w.result,
                EncryptedCode = w.code,
                Count = 1,
                MerchantAmount = w.MerchantAmount,
                OriginalAmount = w.OriginalAmount,
                PayAmount = w.PayAmount,
                BusDis = w.BusDis,
                PlatformDis = w.PlatformDis,
                Title = w.Title
            }).ToList();

            return result;
        }

        public override QueryCouponResponse QueryCoupon(QueryCouponModel query)
        {
            var queryResponse = Prepare(new QueryCouponModel()
            {
                Code = query.Code,
                OpenShopId = query.OpenShopId
            });

            var queryStr = HttpUtility.UrlEncode(queryResponse.certificates.FirstOrDefault().EncryptedCode);
            var url = "goodlife/v1/fulfilment/certificate/get/?encrypted_code=" + queryStr;
            var header = new Dictionary<string, string>();
            header.Add("access-token", AccesstokenResponse.access_token);
            var response = new TiktokResponseBase<TiktokQueryCouponResponse>();
            Retry(() =>
            {
                response = SendRequest<object, TiktokResponseBase<TiktokQueryCouponResponse>>(BaseUrl + url, null, "GET", header);
                if (response.extra.error_code != 0 || response.extra.error_code != 0)
                    throw new WayPlatformException(response.extra.description);
            }, () =>
            {
                AccesstokenResponse = GetAccessToken().data;
                //重新对token赋值
                header["access-token"] = AccesstokenResponse.access_token;
            }, new List<string>() { "access_token过期,请刷新或重新授权" }, 2);

            return new QueryCouponResponse()
            {
                Code = query.Code,
                Count = queryResponse.certificates.Sum(w => w.Count),
                TotalCount = queryResponse.certificates.Sum(w => w.TotalCount),
                ThirdFdNos = queryResponse.certificates.Select(w => w.ProductId).ToList(),
                Status = response.data.certificate.status,
                StartTime = ConvertToDate(response.data.certificate.start_time),
                EndTime = ConvertToDate(response.data.certificate.expire_time),
            };
        }

        public override ReverseConsumeResponseModel ReverseConsume(ReverseConsumeModel reverseConsume)
        {
            var url = "goodlife/v1/fulfilment/certificate/cancel/";
            var header = new Dictionary<string, string>();
            header.Add("access-token", AccesstokenResponse.access_token);

            var request = new TiktokReverseConsumeRequest()
            {
                verify_id = reverseConsume.verify_id,
                certificate_id = reverseConsume.certificate_id
            };

            Retry(() =>
            {
                var response = SendRequest<TiktokReverseConsumeRequest, TiktokResponseBase<TiktokReverseConsumeResponse>>(BaseUrl + url, request, header: header);
                //两个都为0才算撤销核销
                if (response.extra.error_code != 0 || response.data.error_code != 0)
                    throw new WayPlatformException(response.extra.description);
            }, () =>
            {
                AccesstokenResponse = GetAccessToken().data;
                //重新对token赋值
                header["access-token"] = AccesstokenResponse.access_token;
            }, new List<string>() { "access_token过期,请刷新或重新授权" }, 2);

            return new ReverseConsumeResponseModel()
            {
                ReceiptCode = reverseConsume.receipt_code,
                DealGroupId = string.Empty,
                DealId = reverseConsume.app_deal_id,
                OpenShopId = reverseConsume.OpenShopId
            };
        }

        /// <summary>
        /// 验券准备
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public override PrepareCouponModel Prepare(QueryCouponModel query)
        {
            var queryResponse = new TiktokResponseBase<TiktokQueryConsumeResponse>();

            //先准备验券，获取所有待验券码
            var queryUrl = "goodlife/v1/fulfilment/certificate/prepare/";
            if (query.Code.Contains("http"))
            {
                var location = HttpHelper.GetEncrypCode(query.Code);
                var startIndex = location.IndexOf("object_id");
                location = location.Substring(startIndex + 10);
                var endIndex = location.IndexOf("&");
                location = location.Substring(0, endIndex);
                var queryStr = HttpUtility.UrlEncode(location);
                queryUrl += "?encrypted_data=" + queryStr;
            }
            else
                queryUrl += "?code=" + query.Code;

            var header = new Dictionary<string, string>();
            header.Add("access-token", AccesstokenResponse.access_token);
            Retry(() =>
            {
                queryResponse = SendRequest<object, TiktokResponseBase<TiktokQueryConsumeResponse>>(BaseUrl + queryUrl, null, "GET", header);
                if (queryResponse.extra.error_code != 0 || queryResponse.extra.error_code != 0)
                    throw new WayPlatformException(queryResponse.extra.description);
            }, () =>
            {
                AccesstokenResponse = GetAccessToken().data;
                //重新对token赋值
                header["access-token"] = AccesstokenResponse.access_token;
            }, new List<string>() { "access_token过期,请刷新或重新授权" }, 2);

            LogHelper.Info("券号：" + query.Code + "核销准备，返回记录：" + queryResponse.ToJson());
            var result = new PrepareCouponModel();
            result.OrderId = queryResponse.data.order_id;
            result.VerifyToken = queryResponse.data.verify_token;
            result.certificates = queryResponse.data.certificates.Select(w => new Model.Certificate()
            {
                Code = query.Code.Contains("http") ? w.encrypted_code : query.Code,
                EncryptedCode = w.encrypted_code,
                StartTime = ConvertToDate(w.start_time),
                EndTime = ConvertToDate(w.expire_time),
                Count = w.time_card == null ? 1 : (w.time_card.times_count - w.time_card.times_used),
                TotalCount = w.time_card == null ? 1 : w.time_card.times_count,
                OriginalAmount = (decimal)w.amount.list_market_amount / 100,
                MerchantAmount = (decimal)w.amount.original_amount / 100,
                PayAmount = Math.Round(((decimal)w.amount.pay_amount / 100) + ((decimal)w.amount.payment_discount_amount / 100), 2),
                BusDis = Math.Round((decimal)(w.amount?.merchant_ticket_amount ?? 0) / 100, 2),
                PlatformDis = Math.Round((decimal)(w.amount?.platform_discount_amount ?? 0) / 100, 2),
                Title = w.sku.title,
                ProductId = w.sku.sku_id,
                IsTimeCard = w.time_card != null
            }).ToList();

            return result;
        }

        public override List<QueryProductResponse> QueryProduct(QueryProductModel model)
        {
            string url = "goodlife/v1/goods/product/online/query/?count=" + 50 + "&account_id=" + AccountId + "&goods_creator_type=1";

            var header = new Dictionary<string, string>();
            header.Add("access-token", AccesstokenResponse.access_token);

            var list = new List<TiktokQueryProductResponse>();

            bool hasMore = true;
            while (hasMore)
            {
                var response = new TiktokResponseBase<TiktokQueryProductResponse>();
                Retry(() =>
                {
                    response = SendRequest<TiktokQueryProductRequest, TiktokResponseBase<TiktokQueryProductResponse>>(BaseUrl + url, null, "GET", header);
                    if (response.extra.error_code != 0 || response.extra.error_code != 0 || response.BaseResp.StatusCode != 0)
                    {
                        if (!string.IsNullOrEmpty(response.extra.description))
                            throw new WayPlatformException(response.extra.description);
                        else if (!string.IsNullOrEmpty(response.data.description))
                            throw new WayPlatformException(response.extra.description);
                        else
                            throw new WayPlatformException(response.BaseResp.StatusMessage);
                    }

                    list.Add(response.data);
                    hasMore = response.data.has_more;
                    if (url.IndexOf("&cursor=") > 0)//如果不是第一次，则执行修改字符串逻辑，否则执行添加字符串
                    {
                        var index = url.IndexOf("&cursor=");
                        url = url.Remove(index);
                    }

                    url += "&cursor=" + response.data.next_cursor;
                }, () =>
                {
                    AccesstokenResponse = GetAccessToken().data;
                    //重新对token赋值
                    header["access-token"] = AccesstokenResponse.access_token;
                }, new List<string>() { "access_token过期,请刷新或重新授权" }, 2);
            }

            var result = new List<QueryProductResponse>();
            result = list.Select(data =>
            {
                return data.products.Select(w => new QueryProductResponse()
                {
                    ProductId = w.product.product_id,
                    ProductName = w.product.product_name,
                    ProductType = w.product.product_type.ToString(),
                    OriginAmount = w.sku.origin_amount / 100,
                    ActualAmount = w.sku.actual_amount / 100,
                    Status = w.online_status
                }).ToList();
            }).SelectMany(w => w).ToList();

            return result;
        }

        /// <summary>
        /// 获取抖音平台的token（有效时间为2小时）
        /// </summary>
        /// <returns></returns>
        private TiktokResponseBase<GetAccesstokenResponse> GetAccessToken()
        {
            var request = new
            {
                client_key = ClientKey,
                client_secret = Secret,
                grant_type = "client_credential"
            };

            var url = "oauth/client_token/";

            var response = SendRequest<object, TiktokResponseBase<GetAccesstokenResponse>>(BaseUrl + url, request);
            if (response.data.error_code != 0 || response.data.error_code != 0)
                throw new WayPlatformException(response.data.description);

            File.WriteAllText(SavePath, response.data.ToJson());
            return response;
        }

        private OutputT SendRequest<InputT, OutputT>(string url, InputT postData, string method = "POST", Dictionary<string, string> header = null)
        {
            ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidation);
            ServicePointManager.SecurityProtocol = (SecurityProtocolType)192 | (SecurityProtocolType)768 | (SecurityProtocolType)3072 | SecurityProtocolType.Tls;
            HttpWebRequest client = (HttpWebRequest)WebRequest.Create(url);

            var guid = Guid.NewGuid().ToString();
            //本地日志记录核销
            LogHelper.Info("唯一记录：" + guid + "，请求地址：" + url + "，请求记录：" + postData.ToJson());

            try
            {
                client.Method = method;
                client.ContentType = "application/json";
                if (header != null)
                {
                    foreach (var item in header)
                    {
                        client.Headers.Add(item.Key, item.Value);
                    }
                }

                if (postData != null)
                {
                    var byteData = Encoding.UTF8.GetBytes(postData.ToJson());
                    using (Stream dataStream = client.GetRequestStream())
                    {
                        dataStream.Write(byteData, 0, byteData.Length);
                    }
                }

                WebResponse response = client.GetResponse();
                using (StreamReader reader = new StreamReader(response.GetResponseStream()))
                {
                    string responseBody = reader.ReadToEnd();
                    if (string.IsNullOrEmpty(responseBody))
                        throw new Exception("未获取到响应信息！");

                    LogHelper.Info("唯一记录：" + guid + "，返回记录：" + responseBody);
                    var data = responseBody.ToObject<OutputT>();
                    return data;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("请求出现异常！" + ex.Message);
            }
        }

        private static string GenerateSign<T>(T t)
        {
            var type = typeof(T);
            var properties = type.GetProperties();
            var result = new Dictionary<string, string>();
            foreach (var item in properties)
            {
                //包括公共参数和业务参数，但除去sign参数和值为空的参数
                if (item.Name != "sign" && item.GetValue(t, null) != null)
                {
                    result.Add(item.Name, item.GetValue(t, null).ToString());
                }
            }

            return ModelToDic(result);
        }

        private static string ModelToDic(Dictionary<string, string> param)
        {
            List<string> array = new List<string>();
            foreach (var item in param)
            {
                array.Add(item.Key);
            }
            array = array.OrderBy(str => string.Concat(str.Select(w => ((int)w).ToString("D3")))).ToList();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append(Secret);
            foreach (var key in array)
            {
                stringBuilder.Append("&").Append(key).Append("=").Append(param[key]);
            }
            stringBuilder.Append(Secret);
            return stringBuilder.ToString();
        }

        private static DateTime ConvertToDate(long timeStamp)
        {
            DateTime unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            DateTime localDateTime = unixEpoch.AddSeconds(timeStamp).ToLocalTime();

            return localDateTime;
        }

        public override QueryOrderResponse QueryOrder(QueryOrderModel model)
        {
            string url = "goodlife/v1/fulfilment/certificate/query/";
            url += $"?account_id={AccountId}&order_id={model.OrderId}";
            var header = new Dictionary<string, string>();
            header.Add("access-token", AccesstokenResponse.access_token);

            var response = new TiktokResponseBase<TiktokQueryCertificateResponse>();
            Retry(() =>
            {
                response = SendRequest<TiktokQueryOrderRequest, TiktokResponseBase<TiktokQueryCertificateResponse>>(BaseUrl + url, null, "GET", header);
            }, () =>
            {
                AccesstokenResponse = GetAccessToken().data;
                //重新对token赋值
                header["access-token"] = AccesstokenResponse.access_token;
            }, new List<string>() { "access_token过期,请刷新或重新授权" }, 2);

            //如果是抖音次卡
            if (response.data.certificates.FirstOrDefault().time_card != null)
            {
                return new QueryOrderResponse()
                {
                    IsTimeCard = true,
                    OrderId = model.OrderId,
                    Receipt = response.data.certificates.Select(w =>
                    {
                        //拼凑已使用和未使用的数据
                        var noUses = w.time_card.times_count - w.time_card.times_used;
                        var codes = new List<ReceiptInfo>();
                        codes.AddRange(w.verify_records.Select(code => new ReceiptInfo()
                        {
                            ReceiptCode = code.verify_id,
                            Status = 2,
                            PayAmount = w.amount.pay_amount / 100
                        }).ToList());

                        if (noUses > 0)
                        {
                            for (int i = 0; i < noUses; i++)
                            {
                                codes.Add(new ReceiptInfo()
                                {
                                    ReceiptCode = i.ToString(),
                                    Status = 1,
                                    PayAmount = w.amount.pay_amount / 100
                                });
                            }
                        }

                        return codes;
                    }).SelectMany(w => w).ToList()
                };
            }
            else
            {
                return new QueryOrderResponse()
                {
                    IsTimeCard = false,
                    OrderId = model.OrderId,
                    Receipt = response.data.certificates.Select(w =>
                    {
                        int status = w.status;
                        if (w.status == 4)
                            status = 3;
                        return new ReceiptInfo()
                        {
                            ReceiptCode = w.code,
                            Status = status,
                            PayAmount = w.amount.pay_amount / 100
                        };
                    }).ToList()
                };
            }
        }
    }
}
