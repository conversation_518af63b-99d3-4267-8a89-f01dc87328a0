<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod">
            <summary>
            Yürütülecek TestMethod.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestMethodName">
            <summary>
            Test metodunun adını alır.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestClassName">
            <summary>
            Test sınıfının adını alır.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ReturnType">
            <summary>
            Test metodunun dönüş türünü alır.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ParameterTypes">
            <summary>
            Test metodunun parametrelerini alır.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.MethodInfo">
            <summary>
            Test metodu için methodInfo değerini alır.
            </summary>
            <remarks>
            This is just to retrieve additional information about the method.
            Do not directly invoke the method using MethodInfo. Use ITestMethod.Invoke instead.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.Invoke(System.Object[])">
            <summary>
            Test metodunu çağırır.
            </summary>
            <param name="arguments">
            Test metoduna geçirilecek bağımsız değişkenler. (Örn. Veri temelli için)
            </param>
            <returns>
            Test yöntemi çağırma sonucu.
            </returns>
            <remarks>
            This call handles asynchronous test methods as well.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAllAttributes(System.Boolean)">
            <summary>
            Test metodunun tüm özniteliklerini alır.
            </summary>
            <param name="inherit">
            Üst sınıfta tanımlanan özniteliğin geçerli olup olmadığını belirtir.
            </param>
            <returns>
            Tüm öznitelikler.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAttributes``1(System.Boolean)">
            <summary>
            Belirli bir türdeki özniteliği alır.
            </summary>
            <typeparam name="AttributeType"> System.Attribute type. </typeparam>
            <param name="inherit">
            Üst sınıfta tanımlanan özniteliğin geçerli olup olmadığını belirtir.
            </param>
            <returns>
            Belirtilen türün öznitelikleri.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Helper">
            <summary>
            Yardımcı.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNull(System.Object,System.String,System.String)">
            <summary>
            Denetim parametresi null değil.
            </summary>
            <param name="param">
            Parametre.
            </param>
            <param name="parameterName">
            Parametre adı.
            </param>
            <param name="message">
            İleti.
            </param>
            <exception cref="T:System.ArgumentNullException"> Throws argument null exception when parameter is null. </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNullOrEmpty(System.String,System.String,System.String)">
            <summary>
            Denetim parametresi null veya boş değil.
            </summary>
            <param name="param">
            Parametre.
            </param>
            <param name="parameterName">
            Parametre adı.
            </param>
            <param name="message">
            İleti.
            </param>
            <exception cref="T:System.ArgumentException"> Throws ArgumentException when parameter is null. </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod">
            <summary>
            Veri tabanlı testlerde veri satırlarına erişme şekline yönelik sabit listesi.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Sequential">
            <summary>
            Satırlar sıralı olarak döndürülür.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random">
            <summary>
            Satırlar rastgele sırayla döndürülür.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute">
            <summary>
            Bir test metodu için satır içi verileri tanımlayan öznitelik.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="data1"> Veri nesnesi. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object,System.Object[])">
            <summary>
            Bir bağımsız değişken dizisi alan <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="data1"> Bir veri nesnesi. </param>
            <param name="moreData"> Daha fazla veri. </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.Data">
            <summary>
            Çağıran test metodu verilerini alır.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.DisplayName">
            <summary>
            Özelleştirme için test sonuçlarında görünen adı alır veya ayarlar.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            <summary>
            Onay sonuçlandırılmadı özel durumu.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String,System.Exception)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="msg"> İleti. </param>
            <param name="ex"> Özel durum. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="msg"> İleti. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> sınıfının yeni bir örneğini başlatır.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException">
            <summary>
            InternalTestFailureException sınıfı. Bir test çalışmasının iç hatasını belirtmek için kullanılır
            </summary>
            <remarks>
            This class is only added to preserve source compatibility with the V1 framework.
            For all practical purposes either use AssertFailedException/AssertInconclusiveException.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String,System.Exception)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="msg"> Özel durum iletisi. </param>
            <param name="ex"> Özel durum. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="msg"> Özel durum iletisi. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> sınıfının yeni bir örneğini başlatır.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute">
            <summary>
            Belirtilen türde bir özel durum beklemeyi belirten öznitelik
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type)">
            <summary>
            Beklenen tür ile <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> sınıfının yeni bir örneğini başlatır
            </summary>
            <param name="exceptionType">Beklenen özel durum türü</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type,System.String)">
            <summary>
            Beklenen tür ve test tarafından özel durum oluşturulmadığında eklenecek ileti ile <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> sınıfının
            yeni bir örneğini başlatır.
            </summary>
            <param name="exceptionType">Beklenen özel durum türü</param>
            <param name="noExceptionMessage">
            Test bir özel durum oluşturmama nedeniyle başarısız olursa test sonucuna dahil edilecek ileti
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.ExceptionType">
            <summary>
            Beklenen özel durumun Türünü belirten bir değer alır
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.AllowDerivedTypes">
            <summary>
            Beklenen özel durumun türünden türetilmiş türlerin beklenen özel durum türü olarak değerlendirilmesine izin verilip verilmeyeceğini
            belirten değeri alır veya ayarlar
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.NoExceptionMessage">
            <summary>
            Özel durum oluşturulamaması nedeniyle testin başarısız olması durumunda, test sonucuna dahil edilecek olan iletiyi alır
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.Verify(System.Exception)">
            <summary>
            Birim testi tarafından oluşturulan özel durum türünün beklendiğini doğrular
            </summary>
            <param name="exception">Birim testi tarafından oluşturulan özel durum</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute">
            <summary>
            Birim testinden bir özel durum beklemek için belirtilen özniteliklerin temel sınıfı
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor">
            <summary>
            Varsayılan bir 'özel durum yok' iletisi ile <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> sınıfının yeni bir örneğini başlatır
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor(System.String)">
            <summary>
            Bir 'özel durum yok' iletisi ile <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> sınıfının yeni bir örneğini başlatır
            </summary>
            <param name="noExceptionMessage">
            Test bir özel durum oluşturmama nedeniyle başarısız olursa test sonucuna
            dahil edilecek özel durum
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.NoExceptionMessage">
            <summary>
            Özel durum oluşturulamaması nedeniyle testin başarısız olması durumunda, test sonucuna dahil edilecek olan iletiyi alır
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.SpecifiedNoExceptionMessage">
            <summary>
            Özel durum oluşturulamaması nedeniyle testin başarısız olması durumunda, test sonucuna dahil edilecek olan iletiyi alır
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.GetDefaultNoExceptionMessage(System.String)">
            <summary>
            Varsayılan 'özel durum yok' iletisini alır
            </summary>
            <param name="expectedExceptionAttributeTypeName">ExpectedException özniteliği tür adı</param>
            <returns>Özel durum olmayan varsayılan ileti</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.Verify(System.Exception)">
            <summary>
            Özel durumun beklenip beklenmediğini belirler. Metot dönüş yapıyorsa, özel
            durumun beklendiği anlaşılır. Metot bir özel durum oluşturuyorsa, özel durumun
            beklenmediği anlaşılır ve oluşturulan özel durumun iletisi test sonucuna 
            eklenir. Kolaylık sağlamak amacıyla <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert"/> sınıfı kullanılabilir.
            <see cref="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive"/> kullanılırsa ve onaylama başarısız olursa,
            test sonucu Belirsiz olarak ayarlanır.
            </summary>
            <param name="exception">Birim testi tarafından oluşturulan özel durum</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.RethrowIfAssertException(System.Exception)">
            <summary>
            Özel durum bir AssertFailedException veya AssertInconclusiveException ise özel durumu yeniden oluşturur
            </summary>
            <param name="exception">Bir onaylama özel durumu ise yeniden oluşturulacak özel durum</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper">
            <summary>
            Bu sınıf, kullanıcının genel türler kullanan türlere yönelik birim testleri yapmasına yardımcı olmak üzere tasarlanmıştır.
            GenericParameterHelper bazı genel tür kısıtlamalarını yerine getirir;
            örneğin:
            1. genel varsayılan oluşturucu
            2. ortak arabirim uygular: IComparable, IEnumerable
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> sınıfının C# genel türlerindeki 'newable'
            kısıtlamasını karşılayan yeni bir örneğini başlatır.
            </summary>
            <remarks>
            This constructor initializes the Data property to a random value.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor(System.Int32)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> sınıfının, Data özelliğini kullanıcı
            tarafından sağlanan bir değerle başlatan yeni bir örneğini başlatır.
            </summary>
            <param name="data">Herhangi bir tamsayı değeri</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Data">
            <summary>
            Verileri alır veya ayarlar
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Equals(System.Object)">
            <summary>
            İki GenericParameterHelper nesnesi için değer karşılaştırması yapar
            </summary>
            <param name="obj">karşılaştırma yapılacak nesne</param>
            <returns>nesne bu 'this' GenericParameterHelper nesnesiyle aynı değere sahipse true.
            aksi takdirde false.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetHashCode">
            <summary>
            Bu nesne için bir karma kod döndürür.
            </summary>
            <returns>Karma kod.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.CompareTo(System.Object)">
            <summary>
            İki <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> nesnesinin verilerini karşılaştırır.
            </summary>
            <param name="obj">Karşılaştırılacak nesne.</param>
            <returns>
            Bu örnek ve değerin göreli değerlerini gösteren, işaretli sayı.
            </returns>
            <exception cref="T:System.NotSupportedException">
            Thrown when the object passed in is not an instance of <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetEnumerator">
            <summary>
            Uzunluğu Data özelliğinden türetilmiş bir IEnumerator nesnesi
            döndürür.
            </summary>
            <returns>IEnumerator nesnesi</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Clone">
            <summary>
            Geçerli nesneye eşit olan bir GenericParameterHelper nesnesi
            döndürür.
            </summary>
            <returns>Kopyalanan nesne.</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger">
            <summary>
            Kullanıcıların tanılama amacıyla birim testlerindeki izlemeleri günlüğe kaydetmesini/yazmasını sağlar.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessageHandler">
            <summary>
            LogMessage işleyicisi.
            </summary>
            <param name="message">Günlüğe kaydedilecek ileti.</param>
        </member>
        <member name="E:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.OnLogMessage">
            <summary>
            Dinlenecek olay. Birim testi yazıcı bir ileti yazdığında oluşturulur.
            Genellikle bağdaştırıcı tarafından kullanılır.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessage(System.String,System.Object[])">
            <summary>
            İletileri günlüğe kaydetmek için çağrılacak test yazıcısı API'si.
            </summary>
            <param name="format">Yer tutucuları olan dize biçimi.</param>
            <param name="args">Yer tutucu parametreleri.</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute">
            <summary>
            TestCategory özniteliği; bir birim testinin kategorisini belirtmek için kullanılır.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute"/> sınıfının yeni bir örneğini başlatır ve kategoriyi teste uygular.
            </summary>
            <param name="testCategory">
            Test Kategorisi.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.TestCategories">
            <summary>
            Teste uygulanan test kategorilerini alır.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute">
            <summary>
            "Category" özniteliğinin temel sınıfı
            </summary>
            <remarks>
            The reason for this attribute is to let the users create their own implementation of test categories.
            - test framework (discovery, etc) deals with TestCategoryBaseAttribute.
            - The reason that TestCategories property is a collection rather than a string,
              is to give more flexibility to the user. For instance the implementation may be based on enums for which the values can be OR'ed
              in which case it makes sense to have single attribute rather than multiple ones on the same test.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute"/> sınıfının yeni bir örneğini başlatır.
            Kategoriyi teste uygular. TestCategories tarafından döndürülen
            dizeler /category komutu içinde testleri filtrelemek için kullanılır
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.TestCategories">
            <summary>
            Teste uygulanan test kategorisini alır.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            <summary>
            AssertFailedException sınıfı. Test çalışmasının başarısız olduğunu göstermek için kullanılır
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String,System.Exception)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="msg"> İleti. </param>
            <param name="ex"> Özel durum. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="msg"> İleti. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> sınıfının yeni bir örneğini başlatır.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert">
            <summary>
            Birim testleri içindeki çeşitli koşulları test etmeye yönelik yardımcı
            sınıf koleksiyonu. Test edilen koşul karşılanmazsa bir özel durum
            oluşturulur.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.That">
            <summary>
            Assert işlevselliğinin tekil örneğini alır.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void IsOfType&lt;T&gt;(this Assert assert, object obj)"
            Users could then use a syntax similar to the default assertions which in this case is "Assert.That.IsOfType&lt;Dog&gt;(animal);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean)">
            <summary>
            Belirtilen koşulun true olup olmadığını test eder ve koşul false ise
            bir özel durum oluşturur.
            </summary>
            <param name="condition">
            Testte true olması beklenen koşul.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String)">
            <summary>
            Belirtilen koşulun true olup olmadığını test eder ve koşul false ise
            bir özel durum oluşturur.
            </summary>
            <param name="condition">
            Testte true olması beklenen koşul.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="condition"/>
            false. İleti test sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String,System.Object[])">
            <summary>
            Belirtilen koşulun true olup olmadığını test eder ve koşul false ise
            bir özel durum oluşturur.
            </summary>
            <param name="condition">
            Testte true olması beklenen koşul.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="condition"/>
            false. İleti test sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean)">
            <summary>
            Belirtilen koşulun false olup olmadığını test eder ve koşul true ise
            bir özel durum oluşturur.
            </summary>
            <param name="condition">
            Testte false olması beklenen koşul.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String)">
            <summary>
            Belirtilen koşulun false olup olmadığını test eder ve koşul true ise
            bir özel durum oluşturur.
            </summary>
            <param name="condition">
            Testte false olması beklenen koşul.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="condition"/>
            true. İleti test sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String,System.Object[])">
            <summary>
            Belirtilen koşulun false olup olmadığını test eder ve koşul true ise
            bir özel durum oluşturur.
            </summary>
            <param name="condition">
            Testte false olması beklenen koşul.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="condition"/>
            true. İleti test sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object)">
            <summary>
            Belirtilen nesnenin null olup olmadığını test eder ve değilse bir
            özel durum oluşturur.
            </summary>
            <param name="value">
            Testte null olması beklenen nesne.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String)">
            <summary>
            Belirtilen nesnenin null olup olmadığını test eder ve değilse bir
            özel durum oluşturur.
            </summary>
            <param name="value">
            Testte null olması beklenen nesne.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="value"/>
            null değil. İleti test sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String,System.Object[])">
            <summary>
            Belirtilen nesnenin null olup olmadığını test eder ve değilse bir
            özel durum oluşturur.
            </summary>
            <param name="value">
            Testte null olması beklenen nesne.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="value"/>
            null değil. İleti test sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object)">
            <summary>
            Belirtilen dizenin null olup olmadığını test eder ve null ise bir özel durum
            oluşturur.
            </summary>
            <param name="value">
            Testte null olmaması beklenen nesne.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String)">
            <summary>
            Belirtilen dizenin null olup olmadığını test eder ve null ise bir özel durum
            oluşturur.
            </summary>
            <param name="value">
            Testte null olmaması beklenen nesne.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="value"/>
            null. İleti test sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String,System.Object[])">
            <summary>
            Belirtilen dizenin null olup olmadığını test eder ve null ise bir özel durum
            oluşturur.
            </summary>
            <param name="value">
            Testte null olmaması beklenen nesne.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="value"/>
            null. İleti test sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object)">
            <summary>
            Belirtilen her iki nesnenin de aynı nesneye başvurup başvurmadığını test eder
            ve iki giriş aynı nesneye başvurmuyorsa bir özel durum oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci nesne. Testte beklenen değerdir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci nesne. Test kapsamındaki kod tarafından bu değer oluşturulur.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String)">
            <summary>
            Belirtilen her iki nesnenin de aynı nesneye başvurup başvurmadığını test eder
            ve iki giriş aynı nesneye başvurmuyorsa bir özel durum oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci nesne. Testte beklenen değerdir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci nesne. Test kapsamındaki kod tarafından bu değer oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şununla aynı değil: <paramref name="expected"/>. İleti test
            sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Belirtilen her iki nesnenin de aynı nesneye başvurup başvurmadığını test eder
            ve iki giriş aynı nesneye başvurmuyorsa bir özel durum oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci nesne. Testte beklenen değerdir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci nesne. Test kapsamındaki kod tarafından bu değer oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şununla aynı değil: <paramref name="expected"/>. İleti test
            sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object)">
            <summary>
            Belirtilen nesnelerin farklı nesnelere başvurup başvurmadığını test eder
            ve iki giriş aynı nesneye başvuruyorsa bir özel durum oluşturur.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci nesne. Testte bu değerin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci nesne. Test kapsamındaki kod tarafından bu değer oluşturulur.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String)">
            <summary>
            Belirtilen nesnelerin farklı nesnelere başvurup başvurmadığını test eder
            ve iki giriş aynı nesneye başvuruyorsa bir özel durum oluşturur.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci nesne. Testte bu değerin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci nesne. Test kapsamındaki kod tarafından bu değer oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şununla aynıdır: <paramref name="notExpected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Belirtilen nesnelerin farklı nesnelere başvurup başvurmadığını test eder
            ve iki giriş aynı nesneye başvuruyorsa bir özel durum oluşturur.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci nesne. Testte bu değerin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci nesne. Test kapsamındaki kod tarafından bu değer oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şununla aynıdır: <paramref name="notExpected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0)">
            <summary>
            Belirtilen değerlerin eşit olup olmadığını test eder ve iki değer eşit değilse
            bir özel durum oluşturur. Mantıksal değerleri eşit olsa bile
            farklı sayısal türler eşit değil olarak kabul edilir. 42L, 42'ye eşit değildir.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Karşılaştırılacak birinci değer. Testte bu değer beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci değer. Test kapsamındaki kod tarafından bu değer oluşturulur.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String)">
            <summary>
            Belirtilen değerlerin eşit olup olmadığını test eder ve iki değer eşit değilse
            bir özel durum oluşturur. Mantıksal değerleri eşit olsa bile
            farklı sayısal türler eşit değil olarak kabul edilir. 42L, 42'ye eşit değildir.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Karşılaştırılacak birinci değer. Testte bu değer beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci değer. Test kapsamındaki kod tarafından bu değer oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşit değil: <paramref name="expected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Belirtilen değerlerin eşit olup olmadığını test eder ve iki değer eşit değilse
            bir özel durum oluşturur. Mantıksal değerleri eşit olsa bile
            farklı sayısal türler eşit değil olarak kabul edilir. 42L, 42'ye eşit değildir.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Karşılaştırılacak birinci değer. Testte bu değer beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci değer. Test kapsamındaki kod tarafından bu değer oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşit değil: <paramref name="expected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0)">
            <summary>
            Belirtilen değerlerin eşit olup olmadığını test eder ve iki değer eşitse
            bir özel durum oluşturur. Mantıksal değerleri eşit olsa bile
            farklı sayısal türler eşit değil olarak kabul edilir. 42L, 42'ye eşit değildir.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Karşılaştırılacak birinci değer. Testte bu değerin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci değer. Test kapsamındaki kod tarafından bu değer oluşturulur.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String)">
            <summary>
            Belirtilen değerlerin eşit olup olmadığını test eder ve iki değer eşitse
            bir özel durum oluşturur. Mantıksal değerleri eşit olsa bile
            farklı sayısal türler eşit değil olarak kabul edilir. 42L, 42'ye eşit değildir.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Karşılaştırılacak birinci değer. Testte bu değerin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci değer. Test kapsamındaki kod tarafından bu değer oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşittir: <paramref name="notExpected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Belirtilen değerlerin eşit olup olmadığını test eder ve iki değer eşitse
            bir özel durum oluşturur. Mantıksal değerleri eşit olsa bile
            farklı sayısal türler eşit değil olarak kabul edilir. 42L, 42'ye eşit değildir.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Karşılaştırılacak birinci değer. Testte bu değerin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci değer. Test kapsamındaki kod tarafından bu değer oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşittir: <paramref name="notExpected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object)">
            <summary>
            Belirtilen nesnelerin eşit olup olmadığını test eder ve iki nesne eşit değilse
            bir özel durum oluşturur. Mantıksal değerleri eşit olsa bile
            farklı sayısal türler eşit değil olarak kabul edilir. 42L, 42'ye eşit değildir.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci nesne. Testte beklenen nesnedir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci nesne. Test kapsamındaki kod tarafından bu nesne oluşturulur.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String)">
            <summary>
            Belirtilen nesnelerin eşit olup olmadığını test eder ve iki nesne eşit değilse
            bir özel durum oluşturur. Mantıksal değerleri eşit olsa bile
            farklı sayısal türler eşit değil olarak kabul edilir. 42L, 42'ye eşit değildir.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci nesne. Testte beklenen nesnedir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci nesne. Test kapsamındaki kod tarafından bu nesne oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşit değil: <paramref name="expected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Belirtilen nesnelerin eşit olup olmadığını test eder ve iki nesne eşit değilse
            bir özel durum oluşturur. Mantıksal değerleri eşit olsa bile
            farklı sayısal türler eşit değil olarak kabul edilir. 42L, 42'ye eşit değildir.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci nesne. Testte beklenen nesnedir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci nesne. Test kapsamındaki kod tarafından bu nesne oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşit değil: <paramref name="expected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object)">
            <summary>
            Belirtilen nesnelerin eşit olup olmadığını test eder ve iki nesne eşitse
            bir özel durum oluşturur. Mantıksal değerleri eşit olsa bile
            farklı sayısal türler eşit değil olarak kabul edilir. 42L, 42'ye eşit değildir.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci nesne. Testte bu değerin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci nesne. Test kapsamındaki kod tarafından bu nesne oluşturulur.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String)">
            <summary>
            Belirtilen nesnelerin eşit olup olmadığını test eder ve iki nesne eşitse
            bir özel durum oluşturur. Mantıksal değerleri eşit olsa bile
            farklı sayısal türler eşit değil olarak kabul edilir. 42L, 42'ye eşit değildir.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci nesne. Testte bu değerin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci nesne. Test kapsamındaki kod tarafından bu nesne oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşittir: <paramref name="notExpected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Belirtilen nesnelerin eşit olup olmadığını test eder ve iki nesne eşitse
            bir özel durum oluşturur. Mantıksal değerleri eşit olsa bile
            farklı sayısal türler eşit değil olarak kabul edilir. 42L, 42'ye eşit değildir.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci nesne. Testte bu değerin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci nesne. Test kapsamındaki kod tarafından bu nesne oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşittir: <paramref name="notExpected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single)">
            <summary>
            Belirtilen float'ların eşit olup olmadığını test eder ve eşit değilse
            bir özel durum oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci kayan nokta. Testte bu kayan nokta beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci kayan nokta. Test kapsamındaki kod tarafından bu nesne oluşturulur.
            </param>
            <param name="delta">
            Gerekli doğruluk. Yalnızca şu durumlarda bir özel durum oluşturulur:
            <paramref name="actual"/> şundan farklı: <paramref name="expected"/>
            şundan fazla: <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Belirtilen float'ların eşit olup olmadığını test eder ve eşit değilse
            bir özel durum oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci kayan nokta. Testte bu kayan nokta beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci kayan nokta. Test kapsamındaki kod tarafından bu nesne oluşturulur.
            </param>
            <param name="delta">
            Gerekli doğruluk. Yalnızca şu durumlarda bir özel durum oluşturulur:
            <paramref name="actual"/> şundan farklı: <paramref name="expected"/>
            şundan fazla: <paramref name="delta"/>.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şundan farklıdır: <paramref name="expected"/> şundan fazla:
            <paramref name="delta"/>. İleti test sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Belirtilen float'ların eşit olup olmadığını test eder ve eşit değilse
            bir özel durum oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci kayan nokta. Testte bu kayan nokta beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci kayan nokta. Test kapsamındaki kod tarafından bu nesne oluşturulur.
            </param>
            <param name="delta">
            Gerekli doğruluk. Yalnızca şu durumlarda bir özel durum oluşturulur:
            <paramref name="actual"/> şundan farklı: <paramref name="expected"/>
            şundan fazla: <paramref name="delta"/>.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şundan farklıdır: <paramref name="expected"/> şundan fazla:
            <paramref name="delta"/>. İleti test sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single)">
            <summary>
            Belirtilen float'ların eşit olup olmadığını test eder ve eşitse
            bir özel durum oluşturur.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak ilk kayan nokta. Testte bu kayan noktanın
            eşleşmemesi beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci kayan nokta. Test kapsamındaki kod tarafından bu nesne oluşturulur.
            </param>
            <param name="delta">
            Gerekli doğruluk. Yalnızca şu durumlarda bir özel durum oluşturulur:
            <paramref name="actual"/> şundan farklı: <paramref name="notExpected"/>
            en fazla <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Belirtilen float'ların eşit olup olmadığını test eder ve eşitse
            bir özel durum oluşturur.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak ilk kayan nokta. Testte bu kayan noktanın
            eşleşmemesi beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci kayan nokta. Test kapsamındaki kod tarafından bu nesne oluşturulur.
            </param>
            <param name="delta">
            Gerekli doğruluk. Yalnızca şu durumlarda bir özel durum oluşturulur:
            <paramref name="actual"/> şundan farklı: <paramref name="notExpected"/>
            en fazla <paramref name="delta"/>.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşittir: <paramref name="notExpected"/> veya şu değerden daha az farklı:
            <paramref name="delta"/>. İleti test sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Belirtilen float'ların eşit olup olmadığını test eder ve eşitse
            bir özel durum oluşturur.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak ilk kayan nokta. Testte bu kayan noktanın
            eşleşmemesi beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci kayan nokta. Test kapsamındaki kod tarafından bu nesne oluşturulur.
            </param>
            <param name="delta">
            Gerekli doğruluk. Yalnızca şu durumlarda bir özel durum oluşturulur:
            <paramref name="actual"/> şundan farklı: <paramref name="notExpected"/>
            en fazla <paramref name="delta"/>.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşittir: <paramref name="notExpected"/> veya şu değerden daha az farklı:
            <paramref name="delta"/>. İleti test sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double)">
            <summary>
            Belirtilen double'ların eşit olup olmadığını test eder ve eşit değilse
            bir özel durum oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci çift. Testte bu çift beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci çift. Test kapsamındaki kod tarafından bu çift oluşturulur.
            </param>
            <param name="delta">
            Gerekli doğruluk. Yalnızca şu durumlarda bir özel durum oluşturulur:
            <paramref name="actual"/> şundan farklı: <paramref name="expected"/>
            şundan fazla: <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Belirtilen double'ların eşit olup olmadığını test eder ve eşit değilse
            bir özel durum oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci çift. Testte bu çift beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci çift. Test kapsamındaki kod tarafından bu çift oluşturulur.
            </param>
            <param name="delta">
            Gerekli doğruluk. Yalnızca şu durumlarda bir özel durum oluşturulur:
            <paramref name="actual"/> şundan farklı: <paramref name="expected"/>
            şundan fazla: <paramref name="delta"/>.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şundan farklıdır: <paramref name="expected"/> şundan fazla:
            <paramref name="delta"/>. İleti test sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Belirtilen double'ların eşit olup olmadığını test eder ve eşit değilse
            bir özel durum oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci çift. Testte bu çift beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci çift. Test kapsamındaki kod tarafından bu çift oluşturulur.
            </param>
            <param name="delta">
            Gerekli doğruluk. Yalnızca şu durumlarda bir özel durum oluşturulur:
            <paramref name="actual"/> şundan farklı: <paramref name="expected"/>
            şundan fazla: <paramref name="delta"/>.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şundan farklıdır: <paramref name="expected"/> şundan fazla:
            <paramref name="delta"/>. İleti test sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double)">
            <summary>
            Belirtilen double'ların eşit olup olmadığını test eder ve eşitse
            bir özel durum oluşturur.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci çift. Testte bu çiftin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci çift. Test kapsamındaki kod tarafından bu çift oluşturulur.
            </param>
            <param name="delta">
            Gerekli doğruluk. Yalnızca şu durumlarda bir özel durum oluşturulur:
            <paramref name="actual"/> şundan farklı: <paramref name="notExpected"/>
            en fazla <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Belirtilen double'ların eşit olup olmadığını test eder ve eşitse
            bir özel durum oluşturur.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci çift. Testte bu çiftin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci çift. Test kapsamındaki kod tarafından bu çift oluşturulur.
            </param>
            <param name="delta">
            Gerekli doğruluk. Yalnızca şu durumlarda bir özel durum oluşturulur:
            <paramref name="actual"/> şundan farklı: <paramref name="notExpected"/>
            en fazla <paramref name="delta"/>.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşittir: <paramref name="notExpected"/> veya şu değerden daha az farklı:
            <paramref name="delta"/>. İleti test sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Belirtilen double'ların eşit olup olmadığını test eder ve eşitse
            bir özel durum oluşturur.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci çift. Testte bu çiftin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci çift. Test kapsamındaki kod tarafından bu çift oluşturulur.
            </param>
            <param name="delta">
            Gerekli doğruluk. Yalnızca şu durumlarda bir özel durum oluşturulur:
            <paramref name="actual"/> şundan farklı: <paramref name="notExpected"/>
            en fazla <paramref name="delta"/>.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşittir: <paramref name="notExpected"/> veya şu değerden daha az farklı:
            <paramref name="delta"/>. İleti test sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean)">
            <summary>
            Belirtilen dizelerin eşit olup olmadığını test eder ve eşit değilse bir
            özel durum oluşturur. Karşılaştırma için sabit kültür kullanılır.
            </summary>
            <param name="expected">
            Karşılaştırılacak ilk dize. Testte bu dize beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci dize. Bu dize test kapsamındaki kod tarafından oluşturulur.
            </param>
            <param name="ignoreCase">
            Büyük/küçük harfe duyarlı veya duyarsız bir karşılaştırmayı gösteren Boole değeri. (true
            değeri büyük/küçük harfe duyarsız bir karşılaştırmayı belirtir.)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Belirtilen dizelerin eşit olup olmadığını test eder ve eşit değilse bir
            özel durum oluşturur. Karşılaştırma için sabit kültür kullanılır.
            </summary>
            <param name="expected">
            Karşılaştırılacak ilk dize. Testte bu dize beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci dize. Bu dize test kapsamındaki kod tarafından oluşturulur.
            </param>
            <param name="ignoreCase">
            Büyük/küçük harfe duyarlı veya duyarsız bir karşılaştırmayı gösteren Boole değeri. (true
            değeri büyük/küçük harfe duyarsız bir karşılaştırmayı belirtir.)
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşit değil: <paramref name="expected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Belirtilen dizelerin eşit olup olmadığını test eder ve eşit değilse bir
            özel durum oluşturur. Karşılaştırma için sabit kültür kullanılır.
            </summary>
            <param name="expected">
            Karşılaştırılacak ilk dize. Testte bu dize beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci dize. Bu dize test kapsamındaki kod tarafından oluşturulur.
            </param>
            <param name="ignoreCase">
            Büyük/küçük harfe duyarlı veya duyarsız bir karşılaştırmayı gösteren Boole değeri. (true
            değeri büyük/küçük harfe duyarsız bir karşılaştırmayı belirtir.)
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşit değil: <paramref name="expected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Belirtilen dizelerin eşit olup olmadığını test eder ve eşit değilse bir
            özel durum oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak ilk dize. Testte bu dize beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci dize. Bu dize test kapsamındaki kod tarafından oluşturulur.
            </param>
            <param name="ignoreCase">
            Büyük/küçük harfe duyarlı veya duyarsız bir karşılaştırmayı gösteren Boole değeri. (true
            değeri büyük/küçük harfe duyarsız bir karşılaştırmayı belirtir.)
            </param>
            <param name="culture">
            Kültüre özel karşılaştırma bilgileri veren bir CultureInfo nesnesi.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Belirtilen dizelerin eşit olup olmadığını test eder ve eşit değilse bir
            özel durum oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak ilk dize. Testte bu dize beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci dize. Bu dize test kapsamındaki kod tarafından oluşturulur.
            </param>
            <param name="ignoreCase">
            Büyük/küçük harfe duyarlı veya duyarsız bir karşılaştırmayı gösteren Boole değeri. (true
            değeri büyük/küçük harfe duyarsız bir karşılaştırmayı belirtir.)
            </param>
            <param name="culture">
            Kültüre özel karşılaştırma bilgileri veren bir CultureInfo nesnesi.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşit değil: <paramref name="expected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Belirtilen dizelerin eşit olup olmadığını test eder ve eşit değilse bir
            özel durum oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak ilk dize. Testte bu dize beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci dize. Bu dize test kapsamındaki kod tarafından oluşturulur.
            </param>
            <param name="ignoreCase">
            Büyük/küçük harfe duyarlı veya duyarsız bir karşılaştırmayı gösteren Boole değeri. (true
            değeri büyük/küçük harfe duyarsız bir karşılaştırmayı belirtir.)
            </param>
            <param name="culture">
            Kültüre özel karşılaştırma bilgileri veren bir CultureInfo nesnesi.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşit değil: <paramref name="expected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean)">
            <summary>
            Belirtilen dizelerin eşit olup olmadığını test eder ve eşitse bir özel durum
            oluşturur. Karşılaştırma için sabit kültür kullanılır.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci dize. Testte bu dizenin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci dize. Bu dize test kapsamındaki kod tarafından oluşturulur.
            </param>
            <param name="ignoreCase">
            Büyük/küçük harfe duyarlı veya duyarsız bir karşılaştırmayı gösteren Boole değeri. (true
            değeri büyük/küçük harfe duyarsız bir karşılaştırmayı belirtir.)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Belirtilen dizelerin eşit olup olmadığını test eder ve eşitse bir özel durum
            oluşturur. Karşılaştırma için sabit kültür kullanılır.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci dize. Testte bu dizenin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci dize. Bu dize test kapsamındaki kod tarafından oluşturulur.
            </param>
            <param name="ignoreCase">
            Büyük/küçük harfe duyarlı veya duyarsız bir karşılaştırmayı gösteren Boole değeri. (true
            değeri büyük/küçük harfe duyarsız bir karşılaştırmayı belirtir.)
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşittir: <paramref name="notExpected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Belirtilen dizelerin eşit olup olmadığını test eder ve eşitse bir özel durum
            oluşturur. Karşılaştırma için sabit kültür kullanılır.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci dize. Testte bu dizenin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci dize. Bu dize test kapsamındaki kod tarafından oluşturulur.
            </param>
            <param name="ignoreCase">
            Büyük/küçük harfe duyarlı veya duyarsız bir karşılaştırmayı gösteren Boole değeri. (true
            değeri büyük/küçük harfe duyarsız bir karşılaştırmayı belirtir.)
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşittir: <paramref name="notExpected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Belirtilen dizelerin eşit olup olmadığını test eder ve eşitse bir özel durum
            oluşturur.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci dize. Testte bu dizenin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci dize. Bu dize test kapsamındaki kod tarafından oluşturulur.
            </param>
            <param name="ignoreCase">
            Büyük/küçük harfe duyarlı veya duyarsız bir karşılaştırmayı gösteren Boole değeri. (true
            değeri büyük/küçük harfe duyarsız bir karşılaştırmayı belirtir.)
            </param>
            <param name="culture">
            Kültüre özel karşılaştırma bilgileri veren bir CultureInfo nesnesi.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Belirtilen dizelerin eşit olup olmadığını test eder ve eşitse bir özel durum
            oluşturur.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci dize. Testte bu dizenin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci dize. Bu dize test kapsamındaki kod tarafından oluşturulur.
            </param>
            <param name="ignoreCase">
            Büyük/küçük harfe duyarlı veya duyarsız bir karşılaştırmayı gösteren Boole değeri. (true
            değeri büyük/küçük harfe duyarsız bir karşılaştırmayı belirtir.)
            </param>
            <param name="culture">
            Kültüre özel karşılaştırma bilgileri veren bir CultureInfo nesnesi.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşittir: <paramref name="notExpected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Belirtilen dizelerin eşit olup olmadığını test eder ve eşitse bir özel durum
            oluşturur.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci dize. Testte bu dizenin eşleşmemesi
            beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci dize. Bu dize test kapsamındaki kod tarafından oluşturulur.
            </param>
            <param name="ignoreCase">
            Büyük/küçük harfe duyarlı veya duyarsız bir karşılaştırmayı gösteren Boole değeri. (true
            değeri büyük/küçük harfe duyarsız bir karşılaştırmayı belirtir.)
            </param>
            <param name="culture">
            Kültüre özel karşılaştırma bilgileri veren bir CultureInfo nesnesi.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşittir: <paramref name="notExpected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type)">
            <summary>
            Belirtilen nesnenin beklenen türde bir örnek olup olmadığını test eder ve 
            beklenen tür, nesnenin devralma hiyerarşisinde değilse 
            bir özel durum oluşturur.
            </summary>
            <param name="value">
            Testte belirtilen türde olması beklenen nesne.
            </param>
            <param name="expectedType">
            Beklenen tür:<paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Belirtilen nesnenin beklenen türde bir örnek olup olmadığını test eder ve 
            beklenen tür, nesnenin devralma hiyerarşisinde değilse 
            bir özel durum oluşturur.
            </summary>
            <param name="value">
            Testte belirtilen türde olması beklenen nesne.
            </param>
            <param name="expectedType">
            Beklenen tür:<paramref name="value"/>.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="value"/>
            şunun bir örneği değil: <paramref name="expectedType"/>. İleti
            test sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Belirtilen nesnenin beklenen türde bir örnek olup olmadığını test eder ve 
            beklenen tür, nesnenin devralma hiyerarşisinde değilse 
            bir özel durum oluşturur.
            </summary>
            <param name="value">
            Testte belirtilen türde olması beklenen nesne.
            </param>
            <param name="expectedType">
            Beklenen tür:<paramref name="value"/>.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="value"/>
            şunun bir örneği değil: <paramref name="expectedType"/>. İleti
            test sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type)">
            <summary>
            Belirtilen nesnenin yanlış türde bir örnek olup olmadığını test eder
            ve belirtilen tür nesnenin devralma hiyerarşisinde ise
            bir özel durum oluşturur.
            </summary>
            <param name="value">
            Testte beklenen türde olmaması beklenen nesne.
            </param>
            <param name="wrongType">
            Tür <paramref name="value"/> olmamalıdır.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Belirtilen nesnenin yanlış türde bir örnek olup olmadığını test eder
            ve belirtilen tür nesnenin devralma hiyerarşisinde ise
            bir özel durum oluşturur.
            </summary>
            <param name="value">
            Testte beklenen türde olmaması beklenen nesne.
            </param>
            <param name="wrongType">
            Tür <paramref name="value"/> olmamalıdır.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="value"/>
            şunun bir örneğidir: <paramref name="wrongType"/>. İleti test
            sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Belirtilen nesnenin yanlış türde bir örnek olup olmadığını test eder
            ve belirtilen tür nesnenin devralma hiyerarşisinde ise
            bir özel durum oluşturur.
            </summary>
            <param name="value">
            Testte beklenen türde olmaması beklenen nesne.
            </param>
            <param name="wrongType">
            Tür <paramref name="value"/> olmamalıdır.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="value"/>
            şunun bir örneğidir: <paramref name="wrongType"/>. İleti test
            sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail">
            <summary>
            Bir AssertFailedException oluşturur.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String)">
            <summary>
            Bir AssertFailedException oluşturur.
            </summary>
            <param name="message">
            Özel duruma eklenecek ileti. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String,System.Object[])">
            <summary>
            Bir AssertFailedException oluşturur.
            </summary>
            <param name="message">
            Özel duruma eklenecek ileti. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive">
            <summary>
            Bir AssertInconclusiveException oluşturur.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String)">
            <summary>
            Bir AssertInconclusiveException oluşturur.
            </summary>
            <param name="message">
            Özel duruma eklenecek ileti. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String,System.Object[])">
            <summary>
            Bir AssertInconclusiveException oluşturur.
            </summary>
            <param name="message">
            Özel duruma eklenecek ileti. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Equals(System.Object,System.Object)">
            <summary>
            Statik eşit aşırı yüklemeler iki türün örneklerini başvuru eşitliği bakımından
            karşılaştırmak için kullanılır. Bu metot iki örneği eşitlik bakımından karşılaştırmak için
            <b>kullanılmamalıdır</b>. Bu nesne <b>her zaman</b> Assert.Fail ile oluşturulur.
            Lütfen birim testlerinizde Assert.AreEqual ve ilişkili aşırı yüklemelerini kullanın.
            </summary>
            <param name="objA"> Nesne A </param>
            <param name="objB"> Nesne B </param>
            <returns> Her zaman false. </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action)">
            <summary>
            <paramref name="action"/> temsilcisi tarafından belirtilen kodun tam olarak belirtilen <typeparamref name="T"/> türündeki (türetilmiş bir türde olmayan) özel durumu
            oluşturup oluşturmadığını test eder ve kod özel durum oluşturmuyorsa veya <typeparamref name="T"/> türünden başka bir türde özel durum oluşturuyorsa
            <code>
            AssertFailedException
            </code>
            oluşturur.
            </summary>
            <param name="action">
            Test edilecek ve özel durum oluşturması beklenen kodun temsilcisi.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Oluşturulması beklenen özel durum türü.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String)">
            <summary>
            <paramref name="action"/> temsilcisi tarafından belirtilen kodun tam olarak belirtilen <typeparamref name="T"/> türündeki (türetilmiş bir türde olmayan) özel durumu
            oluşturup oluşturmadığını test eder ve kod özel durum oluşturmuyorsa veya <typeparamref name="T"/> türünden başka bir türde özel durum oluşturuyorsa
            <code>
            AssertFailedException
            </code>
            oluşturur.
            </summary>
            <param name="action">
            Test edilecek ve özel durum oluşturması beklenen kodun temsilcisi.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="action"/>
            şu türde bir özel durum oluşturmaz: <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Oluşturulması beklenen özel durum türü.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object})">
            <summary>
            <paramref name="action"/> temsilcisi tarafından belirtilen kodun tam olarak belirtilen <typeparamref name="T"/> türündeki (türetilmiş bir türde olmayan) özel durumu
            oluşturup oluşturmadığını test eder ve kod özel durum oluşturmuyorsa veya <typeparamref name="T"/> türünden başka bir türde özel durum oluşturuyorsa
            <code>
            AssertFailedException
            </code>
            oluşturur.
            </summary>
            <param name="action">
            Test edilecek ve özel durum oluşturması beklenen kodun temsilcisi.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Oluşturulması beklenen özel durum türü.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String)">
            <summary>
            <paramref name="action"/> temsilcisi tarafından belirtilen kodun tam olarak belirtilen <typeparamref name="T"/> türündeki (türetilmiş bir türde olmayan) özel durumu
            oluşturup oluşturmadığını test eder ve kod özel durum oluşturmuyorsa veya <typeparamref name="T"/> türünden başka bir türde özel durum oluşturuyorsa
            <code>
            AssertFailedException
            </code>
            oluşturur.
            </summary>
            <param name="action">
            Test edilecek ve özel durum oluşturması beklenen kodun temsilcisi.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="action"/>
            şu türde bir özel durum oluşturmaz: <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Oluşturulması beklenen özel durum türü.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String,System.Object[])">
            <summary>
            <paramref name="action"/> temsilcisi tarafından belirtilen kodun tam olarak belirtilen <typeparamref name="T"/> türündeki (türetilmiş bir türde olmayan) özel durumu
            oluşturup oluşturmadığını test eder ve kod özel durum oluşturmuyorsa veya <typeparamref name="T"/> türünden başka bir türde özel durum oluşturuyorsa
            <code>
            AssertFailedException
            </code>
            oluşturur.
            </summary>
            <param name="action">
            Test edilecek ve özel durum oluşturması beklenen kodun temsilcisi.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="action"/>
            şu türde bir özel durum oluşturmaz: <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throw exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Oluşturulması beklenen özel durum türü.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String,System.Object[])">
            <summary>
            <paramref name="action"/> temsilcisi tarafından belirtilen kodun tam olarak belirtilen <typeparamref name="T"/> türündeki (türetilmiş bir türde olmayan) özel durumu
            oluşturup oluşturmadığını test eder ve kod özel durum oluşturmuyorsa veya <typeparamref name="T"/> türünden başka bir türde özel durum oluşturuyorsa
            <code>
            AssertFailedException
            </code>
            oluşturur.
            </summary>
            <param name="action">
            Test edilecek ve özel durum oluşturması beklenen kodun temsilcisi.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="action"/>
            şu türde bir özel durum oluşturmaz: <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Oluşturulması beklenen özel durum türü.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task})">
            <summary>
            <paramref name="action"/> temsilcisi tarafından belirtilen kodun tam olarak belirtilen <typeparamref name="T"/> türündeki (türetilmiş bir türde olmayan) özel durumu
            oluşturup oluşturmadığını test eder ve kod özel durum oluşturmuyorsa veya <typeparamref name="T"/> türünden başka bir türde özel durum oluşturuyorsa
            <code>
            AssertFailedException
            </code>
            oluşturur.
            </summary>
            <param name="action">
            Test edilecek ve özel durum oluşturması beklenen kodun temsilcisi.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Bir <see cref="T:System.Threading.Tasks.Task"/> temsilciyi çalıştırıyor.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            <paramref name="action"/> temsilcisi tarafından belirtilen kodun tam olarak belirtilen <typeparamref name="T"/> türündeki (türetilmiş bir türde olmayan) özel durumu
            oluşturup oluşturmadığını test eder ve kod özel durum oluşturmuyorsa veya <typeparamref name="T"/> türünden başka bir türde özel durum oluşturuyorsa <code>AssertFailedException</code> oluşturur.
            </summary>
            <param name="action">Test edilecek ve özel durum oluşturması beklenen kodun temsilcisi.</param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="action"/>
            tarafından şu türde özel durum oluşturulmadığı durumlarda oluşturulur: <typeparamref name="T"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Bir <see cref="T:System.Threading.Tasks.Task"/> temsilciyi çalıştırıyor.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String,System.Object[])">
            <summary>
            <paramref name="action"/> temsilcisi tarafından belirtilen kodun tam olarak belirtilen <typeparamref name="T"/> türündeki (türetilmiş bir türde olmayan) özel durumu
            oluşturup oluşturmadığını test eder ve kod özel durum oluşturmuyorsa veya <typeparamref name="T"/> türünden başka bir türde özel durum oluşturuyorsa <code>AssertFailedException</code> oluşturur.
            </summary>
            <param name="action">Test edilecek ve özel durum oluşturması beklenen kodun temsilcisi.</param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="action"/>
            tarafından şu türde özel durum oluşturulmadığı durumlarda oluşturulur: <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Bir <see cref="T:System.Threading.Tasks.Task"/> temsilciyi çalıştırıyor.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNullChars(System.String)">
            <summary>
            Null karakterleri ('\0'), "\\0" ile değiştirir.
            </summary>
            <param name="input">
            Aranacak dize.
            </param>
            <returns>
            Null karakterler içeren dönüştürülmüş dize "\\0" ile değiştirildi.
            </returns>
            <remarks>
            This is only public and still present to preserve compatibility with the V1 framework.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.HandleFail(System.String,System.String,System.Object[])">
            <summary>
            AssertionFailedException oluşturan yardımcı işlev
            </summary>
            <param name="assertionName">
            özel durum oluşturan onaylamanın adı
            </param>
            <param name="message">
            onaylama hatası koşullarını açıklayan ileti
            </param>
            <param name="parameters">
            Parametreler.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.CheckParameterNotNull(System.Object,System.String,System.String,System.String,System.Object[])">
            <summary>
            Parametreyi geçerli koşullar için denetler
            </summary>
            <param name="param">
            Parametre.
            </param>
            <param name="assertionName">
            Onaylama Adı.
            </param>
            <param name="parameterName">
            parametre adı
            </param>
            <param name="message">
            iletisi geçersiz parametre özel durumu içindir
            </param>
            <param name="parameters">
            Parametreler.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNulls(System.Object)">
            <summary>
            Bir nesneyi güvenli bir şekilde dizeye dönüştürür, null değerleri ve null karakterleri işler.
            Null değerler "(null)" değerine dönüştürülür. Null karakterler "\\0" değerine dönüştürülür.
            </summary>
            <param name="input">
            Dizeye dönüştürülecek nesne.
            </param>
            <returns>
            Dönüştürülmüş dize.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert">
            <summary>
            Dize onayı.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.That">
            <summary>
            CollectionAssert işlevselliğinin tekil örneğini alır.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void ContainsWords(this StringAssert cusomtAssert, string value, ICollection substrings)"
            Users could then use a syntax similar to the default assertions which in this case is "StringAssert.That.ContainsWords(value, substrings);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String)">
            <summary>
            Belirtilen dizenin belirtilen alt dizeyi içerip içermediğini test eder
            ve alt dize test dizesinin içinde geçmiyorsa bir özel durum
            oluşturur.
            </summary>
            <param name="value">
            Şunu içermesi beklenen dize <paramref name="substring"/>.
            </param>
            <param name="substring">
            Şunun içinde gerçekleşmesi beklenen dize: <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String)">
            <summary>
            Belirtilen dizenin belirtilen alt dizeyi içerip içermediğini test eder
            ve alt dize test dizesinin içinde geçmiyorsa bir özel durum
            oluşturur.
            </summary>
            <param name="value">
            Şunu içermesi beklenen dize <paramref name="substring"/>.
            </param>
            <param name="substring">
            Şunun içinde gerçekleşmesi beklenen dize: <paramref name="value"/>.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="substring"/>
            şunun içinde değil: <paramref name="value"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String,System.Object[])">
            <summary>
            Belirtilen dizenin belirtilen alt dizeyi içerip içermediğini test eder
            ve alt dize test dizesinin içinde geçmiyorsa bir özel durum
            oluşturur.
            </summary>
            <param name="value">
            Şunu içermesi beklenen dize <paramref name="substring"/>.
            </param>
            <param name="substring">
            Şunun içinde gerçekleşmesi beklenen dize: <paramref name="value"/>.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="substring"/>
            şunun içinde değil: <paramref name="value"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String)">
            <summary>
            Belirtilen dizenin belirtilen alt dizeyle başlayıp başlamadığını test eder
            ve test dizesi alt dizeyle başlamıyorsa bir özel durum
            oluşturur.
            </summary>
            <param name="value">
            Şununla başlaması beklenen dize <paramref name="substring"/>.
            </param>
            <param name="substring">
            Şunun ön eki olması beklenen dize: <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String)">
            <summary>
            Belirtilen dizenin belirtilen alt dizeyle başlayıp başlamadığını test eder
            ve test dizesi alt dizeyle başlamıyorsa bir özel durum
            oluşturur.
            </summary>
            <param name="value">
            Şununla başlaması beklenen dize <paramref name="substring"/>.
            </param>
            <param name="substring">
            Şunun ön eki olması beklenen dize: <paramref name="value"/>.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="value"/>
            şununla başlamıyor: <paramref name="substring"/>. İleti
            test sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Belirtilen dizenin belirtilen alt dizeyle başlayıp başlamadığını test eder
            ve test dizesi alt dizeyle başlamıyorsa bir özel durum
            oluşturur.
            </summary>
            <param name="value">
            Şununla başlaması beklenen dize <paramref name="substring"/>.
            </param>
            <param name="substring">
            Şunun ön eki olması beklenen dize: <paramref name="value"/>.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="value"/>
            şununla başlamıyor: <paramref name="substring"/>. İleti
            test sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String)">
            <summary>
            Belirtilen dizenin belirtilen alt dizeyle bitip bitmediğini test eder
            ve test dizesi alt dizeyle bitmiyorsa bir özel durum
            oluşturur.
            </summary>
            <param name="value">
            Dizenin şununla bitmesi beklenir: <paramref name="substring"/>.
            </param>
            <param name="substring">
            Şunun son eki olması beklenen dize: <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String)">
            <summary>
            Belirtilen dizenin belirtilen alt dizeyle bitip bitmediğini test eder
            ve test dizesi alt dizeyle bitmiyorsa bir özel durum
            oluşturur.
            </summary>
            <param name="value">
            Dizenin şununla bitmesi beklenir: <paramref name="substring"/>.
            </param>
            <param name="substring">
            Şunun son eki olması beklenen dize: <paramref name="value"/>.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="value"/>
            şununla bitmiyor: <paramref name="substring"/>. İleti
            test sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Belirtilen dizenin belirtilen alt dizeyle bitip bitmediğini test eder
            ve test dizesi alt dizeyle bitmiyorsa bir özel durum
            oluşturur.
            </summary>
            <param name="value">
            Dizenin şununla bitmesi beklenir: <paramref name="substring"/>.
            </param>
            <param name="substring">
            Şunun son eki olması beklenen dize: <paramref name="value"/>.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="value"/>
            şununla bitmiyor: <paramref name="substring"/>. İleti
            test sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Belirtilen dizenin bir normal ifadeyle eşleşip eşleşmediğini test eder
            ve dize ifadeyle eşleşmiyorsa bir özel durum oluşturur.
            </summary>
            <param name="value">
            Eşleşmesi beklenen dize <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Normal ifade: <paramref name="value"/> eşleşmesi
            bekleniyor.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Belirtilen dizenin bir normal ifadeyle eşleşip eşleşmediğini test eder
            ve dize ifadeyle eşleşmiyorsa bir özel durum oluşturur.
            </summary>
            <param name="value">
            Eşleşmesi beklenen dize <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Normal ifade: <paramref name="value"/> eşleşmesi
            bekleniyor.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="value"/>
            eşleşmiyor <paramref name="pattern"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Belirtilen dizenin bir normal ifadeyle eşleşip eşleşmediğini test eder
            ve dize ifadeyle eşleşmiyorsa bir özel durum oluşturur.
            </summary>
            <param name="value">
            Eşleşmesi beklenen dize <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Normal ifade: <paramref name="value"/> eşleşmesi
            bekleniyor.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="value"/>
            eşleşmiyor <paramref name="pattern"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Belirtilen dizenin bir normal ifadeyle eşleşip eşleşmediğini test eder
            ve dize ifadeyle eşleşiyorsa bir özel durum oluşturur.
            </summary>
            <param name="value">
            Eşleşmemesi beklenen dize <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Normal ifade: <paramref name="value"/> eşleşmemesi
            bekleniyor.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Belirtilen dizenin bir normal ifadeyle eşleşip eşleşmediğini test eder
            ve dize ifadeyle eşleşiyorsa bir özel durum oluşturur.
            </summary>
            <param name="value">
            Eşleşmemesi beklenen dize <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Normal ifade: <paramref name="value"/> eşleşmemesi
            bekleniyor.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="value"/>
            eşleşme <paramref name="pattern"/>. İleti, test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Belirtilen dizenin bir normal ifadeyle eşleşip eşleşmediğini test eder
            ve dize ifadeyle eşleşiyorsa bir özel durum oluşturur.
            </summary>
            <param name="value">
            Eşleşmemesi beklenen dize <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Normal ifade: <paramref name="value"/> eşleşmemesi
            bekleniyor.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="value"/>
            eşleşme <paramref name="pattern"/>. İleti, test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert">
            <summary>
            Birim testleri içindeki koleksiyonlarla ilişkili çeşitli koşulları test etmeye
            yönelik yardımcı sınıf koleksiyonu. Test edilen koşul karşılanmazsa
            bir özel durum oluşturulur.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.That">
            <summary>
            CollectionAssert işlevselliğinin tekil örneğini alır.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void AreEqualUnordered(this CollectionAssert cusomtAssert, ICollection expected, ICollection actual)"
            Users could then use a syntax similar to the default assertions which in this case is "CollectionAssert.That.AreEqualUnordered(list1, list2);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object)">
            <summary>
            Belirtilen koleksiyonun belirtilen öğeyi içerip içermediğini test eder
            ve öğe koleksiyonda değilse bir özel durum oluşturur.
            </summary>
            <param name="collection">
            Öğenin aranacağı koleksiyon.
            </param>
            <param name="element">
            Koleksiyonda olması beklenen öğe.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Belirtilen koleksiyonun belirtilen öğeyi içerip içermediğini test eder
            ve öğe koleksiyonda değilse bir özel durum oluşturur.
            </summary>
            <param name="collection">
            Öğenin aranacağı koleksiyon.
            </param>
            <param name="element">
            Koleksiyonda olması beklenen öğe.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="element"/>
            şunun içinde değil: <paramref name="collection"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Belirtilen koleksiyonun belirtilen öğeyi içerip içermediğini test eder
            ve öğe koleksiyonda değilse bir özel durum oluşturur.
            </summary>
            <param name="collection">
            Öğenin aranacağı koleksiyon.
            </param>
            <param name="element">
            Koleksiyonda olması beklenen öğe.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="element"/>
            şunun içinde değil: <paramref name="collection"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object)">
            <summary>
            Belirtilen koleksiyonun belirtilen öğeyi içerip içermediğini test eder
            ve öğe koleksiyonda bulunuyorsa bir özel durum oluşturur.
            </summary>
            <param name="collection">
            Öğenin aranacağı koleksiyon.
            </param>
            <param name="element">
            Koleksiyonda olmaması beklenen öğe.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Belirtilen koleksiyonun belirtilen öğeyi içerip içermediğini test eder
            ve öğe koleksiyonda bulunuyorsa bir özel durum oluşturur.
            </summary>
            <param name="collection">
            Öğenin aranacağı koleksiyon.
            </param>
            <param name="element">
            Koleksiyonda olmaması beklenen öğe.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="element"/>
            şunun içindedir: <paramref name="collection"/>. İleti, test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Belirtilen koleksiyonun belirtilen öğeyi içerip içermediğini test eder
            ve öğe koleksiyonda bulunuyorsa bir özel durum oluşturur.
            </summary>
            <param name="collection">
            Öğenin aranacağı koleksiyon.
            </param>
            <param name="element">
            Koleksiyonda olmaması beklenen öğe.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="element"/>
            şunun içindedir: <paramref name="collection"/>. İleti, test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection)">
            <summary>
            Belirtilen koleksiyondaki tüm öğelerin null dışında değere sahip olup
            olmadığını test eder ve herhangi bir öğe null ise özel durum oluşturur.
            </summary>
            <param name="collection">
            İçinde null öğelerin aranacağı koleksiyon.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String)">
            <summary>
            Belirtilen koleksiyondaki tüm öğelerin null dışında değere sahip olup
            olmadığını test eder ve herhangi bir öğe null ise özel durum oluşturur.
            </summary>
            <param name="collection">
            İçinde null öğelerin aranacağı koleksiyon.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="collection"/>
            bir null öğe içeriyor. İleti test sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Belirtilen koleksiyondaki tüm öğelerin null dışında değere sahip olup
            olmadığını test eder ve herhangi bir öğe null ise özel durum oluşturur.
            </summary>
            <param name="collection">
            İçinde null öğelerin aranacağı koleksiyon.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="collection"/>
            bir null öğe içeriyor. İleti test sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection)">
            <summary>
            Belirtilen koleksiyondaki tüm öğelerin benzersiz olup olmadığını test eder
            ve koleksiyondaki herhangi iki öğe eşitse özel durum oluşturur.
            </summary>
            <param name="collection">
            Yinelenen öğelerin aranacağı koleksiyon.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String)">
            <summary>
            Belirtilen koleksiyondaki tüm öğelerin benzersiz olup olmadığını test eder
            ve koleksiyondaki herhangi iki öğe eşitse özel durum oluşturur.
            </summary>
            <param name="collection">
            Yinelenen öğelerin aranacağı koleksiyon.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="collection"/>
            en az bir yinelenen öğe içeriyor. İleti, test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Belirtilen koleksiyondaki tüm öğelerin benzersiz olup olmadığını test eder
            ve koleksiyondaki herhangi iki öğe eşitse özel durum oluşturur.
            </summary>
            <param name="collection">
            Yinelenen öğelerin aranacağı koleksiyon.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="collection"/>
            en az bir yinelenen öğe içeriyor. İleti, test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Bir koleksiyonun başka bir koleksiyona ait alt küme olup olmadığını
            test eder ve alt kümedeki herhangi bir öğe aynı zamanda üst kümede
            yoksa bir özel durum oluşturur.
            </summary>
            <param name="subset">
            Şunun alt kümesi olması beklenen koleksiyon: <paramref name="superset"/>.
            </param>
            <param name="superset">
            Şunun üst kümesi olması beklenen koleksiyon: <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Bir koleksiyonun başka bir koleksiyona ait alt küme olup olmadığını
            test eder ve alt kümedeki herhangi bir öğe aynı zamanda üst kümede
            yoksa bir özel durum oluşturur.
            </summary>
            <param name="subset">
            Şunun alt kümesi olması beklenen koleksiyon: <paramref name="superset"/>.
            </param>
            <param name="superset">
            Şunun üst kümesi olması beklenen koleksiyon: <paramref name="subset"/>
            </param>
            <param name="message">
            İletinin özel duruma dahil edilmesi için şuradaki bir öğe:
            <paramref name="subset"/> şurada bulunmuyor: <paramref name="superset"/>.
            İleti test sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Bir koleksiyonun başka bir koleksiyona ait alt küme olup olmadığını
            test eder ve alt kümedeki herhangi bir öğe aynı zamanda üst kümede
            yoksa bir özel durum oluşturur.
            </summary>
            <param name="subset">
            Şunun alt kümesi olması beklenen koleksiyon: <paramref name="superset"/>.
            </param>
            <param name="superset">
            Şunun üst kümesi olması beklenen koleksiyon: <paramref name="subset"/>
            </param>
            <param name="message">
            İletinin özel duruma dahil edilmesi için şuradaki bir öğe:
            <paramref name="subset"/> şurada bulunmuyor: <paramref name="superset"/>.
            İleti test sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Bir koleksiyonun başka bir koleksiyona ait alt küme olup olmadığını
            test eder ve alt kümedeki tüm öğeler aynı zamanda üst kümede
            bulunuyorsa bir özel durum oluşturur.
            </summary>
            <param name="subset">
            Şunun alt kümesi olmaması beklenen koleksiyon: <paramref name="superset"/>.
            </param>
            <param name="superset">
            Şunun üst kümesi olmaması beklenen koleksiyon: <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Bir koleksiyonun başka bir koleksiyona ait alt küme olup olmadığını
            test eder ve alt kümedeki tüm öğeler aynı zamanda üst kümede
            bulunuyorsa bir özel durum oluşturur.
            </summary>
            <param name="subset">
            Şunun alt kümesi olmaması beklenen koleksiyon: <paramref name="superset"/>.
            </param>
            <param name="superset">
            Şunun üst kümesi olmaması beklenen koleksiyon: <paramref name="subset"/>
            </param>
            <param name="message">
            Mesajın özel duruma dahil edilmesi için şuradaki her öğe:
            <paramref name="subset"/> ayrıca şurada bulunur: <paramref name="superset"/>.
            İleti test sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Bir koleksiyonun başka bir koleksiyona ait alt küme olup olmadığını
            test eder ve alt kümedeki tüm öğeler aynı zamanda üst kümede
            bulunuyorsa bir özel durum oluşturur.
            </summary>
            <param name="subset">
            Şunun alt kümesi olmaması beklenen koleksiyon: <paramref name="superset"/>.
            </param>
            <param name="superset">
            Şunun üst kümesi olmaması beklenen koleksiyon: <paramref name="subset"/>
            </param>
            <param name="message">
            Mesajın özel duruma dahil edilmesi için şuradaki her öğe:
            <paramref name="subset"/> ayrıca şurada bulunur: <paramref name="superset"/>.
            İleti test sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            İki koleksiyonun aynı öğeleri içerip içermediğini test eder ve koleksiyonlardan
            biri diğer koleksiyonda olmayan bir öğeyi içeriyorsa özel durum
            oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci koleksiyon. Testte beklenen öğeleri
            içerir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından
            bu koleksiyon oluşturulur.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            İki koleksiyonun aynı öğeleri içerip içermediğini test eder ve koleksiyonlardan
            biri diğer koleksiyonda olmayan bir öğeyi içeriyorsa özel durum
            oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci koleksiyon. Testte beklenen öğeleri
            içerir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından
            bu koleksiyon oluşturulur.
            </param>
            <param name="message">
            Bir öğe koleksiyonlardan birinde varken diğerinde olmadığında
            özel duruma eklenecek ileti. İleti, test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            İki koleksiyonun aynı öğeleri içerip içermediğini test eder ve koleksiyonlardan
            biri diğer koleksiyonda olmayan bir öğeyi içeriyorsa özel durum
            oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci koleksiyon. Testte beklenen öğeleri
            içerir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından
            bu koleksiyon oluşturulur.
            </param>
            <param name="message">
            Bir öğe koleksiyonlardan birinde varken diğerinde olmadığında
            özel duruma eklenecek ileti. İleti, test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            İki koleksiyonun farklı öğeler içerip içermediğini test eder ve iki koleksiyon
            sıraya bakılmaksızın aynı öğeleri içeriyorsa bir özel durum
            oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci koleksiyon. Testte gerçek koleksiyondan farklı olması beklenen
            öğeleri içerir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından
            bu koleksiyon oluşturulur.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            İki koleksiyonun farklı öğeler içerip içermediğini test eder ve iki koleksiyon
            sıraya bakılmaksızın aynı öğeleri içeriyorsa bir özel durum
            oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci koleksiyon. Testte gerçek koleksiyondan farklı olması beklenen
            öğeleri içerir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından
            bu koleksiyon oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şununla aynı öğeleri içerir: <paramref name="expected"/>. İleti
            test sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            İki koleksiyonun farklı öğeler içerip içermediğini test eder ve iki koleksiyon
            sıraya bakılmaksızın aynı öğeleri içeriyorsa bir özel durum
            oluşturur.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci koleksiyon. Testte gerçek koleksiyondan farklı olması beklenen
            öğeleri içerir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından
            bu koleksiyon oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şununla aynı öğeleri içerir: <paramref name="expected"/>. İleti
            test sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type)">
            <summary>
            Belirtilen koleksiyondaki tüm öğelerin beklenen türde örnekler
            olup olmadığını test eder ve beklenen tür bir veya daha fazla öğenin
            devralma hiyerarşisinde değilse bir özel durum oluşturur.
            </summary>
            <param name="collection">
            Testte belirtilen türde olması beklenen öğeleri içeren
            koleksiyon.
            </param>
            <param name="expectedType">
            Her öğe için beklenen tür <paramref name="collection"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String)">
            <summary>
            Belirtilen koleksiyondaki tüm öğelerin beklenen türde örnekler
            olup olmadığını test eder ve beklenen tür bir veya daha fazla öğenin
            devralma hiyerarşisinde değilse bir özel durum oluşturur.
            </summary>
            <param name="collection">
            Testte belirtilen türde olması beklenen öğeleri içeren
            koleksiyon.
            </param>
            <param name="expectedType">
            Her öğe için beklenen tür <paramref name="collection"/>.
            </param>
            <param name="message">
            İletinin özel duruma dahil edilmesi için şuradaki bir öğe:
            <paramref name="collection"/> şunun bir örneği değil:
            <paramref name="expectedType"/>. İleti test sonuçlarında gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String,System.Object[])">
            <summary>
            Belirtilen koleksiyondaki tüm öğelerin beklenen türde örnekler
            olup olmadığını test eder ve beklenen tür bir veya daha fazla öğenin
            devralma hiyerarşisinde değilse bir özel durum oluşturur.
            </summary>
            <param name="collection">
            Testte belirtilen türde olması beklenen öğeleri içeren
            koleksiyon.
            </param>
            <param name="expectedType">
            Her öğe için beklenen tür <paramref name="collection"/>.
            </param>
            <param name="message">
            İletinin özel duruma dahil edilmesi için şuradaki bir öğe:
            <paramref name="collection"/> şunun bir örneği değil:
            <paramref name="expectedType"/>. İleti test sonuçlarında gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Belirtilen koleksiyonların eşit olup olmadığını test eder ve iki koleksiyon
            eşit değilse bir özel durum oluşturur. Eşitlik aynı öğelere aynı sırayla ve aynı miktarda
            sahip olunması olarak tanımlanır. Aynı değere yönelik farklı başvurular
            eşit olarak kabul edilir.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci koleksiyon. Testte bu koleksiyon beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından bu
             koleksiyon oluşturulur.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Belirtilen koleksiyonların eşit olup olmadığını test eder ve iki koleksiyon
            eşit değilse bir özel durum oluşturur. Eşitlik aynı öğelere aynı sırayla ve aynı miktarda
            sahip olunması olarak tanımlanır. Aynı değere yönelik farklı başvurular
            eşit olarak kabul edilir.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci koleksiyon. Testte bu koleksiyon beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından bu
             koleksiyon oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşit değil: <paramref name="expected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Belirtilen koleksiyonların eşit olup olmadığını test eder ve iki koleksiyon
            eşit değilse bir özel durum oluşturur. Eşitlik aynı öğelere aynı sırayla ve aynı miktarda
            sahip olunması olarak tanımlanır. Aynı değere yönelik farklı başvurular
            eşit olarak kabul edilir.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci koleksiyon. Testte bu koleksiyon beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından bu
             koleksiyon oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşit değil: <paramref name="expected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Belirtilen koleksiyonların eşit olup olmadığını test eder ve iki koleksiyon eşitse
            bir özel durum oluşturur. Eşitlik aynı öğelere aynı sırayla ve
            aynı miktarda sahip olunması olarak tanımlanır. Aynı değere yönelik farklı başvurular
            eşit olarak kabul edilir.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci koleksiyon. Testte bu koleksiyonun
            eşleşmemesi beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından bu
             koleksiyon oluşturulur.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Belirtilen koleksiyonların eşit olup olmadığını test eder ve iki koleksiyon eşitse
            bir özel durum oluşturur. Eşitlik aynı öğelere aynı sırayla ve
            aynı miktarda sahip olunması olarak tanımlanır. Aynı değere yönelik farklı başvurular
            eşit olarak kabul edilir.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci koleksiyon. Testte bu koleksiyonun
            eşleşmemesi beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından bu
             koleksiyon oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşittir: <paramref name="notExpected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Belirtilen koleksiyonların eşit olup olmadığını test eder ve iki koleksiyon eşitse
            bir özel durum oluşturur. Eşitlik aynı öğelere aynı sırayla ve
            aynı miktarda sahip olunması olarak tanımlanır. Aynı değere yönelik farklı başvurular
            eşit olarak kabul edilir.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci koleksiyon. Testte bu koleksiyonun
            eşleşmemesi beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından bu
             koleksiyon oluşturulur.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşittir: <paramref name="notExpected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Belirtilen koleksiyonların eşit olup olmadığını test eder ve iki koleksiyon
            eşit değilse bir özel durum oluşturur. Eşitlik aynı öğelere aynı sırayla ve aynı miktarda
            sahip olunması olarak tanımlanır. Aynı değere yönelik farklı başvurular
            eşit olarak kabul edilir.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci koleksiyon. Testte bu koleksiyon beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından bu
             koleksiyon oluşturulur.
            </param>
            <param name="comparer">
            Koleksiyonun öğeleri karşılaştırılırken kullanılacak karşılaştırma uygulaması.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Belirtilen koleksiyonların eşit olup olmadığını test eder ve iki koleksiyon
            eşit değilse bir özel durum oluşturur. Eşitlik aynı öğelere aynı sırayla ve aynı miktarda
            sahip olunması olarak tanımlanır. Aynı değere yönelik farklı başvurular
            eşit olarak kabul edilir.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci koleksiyon. Testte bu koleksiyon beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından bu
             koleksiyon oluşturulur.
            </param>
            <param name="comparer">
            Koleksiyonun öğeleri karşılaştırılırken kullanılacak karşılaştırma uygulaması.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşit değil: <paramref name="expected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Belirtilen koleksiyonların eşit olup olmadığını test eder ve iki koleksiyon
            eşit değilse bir özel durum oluşturur. Eşitlik aynı öğelere aynı sırayla ve aynı miktarda
            sahip olunması olarak tanımlanır. Aynı değere yönelik farklı başvurular
            eşit olarak kabul edilir.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci koleksiyon. Testte bu koleksiyon beklenir.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından bu
             koleksiyon oluşturulur.
            </param>
            <param name="comparer">
            Koleksiyonun öğeleri karşılaştırılırken kullanılacak karşılaştırma uygulaması.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti <paramref name="actual"/>
            şuna eşit değil: <paramref name="expected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Biçimlendirme sırasında kullanılacak parametre dizisi <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Belirtilen koleksiyonların eşit olup olmadığını test eder ve iki koleksiyon eşitse
            bir özel durum oluşturur. Eşitlik aynı öğelere aynı sırayla ve
            aynı miktarda sahip olunması olarak tanımlanır. Aynı değere yönelik farklı başvurular
            eşit olarak kabul edilir.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci koleksiyon. Testte bu koleksiyonun
            eşleşmemesi beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından bu
             koleksiyon oluşturulur.
            </param>
            <param name="comparer">
            Koleksiyonun öğeleri karşılaştırılırken kullanılacak karşılaştırma uygulaması.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Belirtilen koleksiyonların eşit olup olmadığını test eder ve iki koleksiyon eşitse
            bir özel durum oluşturur. Eşitlik aynı öğelere aynı sırayla ve
            aynı miktarda sahip olunması olarak tanımlanır. Aynı değere yönelik farklı başvurular
            eşit olarak kabul edilir.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci koleksiyon. Testte bu koleksiyonun
            eşleşmemesi beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından bu
             koleksiyon oluşturulur.
            </param>
            <param name="comparer">
            Koleksiyonun öğeleri karşılaştırılırken kullanılacak karşılaştırma uygulaması.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti: <paramref name="actual"/>
            şuna eşittir: <paramref name="notExpected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Belirtilen koleksiyonların eşit olup olmadığını test eder ve iki koleksiyon eşitse
            bir özel durum oluşturur. Eşitlik aynı öğelere aynı sırayla ve
            aynı miktarda sahip olunması olarak tanımlanır. Aynı değere yönelik farklı başvurular
            eşit olarak kabul edilir.
            </summary>
            <param name="notExpected">
            Karşılaştırılacak birinci koleksiyon. Testte bu koleksiyonun
            eşleşmemesi beklenir <paramref name="actual"/>.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon. Test kapsamındaki kod tarafından bu
             koleksiyon oluşturulur.
            </param>
            <param name="comparer">
            Koleksiyonun öğeleri karşılaştırılırken kullanılacak karşılaştırma uygulaması.
            </param>
            <param name="message">
            Şu durumda özel duruma dahil edilecek ileti: <paramref name="actual"/>
            şuna eşittir: <paramref name="notExpected"/>. İleti test sonuçlarında
            gösterilir.
            </param>
            <param name="parameters">
            Şu parametre biçimlendirilirken kullanılacak parametre dizisi: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOfHelper(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Birinci koleksiyonun ikinci koleksiyona ait bir alt küme olup
            olmadığını belirler. Kümelerden biri yinelenen öğeler içeriyorsa,
            öğenin alt kümedeki oluşum sayısı üst kümedeki oluşum sayısına
            eşit veya bu sayıdan daha az olmalıdır.
            </summary>
            <param name="subset">
            Testin içinde bulunmasını beklediği koleksiyon <paramref name="superset"/>.
            </param>
            <param name="superset">
            Testin içermesini beklediği koleksiyon <paramref name="subset"/>.
            </param>
            <returns>
            Şu durumda true: <paramref name="subset"/> şunun bir alt kümesidir:
            <paramref name="superset"/>, aksi takdirde false.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.GetElementCounts(System.Collections.ICollection,System.Int32@)">
            <summary>
            Belirtilen koleksiyondaki her öğenin oluşum sayısını içeren bir
            sözlük oluşturur.
            </summary>
            <param name="collection">
            İşlenecek koleksiyon.
            </param>
            <param name="nullCount">
            Koleksiyondaki null öğe sayısı.
            </param>
            <returns>
            Belirtilen koleksiyondaki her öğenin oluşum sayısını içeren
            bir sözlük.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.FindMismatchedElement(System.Collections.ICollection,System.Collections.ICollection,System.Int32@,System.Int32@,System.Object@)">
            <summary>
            İki koleksiyon arasında eşleşmeyen bir öğe bulur. Eşleşmeyen öğe,
            beklenen koleksiyonda gerçek koleksiyondakinden farklı sayıda görünen
            öğedir. Koleksiyonların,
            aynı sayıda öğeye sahip null olmayan farklı başvurular olduğu
            varsayılır. Bu doğrulama düzeyinden
            çağıran sorumludur. Eşleşmeyen bir öğe yoksa işlev
            false değerini döndürür ve dış parametreler kullanılmamalıdır.
            </summary>
            <param name="expected">
            Karşılaştırılacak birinci koleksiyon.
            </param>
            <param name="actual">
            Karşılaştırılacak ikinci koleksiyon.
            </param>
            <param name="expectedCount">
            Şunun için beklenen oluşma sayısı:
            <paramref name="mismatchedElement"/> veya uyumsuz öğe yoksa
            0.
            </param>
            <param name="actualCount">
            Gerçek oluşma sayısı:
            <paramref name="mismatchedElement"/> veya uyumsuz öğe yoksa
            0.
            </param>
            <param name="mismatchedElement">
            Uyumsuz öğe (null olabilir) veya uyumsuz bir
            öğe yoksa null.
            </param>
            <returns>
            uyumsuz bir öğe bulunduysa true; aksi takdirde false.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.ObjectComparer">
            <summary>
            object.Equals kullanarak nesneleri karşılaştırır
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException">
            <summary>
            Çerçeve Özel Durumları için temel sınıf.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> sınıfının yeni bir örneğini başlatır.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String,System.Exception)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="msg"> İleti. </param>
            <param name="ex"> Özel durum. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="msg"> İleti. </param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages">
            <summary>
              Yerelleştirilmiş dizeleri aramak gibi işlemler için, türü kesin olarak belirtilmiş kaynak sınıfı.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ResourceManager">
            <summary>
              Bu sınıf tarafından kullanılan, önbelleğe alınmış ResourceManager örneğini döndürür.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Culture">
            <summary>
              Türü kesin olarak belirlenmiş bu kaynak sınıfını kullanan
              tüm kaynak aramaları için geçerli iş parçacığının CurrentUICulture özelliğini geçersiz kılar.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AccessStringInvalidSyntax">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Erişim dizesinde geçersiz söz dizimi var.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ActualHasMismatchedElements">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Beklenen koleksiyon {1} &lt;{2}&gt; oluşumu içeriyor. Gerçek koleksiyon {3} oluşum içeriyor. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AllItemsAreUniqueFailMsg">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Yinelenen öğe bulundu:&lt;{1}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualCaseFailMsg">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Beklenen:&lt;{1}&gt;. Gerçek değer için büyük/küçük harf kullanımı farklı:&lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDeltaFailMsg">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Beklenen &lt;{1}&gt; değeri ile gerçek &lt;{2}&gt; değeri arasında en fazla &lt;{3}&gt; fark bekleniyordu. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDifferentTypesFailMsg">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Beklenen:&lt;{1} ({2})&gt;. Gerçek:&lt;{3} ({4})&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualFailMsg">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Beklenen:&lt;{1}&gt;. Gerçek:&lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualDeltaFailMsg">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Beklenen &lt;{1}&gt; değeri ile gerçek &lt;{2}&gt; değeri arasında &lt;{3}&gt; değerinden büyük bir fark bekleniyordu. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualFailMsg">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: &lt;{1}&gt; dışında bir değer bekleniyordu. Gerçek:&lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreSameGivenValues">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Değer türlerini AreSame() metoduna geçirmeyin. Object türüne dönüştürülen değerler hiçbir zaman aynı olmaz. AreEqual(). kullanmayı deneyin {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AssertionFailed">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: {0} başarısız oldu. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AsyncUITestMethodNotSupported">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: UITestMethodAttribute özniteliğine sahip async TestMethod metodu desteklenmiyor. async ifadesini kaldırın ya da TestMethodAttribute özniteliğini kullanın.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsEmpty">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Her iki koleksiyon da boş. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameElements">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Her iki koleksiyon da aynı öğeleri içeriyor.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameReference">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Her iki koleksiyon başvurusu da aynı koleksiyon nesnesini işaret ediyor. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothSameElements">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Her iki koleksiyon da aynı öğeleri içeriyor. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.CollectionEqualReason">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: {0}({1}).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_NullInMessages">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: null.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_ObjectString">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: nesne.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ContainsFail">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: '{0}' dizesi '{1}' dizesini içermiyor. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DataDrivenResultDisplayName">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: {0} ({1}).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DoNotUseAssertEquals">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Assert.Equals, Onaylamalar için kullanılmamalıdır. Lütfen bunun yerine Assert.AreEqual ve aşırı yüklemelerini kullanın.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementNumbersDontMatch">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Koleksiyonlardaki öğe sayıları eşleşmiyor. Beklenen:&lt;{1}&gt;. Gerçek:&lt;{2}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementsAtIndexDontMatch">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: {0} dizinindeki öğe eşleşmiyor.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: {1} dizinindeki öğe beklenen türde değil. Beklenen tür:&lt;{2}&gt;. Gerçek tür:&lt;{3}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch2">
            <summary>
              Şuna benzer bir yerelleştirilmiş dizeyi arar: {1} dizinindeki öğe (null). Beklenen tür:&lt;{2}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EndsWithFail">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: '{0}' dizesi '{1}' dizesiyle bitmiyor. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EqualsTesterInvalidArgs">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Geçersiz bağımsız değişken. EqualsTester null değerler kullanamaz.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ErrorInvalidCast">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: {0} türündeki nesne {1} türüne dönüştürülemiyor.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InternalObjectNotValid">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Başvurulan iç nesne artık geçerli değil.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidParameterToAssert">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: '{0}' parametresi geçersiz. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidPropertyType">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: {0} özelliği {1} türüne sahip; beklenen tür {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsInstanceOfFailMsg">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: {0} Beklenen tür:&lt;{1}&gt;. Gerçek tür:&lt;{2}&gt;.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsMatchFail">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: '{0}' dizesi '{1}' deseniyle eşleşmiyor. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotInstanceOfFailMsg">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Yanlış Tür:&lt;{1}&gt;. Gerçek tür:&lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotMatchFail">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: '{0}' dizesi '{1}' deseniyle eşleşiyor. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoDataRow">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: No DataRowAttribute belirtilmedi. DataTestMethodAttribute ile en az bir DataRowAttribute gereklidir.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoExceptionThrown">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Özel durum oluşturulmadı. {1} özel durumu bekleniyordu. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NullParameterToAssert">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: '{0}' parametresi geçersiz. Değer null olamaz. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NumberOfElementsDiff">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Farklı sayıda öğe.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorConstructorNotFound">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: 
                 Belirtilen imzaya sahip oluşturucu bulunamadı. Özel erişimcinizi yeniden oluşturmanız gerekebilir
                 veya üye özel ve bir temel sınıfta tanımlanmış olabilir. İkinci durum geçerliyse üyeyi
                 tanımlayan türü PrivateObject oluşturucusuna geçirmeniz gerekir.
               .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorMemberNotFound">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: 
                 Belirtilen üye ({0}) bulunamadı. Özel erişimcinizi yeniden oluşturmanız gerekebilir
                 veya üye özel ve bir temel sınıfta tanımlanmış olabilir. İkinci durum geçerliyse üyeyi tanımlayan türü
                 PrivateObject oluşturucusuna geçirmeniz gerekir.
               .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.StartsWithFail">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: '{0}' dizesi '{1}' dizesiyle başlamıyor. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_ExpectedExceptionTypeMustDeriveFromException">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Beklenen özel durum türü System.Exception veya System.Exception'dan türetilmiş bir tür olmalıdır.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_FailedToGetExceptionMessage">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Bir özel durum nedeniyle {0} türündeki özel durum için ileti alınamadı.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoException">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Test metodu beklenen {0} özel durumunu oluşturmadı. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoExceptionDefault">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Test metodu bir özel durum oluşturmadı. Test metodunda tanımlanan {0} özniteliği tarafından bir özel durum bekleniyordu.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongException">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Test metodu {0} özel durumunu oluşturdu, ancak {1} özel durumu bekleniyordu. Özel durum iletisi: {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongExceptionDerivedAllowed">
            <summary>
              Şuna benzer bir yerelleştirilmiş dize arar: Test metodu {0} özel durumunu oluşturdu, ancak {1} özel durumu veya bundan türetilmiş bir tür bekleniyordu. Özel durum iletisi: {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.WrongExceptionThrown">
             <summary>
               Şuna benzer bir yerelleştirilmiş dize arar: {2} özel durumu oluşturuldu, ancak {1} özel durumu bekleniyordu. {0}
            Özel Durum İletisi: {3}
            Yığın İzleme: {4}.
             </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome">
            <summary>
            birim testi sonuçları
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Failed">
            <summary>
            Test yürütüldü ancak sorunlar oluştu.
            Sorunlar özel durumları veya başarısız onaylamaları içerebilir.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Inconclusive">
            <summary>
            Test tamamlandı ancak başarılı olup olmadığı belli değil.
            İptal edilen testler için kullanılabilir.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Passed">
            <summary>
            Test bir sorun olmadan yürütüldü.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.InProgress">
            <summary>
            Test şu anda yürütülüyor.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Error">
            <summary>
            Test yürütülmeye çalışılırken bir sistem hatası oluştu.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Timeout">
            <summary>
            Test zaman aşımına uğradı.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Aborted">
            <summary>
            Test, kullanıcı tarafından iptal edildi.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Unknown">
            <summary>
            Test bilinmeyen bir durumda
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper">
            <summary>
            Birim testi çerçevesi için yardımcı işlevini sağlar
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper.GetExceptionMsg(System.Exception)">
            <summary>
            Yinelemeli olarak tüm iç özel durumların iletileri dahil olmak üzere
            özel durum iletilerini alır
            </summary>
            <param name="ex">Şunun için iletilerin alınacağı özel durum:</param>
            <returns>hata iletisi bilgilerini içeren dize</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout">
            <summary>
            Zaman aşımları için <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> sınıfı ile birlikte kullanılabilen sabit listesi.
            Sabit listesinin türü eşleşmelidir
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout.Infinite">
            <summary>
            Sonsuz.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute">
            <summary>
            Test sınıfı özniteliği.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute.GetTestMethodAttribute(Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute)">
            <summary>
            Bu testi çalıştırmayı sağlayan bir test metodu özniteliği alır.
            </summary>
            <param name="testMethodAttribute">Bu metot üzerinde tanımlanan test metodu özniteliği örneği.</param>
            <returns>The <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/> bu testi çalıştırmak için kullanılabilir.</returns>
            <remarks>Extensions can override this method to customize how all methods in a class are run.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute">
            <summary>
            Test metodu özniteliği.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Bir test metodu yürütür.
            </summary>
            <param name="testMethod">Yürütülecek test metodu.</param>
            <returns>Testin sonuçlarını temsil eden bir TestResult nesneleri dizisi.</returns>
            <remarks>Extensions can override this method to customize running a TestMethod.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestInitializeAttribute">
            <summary>
            Test başlatma özniteliği.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCleanupAttribute">
            <summary>
            Test temizleme özniteliği.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute">
            <summary>
            Ignore özniteliği.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute">
            <summary>
            Test özelliği özniteliği.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.#ctor(System.String,System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="name">
            Ad.
            </param>
            <param name="value">
            Değer.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Name">
            <summary>
            Adı alır.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Value">
            <summary>
            Değeri alır.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute">
            <summary>
            Sınıf başlatma özniteliği.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute">
            <summary>
            Sınıf temizleme özniteliği.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyInitializeAttribute">
            <summary>
            Bütünleştirilmiş kod başlatma özniteliği.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyCleanupAttribute">
            <summary>
            Bütünleştirilmiş kod temizleme özniteliği.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute">
            <summary>
            Test Sahibi
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="owner">
            Sahip.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.Owner">
            <summary>
            Sahibi alır.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute">
            <summary>
            Priority özniteliği; birim testinin önceliğini belirtmek için kullanılır.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.#ctor(System.Int32)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="priority">
            Öncelik.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.Priority">
            <summary>
            Önceliği alır.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute">
            <summary>
            Testin açıklaması
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.#ctor(System.String)">
            <summary>
            Bir testi açıklamak için kullanılan <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="description">Açıklama.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.Description">
            <summary>
            Bir testin açıklamasını alır.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute">
            <summary>
            CSS Proje Yapısı URI'si
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.#ctor(System.String)">
            <summary>
            CSS Proje Yapısı URI'si için <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="cssProjectStructure">CSS Proje Yapısı URI'si.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.CssProjectStructure">
            <summary>
            CSS Proje Yapısı URI'sini alır.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute">
            <summary>
            CSS Yineleme URI'si
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.#ctor(System.String)">
            <summary>
            CSS Yineleme URI'si için <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="cssIteration">CSS Yineleme URI'si.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.CssIteration">
            <summary>
            CSS Yineleme URI'sini alır.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute">
            <summary>
            WorkItem özniteliği; bu testle ilişkili bir çalışma öğesini belirtmek için kullanılır.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.#ctor(System.Int32)">
            <summary>
            WorkItem Özniteliği için <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="id">Bir iş öğesinin kimliği.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.Id">
            <summary>
            İlişkili bir iş öğesinin kimliğini alır.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute">
            <summary>
            Timeout özniteliği; bir birim testinin zaman aşımını belirtmek için kullanılır.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(System.Int32)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> sınıfının yeni bir örneğini başlatır.
            </summary>
            <param name="timeout">
            Zaman aşımı.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> sınıfının önceden ayarlanmış bir zaman aşımı ile yeni bir örneğini başlatır
            </summary>
            <param name="timeout">
            Zaman aşımı
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.Timeout">
            <summary>
            Zaman aşımını alır.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult">
            <summary>
            Bağdaştırıcıya döndürülecek TestResult nesnesi.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/> sınıfının yeni bir örneğini başlatır.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DisplayName">
            <summary>
            Sonucun görünen adını alır veya ayarlar. Birden fazla sonuç döndürürken yararlıdır.
            Null ise Metot adı DisplayName olarak kullanılır.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Outcome">
            <summary>
            Test yürütmesinin sonucunu alır veya ayarlar.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestFailureException">
            <summary>
            Test başarısız olduğunda oluşturulan özel durumu alır veya ayarlar.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogOutput">
            <summary>
            Test kodu tarafından günlüğe kaydedilen iletinin çıktısını alır veya ayarlar.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogError">
            <summary>
            Test kodu tarafından günlüğe kaydedilen iletinin çıktısını alır veya ayarlar.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DebugTrace">
            <summary>
            Test koduna göre hata ayıklama izlemelerini alır veya ayarlar.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestContextMessages">
            <summary>
            Gets or sets the debug traces by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Duration">
            <summary>
            Test yürütme süresini alır veya ayarlar.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DatarowIndex">
            <summary>
            Veri kaynağındaki veri satırı dizinini alır veya ayarlar. Yalnızca, veri tabanlı bir testin tek bir veri satırının
            çalıştırılmasına ait sonuçlar için ayarlayın.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ReturnValue">
            <summary>
            Test metodunun dönüş değerini alır veya ayarlar. (Şu anda her zaman null).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ResultFiles">
            <summary>
            Test tarafından eklenen sonuç dosyalarını alır veya ayarlar.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute">
            <summary>
            Veri tabanlı test için bağlantı dizesini, tablo adını ve satır erişim metodunu belirtir.
            </summary>
            <example>
            [DataSource("Provider=SQLOLEDB.1;Data Source=source;Integrated Security=SSPI;Initial Catalog=EqtCoverage;Persist Security Info=False", "MyTable")]
            [DataSource("dataSourceNameFromConfigFile")]
            </example>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultProviderName">
            <summary>
            DataSource için varsayılan sağlayıcı adı.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultDataAccessMethod">
            <summary>
            Varsayılan veri erişimi metodu.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String,System.String,Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> sınıfının yeni bir örneğini başlatır. Bu örnek bir veri sağlayıcısı, bağlantı dizesi, veri tablosu ve veri kaynağına erişmek için kullanılan veri erişimi metodu ile başlatılır.
            </summary>
            <param name="providerInvariantName">System.Data.SqlClient gibi değişmez veri sağlayıcısı adı</param>
            <param name="connectionString">
            Veri sağlayıcısına özgü bağlantı dizesi.
            UYARI: Bağlantı dizesi, hassas veriler (parola gibi) içerebilir.
            Bağlantı dizesi, kaynak kodunda ve derlenmiş bütünleştirilmiş kodda düz metin olarak depolanır. 
            Bu hassas bilgileri korumak için kaynak koda ve bütünleştirilmiş koda erişimi kısıtlayın.
            </param>
            <param name="tableName">Veri tablosunun adı.</param>
            <param name="dataAccessMethod">Verilere erişme sırasını belirtir.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> sınıfının yeni bir örneğini başlatır. Bu örnek bir bağlantı dizesi ve tablo adı ile başlatılır.
            OLEDB veri kaynağına erişmek için kullanılan bağlantı dizesini ve veri tablosunu belirtin.
            </summary>
            <param name="connectionString">
            Veri sağlayıcısına özgü bağlantı dizesi.
            UYARI: Bağlantı dizesi, hassas veriler (parola gibi) içerebilir.
            Bağlantı dizesi, kaynak kodunda ve derlenmiş bütünleştirilmiş kodda düz metin olarak depolanır. 
            Bu hassas bilgileri korumak için kaynak koda ve bütünleştirilmiş koda erişimi kısıtlayın.
            </param>
            <param name="tableName">Veri tablosunun adı.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> sınıfının yeni bir örneğini başlatır. Bu örnek bir veri sağlayıcısı ile ve ayar adıyla ilişkili bir bağlantı dizesi ile başlatılır.
            </summary>
            <param name="dataSourceSettingName">App.config dosyasındaki &lt;microsoft.visualstudio.qualitytools&gt; bölümünde bulunan veri kaynağının adı.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ProviderInvariantName">
            <summary>
            Veri kaynağının veri sağlayıcısını temsil eden bir değer alır.
            </summary>
            <returns>
            Veri sağlayıcısı adı. Nesne başlatılırken bir veri sağlayıcısı belirtilmemişse varsayılan System.Data.OleDb sağlayıcısı döndürülür.
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ConnectionString">
            <summary>
            Veri kaynağının bağlantı dizesini temsil eden bir değer alır.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.TableName">
            <summary>
            Verileri sağlayan tablo adını belirten bir değer alır.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataAccessMethod">
             <summary>
             Veri kaynağına erişmek için kullanılan metodu alır.
             </summary>
            
             <returns>
             Bir <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod"/> değerlerdir. Eğer <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> başlatılmazsa, varsayılan değeri döndürür <see cref="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random"/>.
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataSourceSettingName">
            <summary>
            App.config dosyasındaki &lt;microsoft.visualstudio.qualitytools&gt; bölümünde bulunan bir veri kaynağının adını alır.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute">
            <summary>
            Verilerin satır içi belirtilebileceği veri tabanlı testin özniteliği.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Tüm veri satırlarını bulur ve yürütür.
            </summary>
            <param name="testMethod">
            Test Yöntemi.
            </param>
            <returns>
            Bir <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.RunDataDrivenTest(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod,Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute[])">
            <summary>
            Veri tabanlı test metodunu çalıştırır.
            </summary>
            <param name="testMethod"> Yürütülecek test yöntemi. </param>
            <param name="dataRows"> Veri Satırı. </param>
            <returns> Yürütme sonuçları. </returns>
        </member>
    </members>
</doc>
