﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using Saas.Pos.Domain.IService;
using Saas.Pos.Service;
using Saas.Pos.Domain.IService.Pos;
using Saas.Pos.Service.Pos;
using Saas.Pos.Model.DbFood.Context;

namespace Saas.Pos.UnitTest
{
    [TestClass]
    public class UnitTest1
    {
        [TestMethod]
        public void TestMethod1()
        {
            IPos pos = new PosService();
            var resp = pos.ExprotBusinessData(new DbFoodBusinessReportContext()
            {
                StartDate = DateTime.Parse("2024-11-01 00:00:00"),
                EndDate = DateTime.Parse("2024-11-03 00:00:00"),
                BookUserId = "9900340",
                BookUserName = "嘻嘻嘻",
                ShopId = 3
            });
        }

        [TestMethod]
        public void TestMethod2() 
        {
            IPos pos = new PosService();
            var resp = pos.GetBusinessData(new DbFoodBusinessReportContext()
            {
                StartDate = DateTime.Parse("2024-11-01 00:00:00"),
                EndDate = DateTime.Parse("2024-11-03 00:00:00"),
                BookUserId = "9900340",
                BookUserName = "嘻嘻嘻",
                ShopId = 3
            });
        }
    }
}
