﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService
{
    [ServiceContract]
    public interface IShopGoodService
    {
        [OperationContract]
        ResponseContext<ReturnInt> SaveShopGoods(SaveShopGoodsContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetShopGoodModel>> GetShopGoodList(GetShopGoodsListContext context);

        [OperationContract]
        ResponseContext<GetShopGoodInfoModel> GetShopGoodInfo(GetShopGoodsInfoContext context);

        [OperationContract]
        ResponseContext<int> ChangeGoodStatus(ChangeStatusContext context);

        [OperationContract]
        ResponseContext<int> DeleteShopGood(DeleteShopGoodContext context);

        [OperationContract]
        ResponseContext<int> DeleteShopSku(DeleteShopGoodSkuContext context);

        [OperationContract]
        ResponseContext<ReturnInt> SaveShopSkuPayMethod(SaveSkuPayMethodContext context);

        [OperationContract]
        ResponseContext<ReturnInt> ChangeMethodStatus(ChangeStatusContext context);

        [OperationContract]
        ResponseContext<ReturnInt> DeleteShopPayMethod(DeleteShopPayMethodContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<Shop_GoodsPayMethodExModel>> GetShopSkuPayMethodList(GetShopPayMethodsListContext context);

        [OperationContract]
        ResponseContext<Shop_GoodsPayMethod> GetShopSkuPayMethodInfo(GetShopPayMethodsInfoContext context);

        [OperationContract]
        ResponseContext<ReturnInt> SaveShopModeLink(SaveShopModeLinkContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetModeLinkDataModel>> GetModeLinkData(GetModeLinkDataContext context);

        [OperationContract]
        ResponseContext<ReturnInt> DeleteShopModeLink(DeleteShopModeLinkContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetSkuDataModel>> GetSkuData(GetSkuDataContext context);
    }
}
