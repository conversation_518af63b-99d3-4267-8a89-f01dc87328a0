﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.General;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IDrive.OrderManage
{
    public interface ICommissionPermitSchemesConfig
    {
        #region 方案商品配置关联表操作
        ResponseContext<ReturnBool> SavePermitSchemes(SavePermitSchemesContext context);

        ResponseContext<ReturnBool> DeletePermitSchemes(DeletePermitSchemesContext context);

        ResponseContext<RespPaginationModel<GetPermitSchemesConfigsModel>> GetPermitSchemesConfigs(GetPermitSchemesConfigsContext context);
        #endregion

        #region 提成商品方案表操作
        ResponseContext<ReturnBool> SavePermitInfo(SavePermitInfoContext context);

        ResponseContext<ReturnBool> DeletePermitInfo(DeletePermitInfoContext context);

        ResponseContext<List<KeyValueModel>> GetPermitInfoDrop();

        ResponseContext<RespPaginationModel<Commission_PermitInfo>> GetPermitInfoList(GetPermitInfoListContext context);
        #endregion

        #region 提成商品关联表操作
        ResponseContext<ReturnBool> SavePermitItem(SavePermitItemContext context);

        ResponseContext<RespPaginationModel<GetPermitItemModel>> GetPermitItem(GetPermitItemContext context);
        #endregion

        /// <summary>
        /// 查询在线预订所有商品信息
        /// </summary>
        /// <returns></returns>
        ResponseContext<RespPaginationModel<GetPermitDataModel>> GetPermitData(GetPermitDataContext context);
    }
}
