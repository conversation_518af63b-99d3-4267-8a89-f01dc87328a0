﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.General;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.SaasPos
{
    [ServiceContract]
    public interface IOrderManageService
    {
        [OperationContract]
        ResponseContext<CreateOrderExModel> JudgePay(CreateOrderDataContext context);

        [OperationContract]
        ResponseContext<ReturnBool> PayOrderData(PayOrderDataContext context);

        [OperationContract]
        ResponseContext<CreateOrderExModel> InvokePayOrder(CreateOrderInfoExContext context);

        [OperationContract]
        ResponseContext<OrderRefundModel> CreateOrderRefundData(CreateOrderRefundContext context);

        [OperationContract]
        ResponseContext<bool> VerifyOrderRefundCash(VerifyOrderRefundCashContext context);

        [OperationContract]
        ResponseContext<ReturnInt> CallbackOrderRefund(CallbackOrderRefund context);

        [OperationContract]
        ResponseContext<GetOrderDetailExModel> GetOrderDetail(GetOrderDetailDataExContext context);

        [OperationContract]
        ResponseContext<ReturnBool> UserCancelPayment(UserCancalOrderContext context);

        [OperationContract]
        ResponseContext<ReturnInt> MerchantVerifyOrder(MerchantVerifyOrderContext context);

        [OperationContract]
        ResponseContext<GetOrderDetailsModel> GetOrderDetails(QueryOrderPlaceContext context);

        [OperationContract]
        ResponseContext<GetUserOrderDataModel> GetUserOrderData(GetUserOrderDataContext context);

        [OperationContract]
        ResponseContext<UserOrderReturnModel> GetShopMakeData(GetShopMakeDataExContext context);

        [OperationContract]
        ResponseContext<List<GetMakeDataByDay>> GetShopMakeExData(GetShopMakeDataContextByDay context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetMakeFileDataModel>> GetMakeFileData(GetMakeFileDataContext context);

        [OperationContract]
        ResponseContext<EditFileNumberModel> EditFileNumber(EditFileNumberContext context);

        [OperationContract]
        ResponseContext<GetFileDataModel> GetFileData(EditFileNumberContext context);

        [OperationContract]
        ResponseContext<GetOrderReturnByIdModel> GetOrderReturnById(GetOrderReturnByIdContext context);

        [OperationContract]
        ResponseContext<UserOrderReturnModel> UserOrderReturn(UserOrderReturnContext context);

        [OperationContract]
        ResponseContext<List<GetShopBookCacheOrderDetailModel>> GetItemDatas(GetOrderDetailDataExContext context);

        [OperationContract]
        ResponseContext<List<GetOrderDataStatisModel>> GetOrderDataStatis(GetOrderDataStatisContext context);

        [OperationContract]
        ResponseContext<UserOrderReturnModel> ExportOrderDataStatis(GetOrderDataStatisExContext context);


        [OperationContract]
        ResponseContext<ReturnBool> SaveSchemes(SaveCommissionSchemesContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetCommissionSchemesModel>> GetSchemesList(GetSchemesListContext context);

        [OperationContract]
        ResponseContext<ReturnBool> SaveOrderSchemes(SaveOrderSchemesContext context);

        [OperationContract]
        ResponseContext<GetPersonCommissionPosterModel> GetPersonCommissionPoster(GetPersonCommissionPosterContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetOrderEmpRecordModel>> GetOrderSchemesList(GetOrderSchemesContext context);

        [OperationContract]
        ResponseContext<ExcelEmpRecordModel> ExcelEmpRecord(ExcelEmpRecordContext context);

        [OperationContract]
        ResponseContext<ReturnBool> SavePermitSchemes(SavePermitSchemesContext context);

        [OperationContract]
        ResponseContext<ReturnBool> DeletePermitSchemes(DeletePermitSchemesContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetPermitSchemesConfigsModel>> GetPermitSchemesConfigs(GetPermitSchemesConfigsContext context);

        [OperationContract]
        ResponseContext<ReturnBool> SavePermitInfo(SavePermitInfoContext context);

        [OperationContract]
        ResponseContext<ReturnBool> DeletePermitInfo(DeletePermitInfoContext context);

        [OperationContract]
        ResponseContext<List<KeyValueModel>> GetPermitInfoDrop();

        [OperationContract]
        ResponseContext<RespPaginationModel<Commission_PermitInfo>> GetPermitInfoList(GetPermitInfoListContext context);

        [OperationContract]
        ResponseContext<ReturnBool> SavePermitItem(SavePermitItemContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetPermitItemModel>> GetPermitItem(GetPermitItemContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetPermitDataModel>> GetPermitData(GetPermitDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetTradeOrderListModel>> GetTradeOrderList(GetTradeOrderListContext context);

        [OperationContract]
        ResponseContext<GetPersonCommissionPosterModel> GetAnnualPassImage(GetPersonCommissionPosterContext context);
    }
}
