﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Application.Lib.SaasPos;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using Saas.Pos.RepositoryFactory.T4.SaasPos;

namespace Saas.Pos.Application.Lib.SaasPos
{
    public partial class Coupon_InfoApp : AppBase<Coupon_Info>
    {
        public List<GetCouponInfoDataModel> GetUserCouponData(GetCouponExDataContext context)
        {
            return Repository.Coupon_Info.GetUserCouponData(context);
        }

        /// <summary>
        /// 获取当前可使用优惠卷信息按最优往下排序
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetCouponExDataModel> CouponVerifyPermit(CouponVerifyContext context)
        {
            return Repository.Coupon_Info.CouponVerifyPermit(context);
        }

        /// <summary>
        /// 获取当前可使用优惠卷信息按最优往下排序(只获取一张最优)
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public GetCouponExDataModel CouponVerifyPermitBest(CouponVerifyContext context)
        {
            return Repository.Coupon_Info.CouponVerifyPermitBest(context);
        }

        /// <summary>
        /// 获取当前可使用优惠卷信息按最优往下排序Ex
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetCouponExDataModel> CouponVerifyPermitEx(CouponVerifyExContext context)
        {
            return Repository.Coupon_Info.CouponVerifyPermitEx(context);
        }

        /// <summary>
        /// 优惠卷查询要撤销的优惠卷
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<User_Coupon> CouponDataCancel(CouponDataCancelContext context)
        {
            return Repository.Coupon_Info.CouponDataCancel(context);
        }

        /// <summary>
        /// 管理平台查询核销记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetUseRecordDataModel> GetMerchantOrderList(GetUseRecordDataContext context)
        {
            return Repository.Coupon_Info.GetMerchantOrderList(context);
        }

        /// <summary>
        /// 根据用户ID判断用户是否拥有某张卷
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool GetUseCountByBool(UseCountByBoolContext context)
        {
            return Repository.Coupon_Info.GetUseCountByBool(context);
        }

        /// <summary>
        /// 查询优惠卷信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<CouponData> GetCouponData(GetCouponDataExContext context)
        {
            return Repository.Coupon_Info.GetCouponData(context);
        }

        /// <summary>
        /// 查询用户领取过的所有优惠卷
        /// </summary>
        /// <param name="OpenId"></param>
        /// <returns></returns>
        public List<User_Coupon> GetOpenClaimNum(string OpenId)
        {
            return Repository.Coupon_Info.GetOpenClaimNum(OpenId);
        }

        /// <summary>
        /// 查询优惠卷规则信息
        /// </summary>
        /// <param name="middleIds"></param>
        /// <returns></returns>
        public List<Coupon_Rule> GetCouRuleData(List<string> middleIds)
        {
            return Repository.Coupon_Info.GetCouRuleData(middleIds);
        }

        public GetCoupon_CampaignModel GetCouponCampaign(GetCouponCampaignContext context)
        {
            return Repository.Coupon_Info.GetCouponCampaign(context);
        }

        public GetCoupon_CampaignCountModel GetCoupon_CampaignCount(string CamId, string OpenId)
        {
            return Repository.Coupon_Info.GetCoupon_CampaignCount(CamId, OpenId);
        }

        public List<GetCoupon_InfoCountModel> GetCoupon_InfoCount(List<string> MiddleIds, string OpenId)
        {
            return Repository.Coupon_Info.GetCoupon_InfoCount(MiddleIds, OpenId);
        }

        /// <summary>
        /// 管理平台查询优惠卷派发记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetDistributeRecordDataModel> GetDistributeRecordData(GetDistributeRecordDataContext context)
        {
            return Repository.Coupon_Info.GetDistributeRecordData(context);
        }

        /// <summary>
        /// 查询优惠卷信息
        /// </summary>
        /// <returns></returns>
        public List<GetMiddleDataModel> GetMiddleData(GetMiddleDataContext context)
        {
            return Repository.Coupon_Info.GetMiddleData(context);
        }

        /// <summary>
        /// 查询优惠卷关联表及规则信息
        /// </summary>
        /// <param name="MiddleIds"></param>
        /// <returns></returns>
        public List<GetMiddleRuleDataModel> GetMiddleRuleData(List<string> MiddleIds)
        {
            return Repository.Coupon_Info.GetMiddleRuleData(MiddleIds);
        }
    }
}
