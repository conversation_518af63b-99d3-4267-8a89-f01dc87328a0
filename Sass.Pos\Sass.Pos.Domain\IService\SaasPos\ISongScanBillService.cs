﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SongBase.Context;
using Saas.Pos.Model.SongBase.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IService.SaasPos
{
    [ServiceContract]
    public interface ISongScanBillService
    {
        [OperationContract]
        ResponseContext<RespPaginationModel<GetSongScanBillModel>> GetSongScanBill(GetSongScanBillContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetSongScanModel>> GetSongScan(GetSongScanContext context);
    }
}
