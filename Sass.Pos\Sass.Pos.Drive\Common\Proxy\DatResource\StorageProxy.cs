﻿
using Saas.Pos.Common.Log;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Proxy.DatResource
{
    /// <summary>
    /// 仓储代理(持有者)
    /// </summary>
    public class StorageProxy : DatProxy
    {
        public BookingDatProxy BookingDat;
        public DbResourceProxy DbDat;


        /// <summary>
        /// 初始化构造函数
        /// </summary>
        public StorageProxy()
        {
            SetProxy(DbDat = new DbResourceProxy());
            SetProxy(BookingDat = new BookingDatProxy());
        }


        /// <summary>
        /// 仓储代理初始化
        /// </summary>
        public override void Init()
        {
            try
            {
                if (DatProxyList == null) LogHelper.Info("StorageProxy初始化失败!");
                //批量初始化操作
                DatProxyList.ForEach(item =>
                {
                    item.SetStorage(this);
                    item.Init();
                });
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        public override void Refresh()
        {
            try
            {
                if (DatProxyList == null) LogHelper.Info("StorageProxy刷新失败!");
                //批量初始化操作
                DatProxyList.ForEach(item =>
                {
                    item.Refresh();
                });
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public override void Dispose()
        {
            try
            {
                if (DatProxyList == null) LogHelper.Info("StorageProxy初始化失败!");
                //批量初始化操作
                DatProxyList.ForEach(item =>
                {
                    item.Dispose();
                });
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public override void RunningEvent()
        {
            //throw new NotImplementedException();
        }
    }
}
