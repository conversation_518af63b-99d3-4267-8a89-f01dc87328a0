<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute">
            <summary>
            Используется для указания элемента развертывания (файл или каталог) для развертывания каждого теста.
            Может указываться для тестового класса или метода теста.
            Чтобы указать несколько элементов, можно использовать несколько экземпляров атрибута.
            Путь к элементу может быть абсолютным или относительным, в последнем случае он указывается по отношению к RunConfig.RelativePathRoot.
            </summary>
            <example>
            [DeploymentItem("file1.xml")]
            [DeploymentItem("file2.xml", "DataFiles")]
            [DeploymentItem("bin\Debug")]
            </example>
            <remarks>
            Putting this in here so that UWP discovery works. We still do not want users to be using DeploymentItem in the UWP world - Hence making it internal.
            We should separate out DeploymentItem logic in the adapter via a Framework extensiblity point.
            Filed https://github.com/Microsoft/testfx/issues/100 to track this.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/>.
            </summary>
            <param name="path">Файл или каталог для развертывания. Этот путь задается относительно выходного каталога сборки. Элемент будет скопирован в тот же каталог, что и развернутые сборки теста.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String,System.String)">
            <summary>
            Инициализирует новый экземпляр класса <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/> 
            </summary>
            <param name="path">Относительный или абсолютный путь к файлу или каталогу для развертывания. Этот путь задается относительно выходного каталога сборки. Элемент будет скопирован в тот же каталог, что и развернутые сборки теста.</param>
            <param name="outputDirectory">Путь к каталогу, в который должны быть скопированы элементы. Он может быть абсолютным или относительным (по отношению к каталогу развертывания). Все файлы и каталоги, обозначенные при помощи <paramref name="path"/> будет скопировано в этот каталог.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.Path">
            <summary>
            Получает путь к копируемым исходному файлу или папке.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.OutputDirectory">
            <summary>
            Получает путь к каталогу, в который копируется элемент.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AppContainer.UITestMethodAttribute">
            <summary>
            Выполнение кода теста в потоке пользовательского интерфейса для приложений Магазина Windows.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AppContainer.UITestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Выполнение метода теста для потока пользовательского интерфейса.
            </summary>
            <param name="testMethod">
            Метод теста.
            </param>
            <returns>
            Массив <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>экземпляры.
            </returns>
            Throws <exception cref="T:System.NotSupportedException"> when run on an async test method.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext">
            <summary>
            Класс TestContext. Этот класс должен быть полностью абстрактным и не должен содержать ни одного элемента.
            Элементы будут реализованы в адаптере. Пользователи платформы должны обращаться к этому классу
            только при помощи четко определенного интерфейса.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.Properties">
            <summary>
            Получает свойства теста.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.FullyQualifiedTestClassName">
            <summary>
            Получает полное имя класса, содержащего метод теста, который выполняется в данный момент
            </summary>
            <remarks>
            This property can be useful in attributes derived from ExpectedExceptionBaseAttribute.
            Those attributes have access to the test context, and provide messages that are included
            in the test results. Users can benefit from messages that include the fully-qualified
            class name in addition to the name of the test method currently being executed.
            </remarks>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestName">
            <summary>
            Получает имя метода теста, выполняемого в данный момент
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.CurrentTestOutcome">
            <summary>
            Получает текущий результат теста.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String)">
            <summary>
            Used to write trace messages while the test is running
            </summary>
            <param name="message">formatted message string</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String,System.Object[])">
            <summary>
            Used to write trace messages while the test is running
            </summary>
            <param name="format">format string</param>
            <param name="args">the arguments</param>
        </member>
    </members>
</doc>
