﻿using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
    public partial class wxPayInfoApp : AppBase<wxPayInfo>
    {
        public bool AddwxPayInfo(Bill_AddWxPayInfoContext context) 
        {
            return Repository.wxPayInfo.AddwxPayInfo(context);
        }
    }
}
