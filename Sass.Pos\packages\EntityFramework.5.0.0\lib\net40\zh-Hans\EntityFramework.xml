﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>EntityFramework</name>
  </assembly>
  <members>
    <member name="T:System.Data.Entity.CreateDatabaseIfNotExists`1">
      <summary>
        <see cref="T:System.Data.Entity.IDatabaseInitializer`1" /> 的实现，它仅在数据库不存在时重新创建数据库并选择用数据重新设置数据库的种子。若要设置数据库的种子，请创建一个派生类并重写 Seed 方法。</summary>
      <typeparam name="TContext">上下文的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.CreateDatabaseIfNotExists`1.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.CreateDatabaseIfNotExists`1" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.CreateDatabaseIfNotExists`1.InitializeDatabase(`0)">
      <summary>执行策略以初始化给定上下文的数据库。</summary>
      <param name="context">上下文。</param>
    </member>
    <member name="M:System.Data.Entity.CreateDatabaseIfNotExists`1.Seed(`0)">
      <summary>在重写时，将向上下文中添加数据以设置种子。默认实现不执行任何操作。</summary>
      <param name="context">要设置种子的上下文。</param>
    </member>
    <member name="T:System.Data.Entity.Database">
      <summary>从 <see cref="T:System.Data.Entity.DbContext" /> 对象获取此类的实例，并且可使用该实例管理支持 <see cref="T:System.Data.Entity.DbContext" /> 或连接的实际数据库。这包括对数据库执行创建、删除和存在性检查操作。通过使用此类的静态方法，您只需使用一个连接（而不是完整上下文）即可对数据库执行删除和存在性检查。</summary>
    </member>
    <member name="M:System.Data.Entity.Database.CompatibleWithModel(System.Boolean)">
      <summary>如果上下文和数据库各包含一个模型哈希且这两个哈希匹配，则此方法将返回 true。这指示用于创建数据库的模型与当前模型相同，因此可将这两个模型一起使用。</summary>
      <returns>如果上下文中的模型哈希与数据库中的模型哈希匹配，则为 true；否则为 false。</returns>
      <param name="throwIfNoMetadata">如果设置为 true，则当未在与上下文关联的模型中或数据库中找到任何模型元数据时，将引发异常。如果设置为 false，则当未找到元数据时，此方法将返回 true。</param>
    </member>
    <member name="P:System.Data.Entity.Database.Connection">
      <summary>返回此上下文使用的连接。这可能会导致初始化上下文并创建连接（如果连接不存在）。</summary>
      <returns>此上下文使用的连接。</returns>
    </member>
    <member name="M:System.Data.Entity.Database.Create">
      <summary>在数据库服务器上为支持上下文中定义的模型创建一个新的数据库。请注意，在运行数据库初始化策略之前调用此方法将禁止执行该策略。</summary>
    </member>
    <member name="M:System.Data.Entity.Database.CreateIfNotExists">
      <summary>在数据库服务器上为支持上下文中定义的模型创建一个新的数据库（但仅在该服务器上没有带相同名称的数据库时这样做）。</summary>
      <returns>如果数据库不存在且已创建，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Data.Entity.Database.DefaultConnectionFactory">
      <summary>在从数据库名称或连接字符串创建 <see cref="T:System.Data.Common.DbConnection" /> 时要使用的连接工厂。</summary>
      <returns>要使用的连接工厂。</returns>
    </member>
    <member name="M:System.Data.Entity.Database.Delete">
      <summary>如果数据库服务器上存在数据库，则删除该数据库；否则不执行任何操作。</summary>
      <returns>如果数据库以前存在且已被删除，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Data.Entity.Database.Delete(System.Data.Common.DbConnection)">
      <summary>如果数据库服务器上存在数据库，则删除该数据库；否则不执行任何操作。</summary>
      <returns>如果数据库以前存在且已被删除，则为 true；否则为 false。</returns>
      <param name="existingConnection">与数据库的现有连接。</param>
    </member>
    <member name="M:System.Data.Entity.Database.Delete(System.String)">
      <summary>如果数据库服务器上存在数据库，则删除该数据库；否则不执行任何操作。按照 <see cref="T:System.Data.Entity.DbContext" /> 类的文档中描述的方法，使用给定的数据库名称或连接字符串创建与数据库的连接。</summary>
      <returns>如果数据库以前存在且已被删除，则为 true；否则为 false。</returns>
      <param name="nameOrConnectionString">数据库名称或数据库的连接字符串。</param>
    </member>
    <member name="M:System.Data.Entity.Database.Equals(System.Object)">
      <summary>返回指定的数据库是否等于当前数据库。</summary>
      <returns>如果指定的数据库等于当前数据库，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的数据库。</param>
    </member>
    <member name="M:System.Data.Entity.Database.ExecuteSqlCommand(System.String,System.Object[])">
      <summary>对数据库执行给定的 DDL/DML 命令。</summary>
      <returns>执行命令后由数据库返回的结果。</returns>
      <param name="sql">命令字符串。</param>
      <param name="parameters">要应用于命令字符串的参数。</param>
    </member>
    <member name="M:System.Data.Entity.Database.Exists">
      <summary>检查服务器上是否存在数据库。</summary>
      <returns>如果数据库存在，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Data.Entity.Database.Exists(System.Data.Common.DbConnection)">
      <summary>检查服务器上是否存在数据库。</summary>
      <returns>如果数据库存在，则为 true；否则为 false。</returns>
      <param name="existingConnection">与数据库的现有连接。</param>
    </member>
    <member name="M:System.Data.Entity.Database.Exists(System.String)">
      <summary>检查服务器上是否存在数据库。按照 <see cref="T:System.Data.Entity.DbContext" /> 类的文档中描述的方法，使用给定的数据库名称或连接字符串创建与数据库的连接。</summary>
      <returns>如果数据库存在，则为 true；否则为 false。</returns>
      <param name="nameOrConnectionString">数据库名称或数据库的连接字符串。</param>
    </member>
    <member name="M:System.Data.Entity.Database.GetHashCode">
      <summary>返回指定数据库的哈希函数。</summary>
      <returns>指定数据库的哈希函数。</returns>
    </member>
    <member name="M:System.Data.Entity.Database.GetType">
      <summary>获取当前数据库的类型。</summary>
      <returns>当前数据库的类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Database.Initialize(System.Boolean)">
      <summary>在此上下文上运行注册的 <see cref="T:System.Data.Entity.IDatabaseInitializer`1" />。如果将参数 <paramref name="force" /> 设置为 true，则将运行初始值设定项，不管它之前是否已运行。如果在应用程序正在运行时删除了数据库并且需要重新初始化数据库时，则这样做会很有用。</summary>
      <param name="force">如果设置为 true，则将运行初始值设定项，即使它之前已运行过也是如此。</param>
    </member>
    <member name="M:System.Data.Entity.Database.SetInitializer``1(System.Data.Entity.IDatabaseInitializer{``0})">
      <summary>获取或设置数据库初始化策略。在从 <see cref="T:System.Data.Entity.Infrastructure.DbCompiledModel" /> 初始化 <see cref="T:System.Data.Entity.DbContext" /> 实例时，调用数据库初始化策略。</summary>
      <param name="strategy">策略。</param>
      <typeparam name="TContext">上下文的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Database.SqlQuery``1(System.String,System.Object[])">
      <summary>创建一个原始 SQL 查询，该查询将返回给定泛型类型的元素。类型可以是包含与从查询返回的列名匹配的属性的任何类型，也可以是简单的基元类型。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 对象，此对象在枚举时将执行查询。</returns>
      <param name="sql">SQL 查询字符串。</param>
      <param name="parameters">要应用于 SQL 查询字符串的参数。</param>
      <typeparam name="TElement">查询所返回对象的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Database.SqlQuery(System.Type,System.String,System.Object[])">
      <summary>创建一个原始 SQL 查询，该查询将返回给定类型的元素。类型可以是包含与从查询返回的列名匹配的属性的任何类型，也可以是简单的基元类型。</summary>
      <returns>一个 <see cref="T:System.Collections.IEnumerable" /> 对象，此对象在枚举时将执行查询。</returns>
      <param name="elementType">查询所返回对象的类型。</param>
      <param name="sql">SQL 查询字符串。</param>
      <param name="parameters">要应用于 SQL 查询字符串的参数。</param>
    </member>
    <member name="M:System.Data.Entity.Database.ToString">
      <summary>返回数据库的字符串表示形式。</summary>
      <returns>数据库的字符串表示形式。</returns>
    </member>
    <member name="T:System.Data.Entity.DbContext">
      <summary>提供以对象形式查询和使用实体数据的功能。</summary>
    </member>
    <member name="M:System.Data.Entity.DbContext.#ctor">
      <summary>使用约定构建一个新的上下文实例以创建将连接到的数据库的名称。按照约定，该名称是派生上下文类的全名（命名空间与类名称的组合）。有关如何将其用于创建连接的更多信息，请参见 <see cref="T:System.Data.Entity.DbContext" /> 的“备注”一节。</summary>
    </member>
    <member name="M:System.Data.Entity.DbContext.#ctor(System.Data.Common.DbConnection,System.Boolean)">
      <summary>通过现有连接来连接到数据库以构造一个新的上下文实例。释放上下文时将不会释放该连接。</summary>
      <param name="existingConnection">要用于新的上下文的现有连接。</param>
      <param name="contextOwnsConnection">如果设置为 true，则释放上下文时将释放该连接；否则调用方必须释放该连接。</param>
    </member>
    <member name="M:System.Data.Entity.DbContext.#ctor(System.Data.Common.DbConnection,System.Data.Entity.Infrastructure.DbCompiledModel,System.Boolean)">
      <summary>通过使用现有连接来连接到数据库以构造一个新的上下文实例，并从给定模型初始化该实例。释放上下文时将不会释放该连接。</summary>
      <param name="existingConnection">要用于新的上下文的现有连接。</param>
      <param name="model">支持此上下文的模型。</param>
      <param name="contextOwnsConnection">如果设置为 true，则释放上下文时将释放该连接；否则调用方必须释放该连接。</param>
    </member>
    <member name="M:System.Data.Entity.DbContext.#ctor(System.Data.Entity.Infrastructure.DbCompiledModel)">
      <summary>使用约定构造一个新的上下文实例以创建将连接到的数据库的名称，并从给定模型初始化该名称。按照约定，该名称是派生上下文类的全名（命名空间与类名称的组合）。有关如何将其用于创建连接的更多信息，请参见 <see cref="T:System.Data.Entity.DbContext" /> 的“备注”一节。</summary>
      <param name="model">支持此上下文的模型。</param>
    </member>
    <member name="M:System.Data.Entity.DbContext.#ctor(System.Data.Objects.ObjectContext,System.Boolean)">
      <summary>围绕现有 <see cref="P:System.Data.Entity.Infrastructure.IObjectContextAdapter.ObjectContext" /> 构造一个新的上下文实例。</summary>
      <param name="objectContext">要包装新的上下文的现有 <see cref="P:System.Data.Entity.Infrastructure.IObjectContextAdapter.ObjectContext" />。</param>
      <param name="dbContextOwnsObjectContext">如果设置为 true，则释放 <see cref="T:System.Data.Entity.DbContext" /> 时将释放 <see cref="P:System.Data.Entity.Infrastructure.IObjectContextAdapter.ObjectContext" />；否则调用方必须释放该连接。</param>
    </member>
    <member name="M:System.Data.Entity.DbContext.#ctor(System.String)">
      <summary>可以将给定字符串用作将连接到的数据库的名称或连接字符串来构造一个新的上下文实例。有关如何将其用于创建连接的更多信息，请参见 <see cref="T:System.Data.Entity.DbContext" /> 的“备注”一节。</summary>
      <param name="nameOrConnectionString">数据库名称或连接字符串。</param>
    </member>
    <member name="M:System.Data.Entity.DbContext.#ctor(System.String,System.Data.Entity.Infrastructure.DbCompiledModel)">
      <summary>可以将给定字符串用作将连接到的数据库的名称或连接字符串来构造一个新的上下文实例，并从给定模型初始化该实例。有关如何将其用于创建连接的更多信息，请参见 <see cref="T:System.Data.Entity.DbContext" /> 的“备注”一节。</summary>
      <param name="nameOrConnectionString">数据库名称或连接字符串。</param>
      <param name="model">支持此上下文的模型。</param>
    </member>
    <member name="P:System.Data.Entity.DbContext.ChangeTracker">
      <summary>提供对用于处理实体的更改跟踪的上下文功能的访问。</summary>
      <returns>一个用于访问处理更改跟踪的功能的对象。</returns>
    </member>
    <member name="P:System.Data.Entity.DbContext.Configuration">
      <summary>提供对上下文的配置选项的访问。</summary>
      <returns>一个用于访问配置选项的对象。</returns>
    </member>
    <member name="P:System.Data.Entity.DbContext.Database">
      <summary>为此上下文创建一个数据库实例，并允许您对基础数据库执行创建、删除或存在性检查操作。</summary>
      <returns>创建的数据库。</returns>
    </member>
    <member name="M:System.Data.Entity.DbContext.Dispose">
      <summary>调用受保护的 Dispose 方法。</summary>
    </member>
    <member name="M:System.Data.Entity.DbContext.Dispose(System.Boolean)">
      <summary>释放上下文。在以下情况下也将释放基础 <see cref="T:System.Data.Objects.ObjectContext" />：它由此上下文创建，或者在创建此上下文时将所有权传递给了此上下文。在以下情况下也将释放与数据库的连接（<see cref="T:System.Data.Common.DbConnection" /> 对象）：它由此上下文创建，或者在创建此上下文时将所有权传递给了此上下文。</summary>
      <param name="disposing">如果为 true，则同时释放托管资源和非托管资源；如果为 false，则仅释放非托管资源。</param>
    </member>
    <member name="M:System.Data.Entity.DbContext.Entry(System.Object)">
      <summary>获取给定实体的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" /> 对象，以便提供对与该实体有关的信息的访问以及对实体执行操作的功能。</summary>
      <returns>实体的项。</returns>
      <param name="entity">实体。</param>
    </member>
    <member name="M:System.Data.Entity.DbContext.Entry``1(``0)">
      <summary>获取给定实体的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" /> 对象，以便提供对与该实体有关的信息的访问以及对实体执行操作的功能。</summary>
      <returns>实体的项。</returns>
      <param name="entity">实体。</param>
      <typeparam name="TEntity">实体的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.DbContext.Equals(System.Object)">
      <summary>返回指定的上下文是否等于当前上下文。</summary>
      <returns>如果指定的上下文等于当前上下文，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的上下文。</param>
    </member>
    <member name="M:System.Data.Entity.DbContext.GetHashCode">
      <summary>返回指定上下文的哈希函数。</summary>
      <returns>指定上下文的哈希函数。</returns>
    </member>
    <member name="M:System.Data.Entity.DbContext.GetType">
      <summary>获取当前上下文的类型。</summary>
      <returns>当前上下文的类型。</returns>
    </member>
    <member name="M:System.Data.Entity.DbContext.GetValidationErrors">
      <summary>验证跟踪的实体，并返回包含验证结果的 <see cref="T:System.Data.Entity.Validation.DbEntityValidationResult" /> 的集合。</summary>
      <returns>无效实体的验证结果集合。集合决不为 null，且不得包含 null 值或有效实体的结果。</returns>
    </member>
    <member name="M:System.Data.Entity.DbContext.OnModelCreating(System.Data.Entity.DbModelBuilder)">
      <summary>在完成对派生上下文的模型的初始化后，并在该模型已锁定并用于初始化上下文之前，将调用此方法。虽然此方法的默认实现不执行任何操作，但可在派生类中重写此方法，这样便能在锁定模型之前对其进行进一步的配置。</summary>
      <param name="modelBuilder">定义要创建的上下文的模型的生成器。</param>
    </member>
    <member name="M:System.Data.Entity.DbContext.SaveChanges">
      <summary>将在此上下文中所做的所有更改保存到基础数据库。</summary>
      <returns>已写入基础数据库的对象的数目。</returns>
    </member>
    <member name="M:System.Data.Entity.DbContext.Set``1">
      <summary>为指定的类型返回 <see cref="T:System.Data.Entity.DbSet" />，这将允许对上下文中的给定实体执行 CRUD 操作。</summary>
      <returns>给定实体类型的 <see cref="T:System.Data.Entity.DbSet" /> 实例。</returns>
      <typeparam name="TEntity">应为其返回一个集的实体类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.DbContext.Set(System.Type)">
      <summary>为指定的类型返回 <see cref="T:System.Data.Entity.DbSet" />，这将允许对上下文中的给定实体执行 CRUD 操作。</summary>
      <returns>给定实体类型的 <see cref="T:System.Data.Entity.DbSet" />。</returns>
      <param name="entityType">应为其返回一个集的实体的类型。</param>
    </member>
    <member name="M:System.Data.Entity.DbContext.ShouldValidateEntity(System.Data.Entity.Infrastructure.DbEntityEntry)">
      <summary>扩展点允许用户重写仅验证添加的实体和修改的实体的默认行为。</summary>
      <returns>如果继续验证，则为 true；否则为 false。</returns>
      <param name="entityEntry">应验证的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" /> 实例。</param>
    </member>
    <member name="P:System.Data.Entity.DbContext.System#Data#Entity#Infrastructure#IObjectContextAdapter#ObjectContext">
      <summary>返回基础化此上下文的实体框架 <see cref="T:System.Data.Objects.ObjectContext" />。</summary>
      <returns>返回 <see cref="T:System.Data.Objects.ObjectContext" />。</returns>
    </member>
    <member name="M:System.Data.Entity.DbContext.ToString">
      <summary>返回上下文的字符串表示形式。</summary>
      <returns>上下文的字符串表示形式。</returns>
    </member>
    <member name="M:System.Data.Entity.DbContext.ValidateEntity(System.Data.Entity.Infrastructure.DbEntityEntry,System.Collections.Generic.IDictionary{System.Object,System.Object})">
      <summary>扩展点允许用户自定义实体的验证或筛选出验证结果。由 <see cref="M:System.Data.Entity.DbContext.GetValidationErrors" /> 调用。</summary>
      <returns>实体验证结果。重写时可能为 null。</returns>
      <param name="entityEntry">要验证的 DbEntityEntry 实例。</param>
      <param name="items">包含用于自定义验证的附加信息的用户定义的字典。它将被传递给 <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext" /> 并将作为 <see cref="P:System.ComponentModel.DataAnnotations.ValidationContext.Items" /> 公开。此参数是可选的，并且可为 null。</param>
    </member>
    <member name="T:System.Data.Entity.DbExtensions">
      <summary>包含一组可帮助完成常规任务的扩展方法。</summary>
    </member>
    <member name="M:System.Data.Entity.DbExtensions.AsNoTracking(System.Linq.IQueryable)">
      <summary>返回一个新查询，其中返回的实体将不会在 <see cref="T:System.Data.Entity.DbContext" /> 或 <see cref="T:System.Data.Objects.ObjectContext" /> 中进行缓存。</summary>
      <returns>应用 NoTracking 的新查询，如果不支持 NoTracking，则为源查询。</returns>
      <param name="source">源查询。</param>
    </member>
    <member name="M:System.Data.Entity.DbExtensions.AsNoTracking``1(System.Linq.IQueryable{``0})">
      <summary>返回一个新查询，其中返回的实体将不会在 <see cref="T:System.Data.Entity.DbContext" /> 或 <see cref="T:System.Data.Objects.ObjectContext" /> 中进行缓存。</summary>
      <returns>应用 NoTracking 的新查询，如果不支持 NoTracking，则为源查询。</returns>
      <param name="source">源查询。</param>
      <typeparam name="T">元素类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.DbExtensions.Include(System.Linq.IQueryable,System.String)">
      <summary>指定要包括在查询结果中的相关对象。</summary>
      <returns>一个新的 IQueryable，它具有定义的查询路径。</returns>
      <param name="source">要在其上调用 Include 的源 IQueryable。</param>
      <param name="path">要在查询结果中返回的相关对象列表（以点号分隔）。</param>
    </member>
    <member name="M:System.Data.Entity.DbExtensions.Include``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>指定要包括在查询结果中的相关对象。</summary>
      <returns>T 的一个新 IQueryable，它具有定义的查询路径。</returns>
      <param name="source">要在其上调用 Include 的源 IQueryable。</param>
      <param name="path">表示要包括的路径的 lambda 表达式。</param>
      <typeparam name="T">要查询的实体类型。</typeparam>
      <typeparam name="TProperty">要包含的导航属性的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.DbExtensions.Include``1(System.Linq.IQueryable{``0},System.String)">
      <summary>指定要包括在查询结果中的相关对象。</summary>
      <returns>T 的一个新 IQueryable，它具有定义的查询路径。</returns>
      <param name="source">要在其上调用 Include 的源 IQueryable。</param>
      <param name="path">要在查询结果中返回的相关对象列表（以点号分隔）。</param>
      <typeparam name="T">要查询的实体类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.DbExtensions.Load(System.Linq.IQueryable)">
      <summary>IQueryable 上的一个扩展方法，该方法枚举查询的结果。这与在不实际创建列表的情况下调用 ToList 等效。</summary>
      <param name="source">源查询。</param>
    </member>
    <member name="M:System.Data.Entity.DbExtensions.ToBindingList``1(System.Collections.ObjectModel.ObservableCollection{``0})">
      <summary> 返回 <see cref="T:System.ComponentModel.BindingList`1" /> 实现，该实现与给定的 <see cref="T:System.Collections.ObjectModel.ObservableCollection`1" /> 保持同步。</summary>
      <returns>绑定列表。</returns>
      <param name="source">绑定列表将与其保持同步的集合。</param>
      <typeparam name="T">元素类型。</typeparam>
    </member>
    <member name="T:System.Data.Entity.DbModelBuilder">
      <summary>
        <see cref="T:System.Data.Entity.DbModelBuilder" /> 用于将 CLR 类映射到数据库架构。此以代码为中心的方法称作 Code First，可用于生成实体数据模型 (EDM) 模型。</summary>
    </member>
    <member name="M:System.Data.Entity.DbModelBuilder.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.DbModelBuilder" /> 类的新实例。发现初始模型的过程将使用您计算机上安装的 Entity Framework 的最新版本中所包含的一组约定。</summary>
    </member>
    <member name="M:System.Data.Entity.DbModelBuilder.#ctor(System.Data.Entity.DbModelBuilderVersion)">
      <summary>初始化 <see cref="T:System.Data.Entity.DbModelBuilder" /> 类的新实例，它将使用一组特定的约定来发现初始模型。</summary>
      <param name="modelBuilderVersion">要使用的约定的版本。</param>
    </member>
    <member name="M:System.Data.Entity.DbModelBuilder.Build(System.Data.Common.DbConnection)">
      <summary>使用此生成器基于执行的配置来创建一个 <see cref="T:System.Data.Entity.Infrastructure.DbModel" />。连接可用于确定要使用的数据库提供程序，因为这会影响生成的模型的数据库层。</summary>
      <returns>已生成的模型。</returns>
      <param name="providerConnection">用来确定提供程序信息的连接。</param>
    </member>
    <member name="M:System.Data.Entity.DbModelBuilder.Build(System.Data.Entity.Infrastructure.DbProviderInfo)">
      <summary>使用此生成器基于执行的配置来创建一个 <see cref="T:System.Data.Entity.Infrastructure.DbModel" />。必须指定提供程序信息，因为这会影响生成的模型的数据库层。对于 SqlClient，固定名称为“System.Data.SqlClient”，清单标记为版本年份（例如，“2005”或“2008”。）</summary>
      <returns>已生成的模型。</returns>
      <param name="providerInfo">模型将用于的数据库提供程序。</param>
    </member>
    <member name="M:System.Data.Entity.DbModelBuilder.ComplexType``1">
      <summary>将一个类型注册为模型中的复杂类型，并返回一个可用来配置复杂类型的对象。可对同一类型多次调用此方法以执行多行配置。</summary>
      <returns>指定的复杂类型的配置对象。</returns>
      <typeparam name="TComplexType">要注册或配置的类型。</typeparam>
    </member>
    <member name="P:System.Data.Entity.DbModelBuilder.Configurations">
      <summary>获取此 <see cref="T:System.Data.Entity.DbModelBuilder" /> 的 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.ConfigurationRegistrar" />。注册器允许使用此生成器来注册派生的实体和复杂类型配置。</summary>
      <returns>
        <see cref="T:System.Data.Entity.DbModelBuilder" /> 的配置。</returns>
    </member>
    <member name="P:System.Data.Entity.DbModelBuilder.Conventions">
      <summary>提供对处理约定的此 <see cref="T:System.Data.Entity.DbModelBuilder" /> 的设置的访问。</summary>
      <returns>对设置的访问</returns>
    </member>
    <member name="M:System.Data.Entity.DbModelBuilder.Entity``1">
      <summary>将实体类型注册为模型的一部分，并返回一个可用来配置实体的对象。可对同一实体多次调用此方法以执行多行配置。</summary>
      <returns>指定的实体类型的配置对象。</returns>
      <typeparam name="TEntityType">要注册或配置的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.DbModelBuilder.Equals(System.Object)">
      <summary>返回指定的模型生成器是否等于当前模型生成器。</summary>
      <returns>如果指定的模型生成器等于当前模型生成器，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的模型生成器。</param>
    </member>
    <member name="M:System.Data.Entity.DbModelBuilder.GetHashCode">
      <summary>返回指定模型生成器的哈希函数。</summary>
      <returns>指定模型生成器的哈希函数。</returns>
    </member>
    <member name="M:System.Data.Entity.DbModelBuilder.GetType">
      <summary>获取当前模型生成器的类型。</summary>
      <returns>当前模型生成器的类型。</returns>
    </member>
    <member name="M:System.Data.Entity.DbModelBuilder.Ignore``1">
      <summary>从模型中排除类型。这用于在初始模型发现期间从约定所添加的模型中删除类型。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.DbModelBuilder" /> 实例，以便多个调用可以链接在一起。</returns>
      <typeparam name="T">要排除的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.DbModelBuilder.Ignore(System.Collections.Generic.IEnumerable{System.Type})">
      <summary>从模型中排除一个或多个类型。这用于在初始模型发现期间从约定所添加的模型中删除类型。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.DbModelBuilder" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="types">要从模型中排除的类型。</param>
    </member>
    <member name="M:System.Data.Entity.DbModelBuilder.ToString">
      <summary>返回模型生成器的字符串表示形式。</summary>
      <returns>模型生成器的字符串表示形式。</returns>
    </member>
    <member name="T:System.Data.Entity.DbModelBuilderVersion">
      <summary>可直接将此枚举中的值提供给 <see cref="T:System.Data.Entity.DbModelBuilder" /> 类，也可以在应用于派生自 <see cref="T:System.Data.Entity.DbContext" /> 的类的 <see cref="T:System.Data.Entity.DbModelBuilderVersionAttribute" /> 中使用该值。使用的值可定义在从代码生成模型时应使用的 <see cref="T:System.Data.Entity.DbContext" /> 和 <see cref="T:System.Data.Entity.DbModelBuilder" /> 约定的版本。</summary>
    </member>
    <member name="F:System.Data.Entity.DbModelBuilderVersion.Latest">
      <summary>指示应使用 <see cref="T:System.Data.Entity.DbModelBuilder" /> 和 <see cref="T:System.Data.Entity.DbContext" /> 约定的最新版本。</summary>
    </member>
    <member name="F:System.Data.Entity.DbModelBuilderVersion.V4_1">
      <summary>指示应使用 Entity Framework 4.1 版附带的 <see cref="T:System.Data.Entity.DbModelBuilder" /> 和 <see cref="T:System.Data.Entity.DbContext" /> 约定的版本。</summary>
    </member>
    <member name="F:System.Data.Entity.DbModelBuilderVersion.V5_0_Net4">
      <summary>指示面向 .Net Framework 4 时，应使用 Entity Framework 5.0 版附带的 <see cref="T:System.Data.Entity.DbModelBuilder" /> 和 <see cref="T:System.Data.Entity.DbContext" /> 约定的版本。</summary>
    </member>
    <member name="F:System.Data.Entity.DbModelBuilderVersion.V5_0">
      <summary>指示应使用 Entity Framework 5.0 版附带的 <see cref="T:System.Data.Entity.DbModelBuilder" /> 和 <see cref="T:System.Data.Entity.DbContext" /> 约定的版本。</summary>
    </member>
    <member name="T:System.Data.Entity.DbModelBuilderVersionAttribute">
      <summary>此特性可应用于派生自 <see cref="T:System.Data.Entity.DbContext" /> 的类，以便在从代码生成模型时设置应使用的 <see cref="T:System.Data.Entity.DbContext" /> 和 <see cref="T:System.Data.Entity.DbModelBuilder" /> 约定的版本。有关 <see cref="T:System.Data.Entity.DbModelBuilder" /> 版本的详细信息，请参见 <see cref="T:System.Data.Entity.DbModelBuilderVersion" /> 枚举。</summary>
    </member>
    <member name="M:System.Data.Entity.DbModelBuilderVersionAttribute.#ctor(System.Data.Entity.DbModelBuilderVersion)">
      <summary>初始化 <see cref="T:System.Data.Entity.DbModelBuilderVersionAttribute" /> 类的新实例。</summary>
      <param name="version">要使用的 <see cref="T:System.Data.Entity.DbModelBuilder" /> 约定版本。</param>
    </member>
    <member name="P:System.Data.Entity.DbModelBuilderVersionAttribute.Version">
      <summary>获取 <see cref="T:System.Data.Entity.DbModelBuilder" /> 约定版本。</summary>
      <returns>
        <see cref="T:System.Data.Entity.DbModelBuilder" /> 约定版本。</returns>
    </member>
    <member name="T:System.Data.Entity.DbSet">
      <summary>表示用于执行创建、读取、更新和删除操作的实体集。非泛型版本的 <see cref="T:System.Data.Entity.DbSet`1" />，可在生成时不知道实体类型时使用。</summary>
    </member>
    <member name="M:System.Data.Entity.DbSet.Add(System.Object)">
      <summary>将给定实体以 <see cref="F:System.Data.EntityState.Added" /> 状态添加到上下文中。保存更改后，“已添加”状态的实体将插入到数据库中。在保存更改后，对象状态将更改为 <see cref="F:System.Data.EntityState.Unchanged" />。</summary>
      <returns>实体。</returns>
      <param name="entity">要添加的实体。</param>
    </member>
    <member name="M:System.Data.Entity.DbSet.Attach(System.Object)">
      <summary>将给定实体以 <see cref="F:System.Data.EntityState.Unchanged" /> 状态附加到上下文中</summary>
      <returns>实体。</returns>
      <param name="entity">要附加的实体。</param>
    </member>
    <member name="M:System.Data.Entity.DbSet.Cast``1">
      <summary>返回等效泛型 <see cref="T:System.Data.Entity.DbSet`1" /> 对象。</summary>
      <returns>泛型集对象。</returns>
      <typeparam name="TEntity">要强制转换为泛型集对象的对象的基类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.DbSet.Create">
      <summary>为此集的类型创建新的实体实例。该实例不会添加或附加到此集。如果基础上下文配置为创建代理，且实体类型满足创建代理的要求，则返回的实例将是一个代理。</summary>
      <returns>实体实例，可能为代理。</returns>
    </member>
    <member name="M:System.Data.Entity.DbSet.Create(System.Type)">
      <summary>为此集的类型或派生自此集类型的类型创建新的实体实例。该实例不会添加或附加到此集。如果基础上下文配置为创建代理，且实体类型满足创建代理的要求，则返回的实例将是一个代理。</summary>
      <returns>实体实例，可能为代理。</returns>
      <param name="derivedEntityType">派生的实体类型。</param>
    </member>
    <member name="M:System.Data.Entity.DbSet.Equals(System.Object)">
      <summary>返回指定的集是否等于当前集。</summary>
      <returns>如果指定的对象等于当前集，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的集。</param>
    </member>
    <member name="M:System.Data.Entity.DbSet.Find(System.Object[])">
      <summary>使用主键值尝试查找上下文跟踪的实体。如果该实体未在上下文中，则将针对数据源中的数据执行和计算查询；如果未在上下文或数据源中找到该实体，则将返回 null。请注意，Find 还会返回已添加到上下文但尚未保存到数据库中的实体。</summary>
      <returns>找到的实体或为 null。</returns>
      <param name="keyValues">要查找的实体的主键值。</param>
    </member>
    <member name="M:System.Data.Entity.DbSet.GetHashCode">
      <summary>返回指定集的哈希函数。</summary>
      <returns>指定集的哈希函数。</returns>
    </member>
    <member name="M:System.Data.Entity.DbSet.GetType">
      <summary>获取当前集的类型。</summary>
      <returns>当前集的类型。</returns>
    </member>
    <member name="P:System.Data.Entity.DbSet.Local">
      <summary>返回表示该实体集中当前正由上下文跟踪且尚未标记为“已删除”的实体的 <see cref="T:System.Collections.ObjectModel.ObservableCollection`1" />。访问 Local 属性绝不会导致向数据库发送查询。此属性通常在执行查询后使用。</summary>
      <returns>本地视图。</returns>
    </member>
    <member name="M:System.Data.Entity.DbSet.Remove(System.Object)">
      <summary>将给定实体标记为 <see cref="F:System.Data.EntityState.Deleted" />。保存更改后，将从数据库中删除该实体。在调用此方法之前，该实体必须以另一种状态存在于该上下文中。</summary>
      <returns>实体。</returns>
      <param name="entity">要删除的实体。</param>
    </member>
    <member name="M:System.Data.Entity.DbSet.SqlQuery(System.String,System.Object[])">
      <summary>创建一个原始 SQL 查询，该查询将返回此集中的实体。默认情况下，上下文会跟踪返回的实体；可通过对此方法返回的 <see cref="T:System.Data.Entity.Infrastructure.DbSqlQuery`1" /> 调用 AsNoTracking 来更改此设置。</summary>
      <returns>一个 <see cref="T:System.Data.Entity.Infrastructure.DbSqlQuery" /> 对象，此对象在枚举时将执行查询。</returns>
      <param name="sql">SQL 查询字符串。</param>
      <param name="parameters">要应用于 SQL 查询字符串的参数。</param>
    </member>
    <member name="T:System.Data.Entity.DbSet`1">
      <summary>表示用于执行创建、读取、更新和删除操作的类型化实体集。DbSet 不是公共可构造的，只能从 <see cref="T:System.Data.Entity.DbContext" /> 实例创建。</summary>
      <typeparam name="TEntity">定义集的类型。该类型可以是派生类型以及基类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.DbSet`1.Add(`0)">
      <summary>将给定实体以 <see cref="F:System.Data.EntityState.Added" /> 状态添加到上下文中。保存更改后，“已添加”状态的实体将插入到数据库中。在保存更改后，对象状态将更改为 <see cref="F:System.Data.EntityState.Unchanged" />。</summary>
      <returns>返回 <see cref="{0}" />。</returns>
      <param name="entity">要添加的实体。</param>
    </member>
    <member name="M:System.Data.Entity.DbSet`1.Attach(`0)">
      <summary>将给定实体以 <see cref="F:System.Data.EntityState.Unchanged" /> 状态附加到上下文中</summary>
      <returns>返回 <see cref="{0}" />。</returns>
      <param name="entity">要附加的实体。</param>
    </member>
    <member name="M:System.Data.Entity.DbSet`1.Create``1">
      <summary>为此集的类型或派生自此集类型的类型创建新的实体实例。该实例不会添加或附加到此集。如果基础上下文配置为创建代理，且实体类型满足创建代理的要求，则返回的实例将是一个代理。</summary>
      <returns>返回 <see cref="{0}" />。</returns>
      <typeparam name="TDerivedEntity">定义集的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.DbSet`1.Create">
      <summary>为此集的类型创建新的实体实例。该实例不会添加或附加到此集。如果基础上下文配置为创建代理，且实体类型满足创建代理的要求，则返回的实例将是一个代理。</summary>
      <returns>返回 <see cref="{0}" />。</returns>
    </member>
    <member name="M:System.Data.Entity.DbSet`1.Equals(System.Object)">
      <summary>返回指定的对象是否等于当前集。</summary>
      <returns>如果指定的集等于当前集，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的集。</param>
    </member>
    <member name="M:System.Data.Entity.DbSet`1.Find(System.Object[])">
      <summary>使用主键值尝试查找上下文跟踪的实体。如果该实体未在上下文中，则将针对数据源中的数据执行和计算查询；如果未在上下文或数据源中找到该实体，则将返回 null。请注意，Find 还会返回已添加到上下文但尚未保存到数据库中的实体。</summary>
      <returns>返回 <see cref="T:System.Boolean" />。</returns>
      <param name="keyValues">要查找的实体的主键值。</param>
    </member>
    <member name="M:System.Data.Entity.DbSet`1.GetHashCode">
      <summary>返回指定集的哈希函数。</summary>
      <returns>指定集的哈希函数。</returns>
    </member>
    <member name="M:System.Data.Entity.DbSet`1.GetType">
      <summary>获取当前集的类型。</summary>
      <returns>当前集的类型。</returns>
    </member>
    <member name="P:System.Data.Entity.DbSet`1.Local">
      <summary>返回表示该实体集中当前正由上下文跟踪且尚未标记为“已删除”的实体的 <see cref="T:System.Collections.ObjectModel.ObservableCollection`1" />。访问 Local 属性绝不会导致向数据库发送查询。此属性通常在执行查询后使用。</summary>
      <returns>本地视图。</returns>
    </member>
    <member name="M:System.Data.Entity.DbSet`1.op_Implicit(System.Data.Entity.DbSet{`0})~System.Data.Entity.DbSet">
      <summary>返回等效非泛型 <see cref="T:System.Data.Entity.DbSet" />。</summary>
      <returns>返回 <see cref="T:System.Data.Entity.DbSet" />，它是非泛型集对象。</returns>
      <param name="entry">要为其返回等效非泛型 <see cref="T:System.Data.Entity.DbSet" /> 的项。</param>
    </member>
    <member name="M:System.Data.Entity.DbSet`1.Remove(`0)">
      <summary>将给定实体标记为 <see cref="F:System.Data.EntityState.Deleted" />。保存更改后，将从数据库中删除该实体。在调用此方法之前，该实体必须以另一种状态存在于该上下文中。</summary>
      <returns>返回 <see cref="T:System.Data.Entity.DbSet" />。</returns>
      <param name="entity">要删除的实体。</param>
    </member>
    <member name="M:System.Data.Entity.DbSet`1.SqlQuery(System.String,System.Object[])">
      <summary>创建一个原始 SQL 查询，该查询将返回此集中的实体。默认情况下，上下文会跟踪返回的实体；可通过对此方法返回的 <see cref="T:System.Data.Entity.Infrastructure.DbSqlQuery`1" /> 调用 AsNoTracking 来更改此设置。</summary>
      <returns>一个 <see cref="T:System.Data.Entity.Infrastructure.DbSqlQuery`1" /> 对象，此对象在枚举时将执行查询。</returns>
      <param name="sql">SQL 查询字符串。</param>
      <param name="parameters">返回的实体始终是此集的类型，而不会是派生的类型。如果查询的一个或多个表可能包含其他实体类型的数据，则必须编写适当的 SQL 查询以确保只返回适当类型的实体。要应用于 SQL 查询字符串的参数。</param>
    </member>
    <member name="T:System.Data.Entity.DropCreateDatabaseAlways`1">
      <summary>
        <see cref="T:System.Data.Entity.IDatabaseInitializer`1" /> 的实现，它总是会在首次在应用程序域中使用上下文时，重新创建数据库并选择用数据重新设置数据库的种子。若要设置数据库的种子，请创建一个派生类并重写 Seed 方法。</summary>
      <typeparam name="TContext">上下文的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.DropCreateDatabaseAlways`1.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.DropCreateDatabaseAlways`1" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.DropCreateDatabaseAlways`1.InitializeDatabase(`0)">
      <summary>执行策略以初始化给定上下文的数据库。</summary>
      <param name="context">上下文。</param>
    </member>
    <member name="M:System.Data.Entity.DropCreateDatabaseAlways`1.Seed(`0)">
      <summary>在重写时，将向上下文中添加数据以设置种子。默认实现不执行任何操作。</summary>
      <param name="context">要设置种子的上下文。</param>
    </member>
    <member name="T:System.Data.Entity.DropCreateDatabaseIfModelChanges`1">
      <summary>
        <see cref="T:System.Data.Entity.IDatabaseInitializer`1" /> 的实现，它仅在模型自数据库创建后发生更改时删除数据库、重新创建数据库并选择用数据重新设置数据库的种子。可以通过在创建数据库时向其中写入存储模型的哈希，然后将该哈希与从当前模型生成的哈希进行比较来实现这一点。若要设置数据库的种子，请创建一个派生类并重写 Seed 方法。</summary>
      <typeparam name="TContext">上下文的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.DropCreateDatabaseIfModelChanges`1.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.DropCreateDatabaseIfModelChanges`1" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.DropCreateDatabaseIfModelChanges`1.InitializeDatabase(`0)">
      <summary>执行策略以初始化给定上下文的数据库。</summary>
      <param name="context">上下文。</param>
    </member>
    <member name="M:System.Data.Entity.DropCreateDatabaseIfModelChanges`1.Seed(`0)">
      <summary>应重写以向上下文实际添加数据以设置种子。默认实现不执行任何操作。</summary>
      <param name="context">要设置种子的上下文。</param>
    </member>
    <member name="T:System.Data.Entity.IDatabaseInitializer`1">
      <summary>定义数据库初始值设定项的方法。</summary>
      <typeparam name="TContext">上下文的类型。此类型参数为协变的。也就是说，您可以使用您指定的类型或者派生程度更小的任何类型。有关协变式和逆变式的详细信息，请参阅。</typeparam>
    </member>
    <member name="M:System.Data.Entity.IDatabaseInitializer`1.InitializeDatabase(`0)">
      <summary>执行策略以初始化给定上下文的数据库。</summary>
      <param name="context">上下文。</param>
    </member>
    <member name="T:System.Data.Entity.IDbSet`1">
      <summary>表示上下文中的所有实体的集或可从给定类型的数据库中查询的所有实体的集。<see cref="T:System.Data.Entity.DbSet" /> 是 <see cref="T:System.Data.Entity.IDbSet`1" /> 的具体实现。</summary>
      <typeparam name="TEntity">定义集的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.IDbSet`1.Add(`0)">
      <summary>将给定实体以 <see cref="F:System.Data.EntityState.Added" /> 状态添加到上下文中。保存更改后，“已添加”状态的实体将插入到数据库中。在保存更改后，对象状态将更改为 <see cref="F:System.Data.EntityState.Unchanged" />。</summary>
      <returns>返回实体。</returns>
      <param name="entity">要添加的实体。</param>
    </member>
    <member name="M:System.Data.Entity.IDbSet`1.Attach(`0)">
      <summary>将给定实体以 <see cref="F:System.Data.EntityState.Unchanged" /> 状态附加到上下文中</summary>
      <returns>返回实体。</returns>
      <param name="entity">要附加的实体。</param>
    </member>
    <member name="M:System.Data.Entity.IDbSet`1.Create">
      <summary>为此集的类型创建新的实体实例。该实例不会添加或附加到此集。如果基础上下文配置为创建代理，且实体类型满足创建代理的要求，则返回的实例将是一个代理。</summary>
      <returns>返回可能为代理的实体实例。</returns>
    </member>
    <member name="M:System.Data.Entity.IDbSet`1.Create``1">
      <summary>为此集的类型或派生自此集类型的类型创建新的实体实例。该实例不会添加或附加到此集。如果基础上下文配置为创建代理，且实体类型满足创建代理的要求，则返回的实例将是一个代理。</summary>
      <returns>返回可能为代理的实体实例。</returns>
      <typeparam name="TDerivedEntity">要创建的实体的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.IDbSet`1.Find(System.Object[])">
      <summary>使用主键值尝试查找上下文跟踪的实体。如果该实体未在上下文中，则将针对数据源中的数据执行和计算查询；如果未在上下文或数据源中找到该实体，则将返回 null。请注意，Find 还会返回已添加到上下文但尚未保存到数据库中的实体。</summary>
      <returns>返回找到的实体或 null。</returns>
      <param name="keyValues">要查找的实体的主键值。</param>
    </member>
    <member name="P:System.Data.Entity.IDbSet`1.Local">
      <summary>返回表示该实体集中当前正由上下文跟踪且尚未标记为“已删除”的实体的 <see cref="T:System.Collections.ObjectModel.ObservableCollection`1" />。访问 Local 属性绝不会导致向数据库发送查询。此属性通常在执行查询后使用。</summary>
      <returns>本地视图。</returns>
    </member>
    <member name="M:System.Data.Entity.IDbSet`1.Remove(`0)">
      <summary>将给定实体标记为 <see cref="F:System.Data.EntityState.Deleted" />。保存更改后，将从数据库中删除该实体。在调用此方法之前，该实体必须以另一种状态存在于该上下文中。</summary>
      <returns>返回实体。</returns>
      <param name="entity">要删除的实体。</param>
    </member>
    <member name="T:System.Data.Entity.MigrateDatabaseToLatestVersion`2">
      <summary>将使用 Code First 迁移将数据库更新到最新迁移的 IDatabaseInitializer 的实现。</summary>
      <typeparam name="TContext"></typeparam>
      <typeparam name="TMigrationsConfiguration"></typeparam>
    </member>
    <member name="M:System.Data.Entity.MigrateDatabaseToLatestVersion`2.#ctor">
      <summary> 初始化 MigrateDatabaseToLatestVersion 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.MigrateDatabaseToLatestVersion`2.#ctor(System.String)">
      <summary> 初始化将使用配置文件中的特定连接字符串连接到数据库以执行迁移的 MigrateDatabaseToLatestVersion 类的新实例。</summary>
      <param name="connectionStringName">用于迁移的连接字符串的名称。</param>
    </member>
    <member name="M:System.Data.Entity.MigrateDatabaseToLatestVersion`2.InitializeDatabase(`0)">
      <summary>执行策略以初始化给定上下文的数据库。</summary>
      <param name="context">上下文。</param>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbChangeTracker">
      <summary>由 <see cref="T:System.Data.Entity.DbContext" /> 的 ChangeTracker 方法返回以提供对与实体的更改跟踪相关的上下文的功能的访问。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbChangeTracker.DetectChanges">
      <summary>检测对 POCO 实体的属性和关系所做的更改。请注意，某些类型的实体（如更改跟踪代理和派生自 <see cref="T:System.Data.Objects.DataClasses.EntityObject" /> 的实体）会自动报告更改，并且这些类型的实体通常不需要调用此方法。另请注意，通常，<see cref="T:System.Data.Entity.DbContext" /> 的许多方法及其相关类会自动调用此方法，这样一来，便很少需要显式调用此方法。但通常出于性能原因，可能需要使用 <see cref="P:System.Data.Entity.DbContext.Configuration" /> 中的 AutoDetectChangesEnabled 标记来关闭对此方法的自动调用。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbChangeTracker.Entries">
      <summary>获取此上下文跟踪的所有实体的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" /> 对象。</summary>
      <returns>项。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbChangeTracker.Entries``1">
      <summary>获取此上下文跟踪的给定类型的所有实体的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" /> 对象。</summary>
      <returns>项。</returns>
      <typeparam name="TEntity">实体的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbChangeTracker.Equals(System.Object)">
      <summary>返回指定的跟踪器是否等于当前跟踪器。</summary>
      <returns>如果指定的跟踪器等于当前跟踪器，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的跟踪器。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbChangeTracker.GetHashCode">
      <summary>返回指定跟踪器的哈希函数。</summary>
      <returns>指定跟踪器的哈希函数。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbChangeTracker.GetType">
      <summary>获取当前跟踪器的类型。</summary>
      <returns>当前跟踪器的类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbChangeTracker.ToString">
      <summary>返回跟踪器的字符串表示形式。</summary>
      <returns>跟踪器的字符串表示形式。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbCollectionEntry">
      <summary>非泛型版本的 <see cref="T:System.Data.Entity.Infrastructure.DbCollectionEntry`2" /> 类。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbCollectionEntry.Cast``2">
      <summary>返回等效泛型 <see cref="T:System.Data.Entity.Infrastructure.DbCollectionEntry`2" /> 对象。</summary>
      <returns>等效泛型对象。</returns>
      <typeparam name="TEntity">在其上声明该成员的实体的类型。</typeparam>
      <typeparam name="TElement">集合元素的类型。</typeparam>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbCollectionEntry.CurrentValue">
      <summary>获取或设置导航属性的当前值。当前值为导航属性引用的实体。</summary>
      <returns>当前值。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbCollectionEntry.EntityEntry">
      <summary>此导航属性所属的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" />。</summary>
      <returns>拥有此导航属性的实体的项。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbCollectionEntry.IsLoaded">
      <summary>获取一个值，该值指示是否已从数据库加载实体的集合。</summary>
      <returns>如果加载了集合，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbCollectionEntry.Load">
      <summary>从数据库中加载实体的集合。请注意，不会使用数据库中的值覆盖上下文中已存在的实体。</summary>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbCollectionEntry.Name">
      <summary>获取属性名称。</summary>
      <returns>属性名称。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbCollectionEntry.Query">
      <summary>返回将用于从数据库加载此集合的查询。可使用 LINQ 修改返回的查询以便在数据库中执行筛选或操作，例如，计算数据库中的集合中的实体数，而不实际加载这些实体。</summary>
      <returns>针对集合的查询。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbCollectionEntry`2">
      <summary>此类的实例从 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" /> 的 <see cref="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.Collection(System.String)" /> 方法返回，并且允许对实体的集合导航属性执行各种操作（如加载）。</summary>
      <typeparam name="TEntity">此属性所属的实体的类型。</typeparam>
      <typeparam name="TElement">实体集合中的元素的类型。</typeparam>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbCollectionEntry`2.CurrentValue">
      <summary>获取或设置导航属性的当前值。当前值为导航属性引用的实体。</summary>
      <returns>当前值。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbCollectionEntry`2.EntityEntry">
      <summary>此导航属性所属的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" />。</summary>
      <returns>拥有此导航属性的实体的项。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbCollectionEntry`2.IsLoaded">
      <summary>获取一个值，该值指示是否已从数据库加载实体的集合。</summary>
      <returns>如果加载了集合，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbCollectionEntry`2.Load">
      <summary>从数据库中加载实体的集合。请注意，不会使用数据库中的值覆盖上下文中已存在的实体。</summary>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbCollectionEntry`2.Name">
      <summary>获取属性名称。</summary>
      <returns>属性名称。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbCollectionEntry`2.op_Implicit(System.Data.Entity.Infrastructure.DbCollectionEntry{`0,`1})~System.Data.Entity.Infrastructure.DbCollectionEntry">
      <summary>返回由此对象表示的导航属性的非泛型 <see cref="T:System.Data.Entity.Infrastructure.DbCollectionEntry" /> 类的新实例。</summary>
      <returns>返回 <see cref="T:System.Data.Entity.Infrastructure.DbCollectionEntry" />。</returns>
      <param name="entry">项。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbCollectionEntry`2.Query">
      <summary>返回将用于从数据库加载此集合的查询。可使用 LINQ 修改返回的查询以便在数据库中执行筛选或操作，例如，计算数据库中的集合中的实体数，而不实际加载这些实体。</summary>
      <returns>针对集合的查询。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbCompiledModel">
      <summary>实体数据模型 (EDM) 模型的不可变表示形式，该模型可用于创建 <see cref="T:System.Data.Objects.ObjectContext" />，也可将该模型传递给 <see cref="T:System.Data.Entity.DbContext" /> 的构造函数。为了提高性能，应缓存和重用此类型的实例以构造上下文。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbCompiledModel.CreateObjectContext``1(System.Data.Common.DbConnection)">
      <summary>创建 <see cref="T:System.Data.Objects.ObjectContext" /> 的实例或派生自 ObjectContext 的类的实例。请注意，可使用适当的 DbContext 构造函数来改为创建 DbContext 的实例。如果使用派生的 ObjectContext，则它必须具有带单个 EntityConnection 参数的公共构造函数。传递的连接由创建的 ObjectContext 使用，但不属于上下文。在释放上下文后，调用方必须释放连接。</summary>
      <returns>返回上下文。</returns>
      <param name="existingConnection">供上下文使用的数据库的现有连接。</param>
      <typeparam name="TContext">要创建的上下文的类型。</typeparam>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbComplexPropertyEntry">
      <summary>非泛型版本的 <see cref="T:System.Data.Entity.Infrastructure.DbComplexPropertyEntry`2" /> 类。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbComplexPropertyEntry.Cast``2">
      <summary>返回等效泛型 <see cref="T:System.Data.Entity.Infrastructure.DbComplexPropertyEntry`2" /> 对象。</summary>
      <returns>等效泛型对象。</returns>
      <typeparam name="TEntity">在其上声明该成员的实体的类型。</typeparam>
      <typeparam name="TComplexProperty">复杂属性的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbComplexPropertyEntry.ComplexProperty(System.String)">
      <summary>获取表示此属性的嵌套复杂属性的对象。</summary>
      <returns>表示嵌套属性的对象。</returns>
      <param name="propertyName">嵌套属性的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbComplexPropertyEntry.Property(System.String)">
      <summary>获取表示此属性的嵌套属性的对象。此方法可用于标量属性和/或复杂属性。</summary>
      <returns>表示嵌套属性的对象。</returns>
      <param name="propertyName">嵌套属性的名称。</param>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbComplexPropertyEntry`2">
      <summary>此类的实例从 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" /> 的 ComplexProperty 方法返回，并允许访问复杂属性的状态。</summary>
      <typeparam name="TEntity">此属性所属的实体的类型。</typeparam>
      <typeparam name="TComplexProperty">此属性所属的实体的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbComplexPropertyEntry`2.ComplexProperty``1(System.Linq.Expressions.Expression{System.Func{`1,``0}})">
      <summary>获取表示此属性的嵌套复杂属性的对象。</summary>
      <returns>表示嵌套属性的对象。</returns>
      <param name="property">表示嵌套属性的表达式。</param>
      <typeparam name="TNestedComplexProperty">嵌套属性的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbComplexPropertyEntry`2.ComplexProperty(System.String)">
      <summary>获取表示此属性的嵌套复杂属性的对象。</summary>
      <returns>表示嵌套属性的对象。</returns>
      <param name="propertyName">嵌套属性的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbComplexPropertyEntry`2.ComplexProperty``1(System.String)">
      <summary>获取表示此属性的嵌套复杂属性的对象。</summary>
      <returns>表示嵌套属性的对象。</returns>
      <param name="propertyName">嵌套属性的名称。</param>
      <typeparam name="TNestedComplexProperty">嵌套属性的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbComplexPropertyEntry`2.op_Implicit(System.Data.Entity.Infrastructure.DbComplexPropertyEntry{`0,`1})~System.Data.Entity.Infrastructure.DbComplexPropertyEntry">
      <summary>返回由此对象表示的属性的非泛型 <see cref="T:System.Data.Entity.Infrastructure.DbComplexPropertyEntry" /> 类的新实例。</summary>
      <returns>返回 <see cref="T:System.Data.Entity.Infrastructure.DbComplexPropertyEntry" />。</returns>
      <param name="entry">项。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbComplexPropertyEntry`2.Property``1(System.Linq.Expressions.Expression{System.Func{`1,``0}})">
      <summary>获取表示此属性的嵌套属性的对象。此方法可用于标量属性和/或复杂属性。</summary>
      <returns>表示嵌套属性的对象。</returns>
      <param name="property">表示嵌套属性的表达式。</param>
      <typeparam name="TNestedProperty">嵌套属性的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbComplexPropertyEntry`2.Property``1(System.String)">
      <summary>获取表示此属性的嵌套属性的对象。此方法可用于标量属性和/或复杂属性。</summary>
      <returns>表示嵌套属性的对象。</returns>
      <param name="propertyName">嵌套属性的名称。</param>
      <typeparam name="TNestedProperty">嵌套属性的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbComplexPropertyEntry`2.Property(System.String)">
      <summary>获取表示此属性的嵌套属性的对象。此方法可用于标量属性和/或复杂属性。</summary>
      <returns>表示嵌套属性的对象。</returns>
      <param name="propertyName">嵌套属性的名称。</param>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbConnectionInfo">
      <summary>表示有关数据库连接的信息。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbConnectionInfo.#ctor(System.String)">
      <summary>创建表示应用程序配置文件中指定的连接的 <see cref="T:System.Data.Entity.Infrastructure.DbConnectionInfo" /> 类的新实例。</summary>
      <param name="connectionName">应用程序配置中的连接字符串的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbConnectionInfo.#ctor(System.String,System.String)">
      <summary>创建基于连接字符串的 <see cref="T:System.Data.Entity.Infrastructure.DbConnectionInfo" /> 类的新实例。</summary>
      <param name="connectionString">用于连接的连接字符串。</param>
      <param name="providerInvariantName">用于连接的提供程序的名称。将“System.Data.SqlClient”用于 SQL Server。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbConnectionInfo.Equals(System.Object)">
      <summary>指示指定的对象是否等于当前对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的对象。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbConnectionInfo.GetHashCode">
      <summary>返回此实例的哈希代码。</summary>
      <returns>此实例的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbConnectionInfo.GetType">
      <summary>获取当前实例的类型。</summary>
      <returns>当前实例的类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbConnectionInfo.ToString">
      <summary>返回表示当前对象的字符串。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbConnectionStringOrigin">
      <summary>描述与 <see cref="T:System.Data.Entity.DbContext" /> 对象关联的数据库连接字符串的来源。</summary>
    </member>
    <member name="F:System.Data.Entity.Infrastructure.DbConnectionStringOrigin.Convention">
      <summary>该连接字符串是按约定创建的。</summary>
    </member>
    <member name="F:System.Data.Entity.Infrastructure.DbConnectionStringOrigin.Configuration">
      <summary>该连接字符串是从外部配置中进行读取的。</summary>
    </member>
    <member name="F:System.Data.Entity.Infrastructure.DbConnectionStringOrigin.UserCode">
      <summary>该连接字符串是在运行时显式指定的。</summary>
    </member>
    <member name="F:System.Data.Entity.Infrastructure.DbConnectionStringOrigin.DbContextInfo">
      <summary>     该连接字符串是由提供给 DbContextInfo 的连接信息重写的。</summary>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbContextConfiguration">
      <summary>由 <see cref="T:System.Data.Entity.DbContext" /> 的 Configuration 方法返回以提供对上下文的配置选项的访问。</summary>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbContextConfiguration.AutoDetectChangesEnabled">
      <summary>获取或设置是否启用自动配置更改检测。</summary>
      <returns>如果启用了自动配置更改检测，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbContextConfiguration.Equals(System.Object)">
      <summary>返回指定的配置是否等于当前配置。</summary>
      <returns>如果指定的配置等于当前配置，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的配置。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbContextConfiguration.GetHashCode">
      <summary>返回指定配置的哈希函数。</summary>
      <returns>指定配置的哈希函数。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbContextConfiguration.GetType">
      <summary>获取当前配置的类型。</summary>
      <returns>当前配置的类型。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbContextConfiguration.LazyLoadingEnabled">
      <summary>获取或设置一个值，该值指示是否启用针对公开为导航属性的关系的延迟加载。延迟加载在默认情况下处于启用状态。</summary>
      <returns>如果启用了延迟加载，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbContextConfiguration.ProxyCreationEnabled">
      <summary>获取或设置一个值，该值指示框架在创建实体类型的实例时是否会创建动态生成的代理类的实例。请注意，即使使用此标记启用了代理创建，也只会为满足代理设置要求的实体类型创建代理实例。默认情况下启用代理创建。</summary>
      <returns>如果启用了代理创建，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbContextConfiguration.ToString">
      <summary>返回配置的字符串表示形式。</summary>
      <returns>配置的字符串表示形式。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbContextConfiguration.ValidateOnSaveEnabled">
      <summary>获取或设置一个值，该值指示在调用 <see cref="M:System.Data.Entity.DbContext.SaveChanges" /> 时是否自动验证跟踪实体。默认值为 true。</summary>
      <returns>如果应自动验证跟踪实体，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbContextInfo">
      <summary>提供有关给定 <see cref="T:System.Data.Entity.DbContext" /> 类型的运行时信息。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbContextInfo.#ctor(System.Type)">
      <summary>初始化表示给定 <see cref="T:System.Data.Entity.DbContext" /> 类型的新实例。</summary>
      <param name="contextType">派生自 <see cref="T:System.Data.Entity.DbContext" /> 的类型。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbContextInfo.#ctor(System.Type,System.Configuration.Configuration)">
      <summary>初始化表示给定 <see cref="T:System.Data.Entity.DbContext" /> 类型的新实例。可以提供外部配置对象（如 app.config 或 web.config），并将在解析连接字符串期间使用该对象。这包括查找连接字符串和 DefaultConnectionFactory 条目。</summary>
      <param name="contextType">派生自 <see cref="T:System.Data.Entity.DbContext" /> 的类型。</param>
      <param name="config">表示配置文件的对象。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbContextInfo.#ctor(System.Type,System.Configuration.Configuration,System.Data.Entity.Infrastructure.DbConnectionInfo)">
      <summary>初始化表示给定的面向特定数据库的 <see cref="T:System.Data.Entity.DbContext" /> 的新实例。外部配置对象（如 app.config 或 web.config）可以提供，并且将在连接字符串解决方案中使用。这包括查找连接字符串和 DefaultConnectionFactory 条目。</summary>
      <param name="contextType">派生自 <see cref="T:System.Data.Entity.DbContext" /> 的类型。</param>
      <param name="config">表示配置文件的对象。</param>
      <param name="connectionInfo">要使用的数据库的连接信息。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbContextInfo.#ctor(System.Type,System.Configuration.ConnectionStringSettingsCollection)">
      <summary>初始化表示给定 <see cref="T:System.Data.Entity.DbContext" /> 类型的新实例。可提供连接字符串的外部列表，并且将在解析连接字符串期间使用该列表，用于替代外部配置文件中指定的所有连接字符串。</summary>
      <param name="contextType">派生自 <see cref="T:System.Data.Entity.DbContext" /> 的类型。</param>
      <param name="connectionStringSettings">连接字符串的集合。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbContextInfo.#ctor(System.Type,System.Data.Entity.Infrastructure.DbConnectionInfo)">
      <summary>初始化表示给定的面向特定数据库的 <see cref="T:System.Data.Entity.DbContext" /> 的新实例。</summary>
      <param name="contextType">派生自 <see cref="T:System.Data.Entity.DbContext" /> 的类型。</param>
      <param name="connectionInfo">要使用的数据库的连接信息。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbContextInfo.#ctor(System.Type,System.Data.Entity.Infrastructure.DbProviderInfo)">
      <summary>初始化表示给定 <see cref="T:System.Data.Entity.DbContext" /> 类型的新实例。可以提供 <see cref="T:System.Data.Entity.Infrastructure.DbProviderInfo" /> 对象，以重写构造基础 EDM 模型时使用的默认确定的提供程序。</summary>
      <param name="contextType">派生自 <see cref="T:System.Data.Entity.DbContext" /> 的类型。</param>
      <param name="modelProviderInfo">指定要面向的基础 ADO.NET 提供程序的 <see cref="T:System.Data.Entity.Infrastructure.DbProviderInfo" /> 对象。</param>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbContextInfo.ConnectionProviderName">
      <summary>获取或设置基础 <see cref="T:System.Data.Entity.DbContext" /> 类型使用的连接的 ADO.NET 提供程序名称。</summary>
      <returns>基础类型使用的连接的 ADO.NET 提供程序名称。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbContextInfo.ConnectionString">
      <summary>获取或设置基础 <see cref="T:System.Data.Entity.DbContext" /> 类型使用的连接字符串。</summary>
      <returns>基础 <see cref="T:System.Data.Entity.DbContext" /> 类型使用的连接字符串。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbContextInfo.ConnectionStringName">
      <summary>获取或设置基础 <see cref="T:System.Data.Entity.DbContext" /> 类型使用的连接字符串名称。</summary>
      <returns>基础 <see cref="T:System.Data.Entity.DbContext" /> 类型使用的连接字符串名称。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbContextInfo.ConnectionStringOrigin">
      <summary>获取或设置基础 <see cref="T:System.Data.Entity.DbContext" /> 类型使用的连接字符串的来源。</summary>
      <returns>基础 <see cref="T:System.Data.Entity.DbContext" /> 类型使用的连接字符串的来源。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbContextInfo.ContextType">
      <summary>获取或设置具体 <see cref="T:System.Data.Entity.DbContext" /> 类型。</summary>
      <returns>具体 <see cref="T:System.Data.Entity.DbContext" /> 类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbContextInfo.CreateInstance">
      <summary>创建表示给定 <see cref="T:System.Data.Entity.DbContext" /> 类型的新实例。</summary>
      <returns>如果可以创建基础 <see cref="T:System.Data.Entity.DbContext" /> 类型的实例，则返回新实例；否则返回 null。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbContextInfo.IsConstructible">
      <summary>获取或设置指示基础 <see cref="T:System.Data.Entity.DbContext" /> 类型是否可创建的值。</summary>
      <returns>如果基础 <see cref="T:System.Data.Entity.DbContext" /> 类型可创建，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbContextInfo.OnModelCreating">
      <summary>获取或设置已初始化的派生上下文的模型。</summary>
      <returns>已初始化的派生上下文的模型。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbEntityEntry">
      <summary>非泛型版本的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" /> 类。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry.Cast``1">
      <summary>返回由此对象表示的跟踪实体的给定泛型类型的泛型 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" /> 类的新实例。跟踪实体的类型必须与泛型类型兼容，否则将引发异常。</summary>
      <returns>泛型版本。</returns>
      <typeparam name="TEntity">实体的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry.Collection(System.String)">
      <summary>获取一个对象，该对象表示从该实体到相关实体集合的集合导航属性。</summary>
      <returns>表示导航属性的对象。</returns>
      <param name="navigationProperty">导航属性的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry.ComplexProperty(System.String)">
      <summary>获取一个对象，此对象表示该实体的复杂属性。</summary>
      <returns>表示复杂属性的对象。</returns>
      <param name="propertyName">复杂属性的名称。</param>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbEntityEntry.CurrentValues">
      <summary>获取由此对象表示的跟踪实体的当前属性值。</summary>
      <returns>当前值。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbEntityEntry.Entity">
      <summary>获取实体。</summary>
      <returns>实体。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry.Equals(System.Data.Entity.Infrastructure.DbEntityEntry)">
      <summary>确定指定的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" /> 是否等于此实例。如果两个 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" /> 实例都是同一 <see cref="T:System.Data.Entity.DbContext" /> 上的同一实体的项，则将这两个实例视为相等。</summary>
      <returns>如果指定的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" /> 等于此实例，则为 true；否则为 false。</returns>
      <param name="other">要与此实例进行比较的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" />。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry.Equals(System.Object)">
      <summary>确定指定的 <see cref="T:System.Object" /> 是否等于此实例。如果两个 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" /> 实例都是同一 <see cref="T:System.Data.Entity.DbContext" /> 上的同一实体的项，则将这两个实例视为相等。</summary>
      <returns>如果指定对象等于此实例，则为 true；否则为 false。</returns>
      <param name="obj">要与此实例进行比较的 <see cref="T:System.Object" />。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry.GetDatabaseValues">
      <summary>在数据库中查询跟踪实体的值的副本，因为当前它们位于数据库中。更改返回的字典中的值将不会更新数据库中的值。如果未在数据库中找到实体，则返回 null。</summary>
      <returns>存储值。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry.GetHashCode">
      <summary>返回此实例的哈希代码。</summary>
      <returns>此实例的哈希代码，适合在哈希算法和类似哈希表的数据结构中使用。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry.GetType">
      <summary>获取当前数据库实体项的类型。</summary>
      <returns>当前数据库实体项的类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry.GetValidationResult">
      <summary>验证此 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" /> 实例并返回验证结果。</summary>
      <returns>实体验证结果。如果改写 <see cref="M:System.Data.Entity.DbContext.ValidateEntity(System.Data.Entity.Infrastructure.DbEntityEntry,System.Collections.Generic.IDictionary{System.Object,System.Object})" /> 方法，则可能为 null。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry.Member(System.String)">
      <summary>获取表示实体成员的对象。返回的对象的运行时类型将随要求的成员类型的不同而不同。当前支持的成员类型及其返回类型为：引用导航属性 (<see cref="T:System.Data.Entity.Infrastructure.DbReferenceEntry" />)、集合导航属性 (<see cref="T:System.Data.Entity.Infrastructure.DbCollectionEntry" />)、基元/标量属性 (<see cref="T:System.Data.Entity.Infrastructure.DbPropertyEntry" />) 和复杂属性 ( <see cref="T:System.Data.Entity.Infrastructure.DbComplexPropertyEntry" />)。</summary>
      <returns>表示成员的对象。</returns>
      <param name="propertyName">成员名。</param>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbEntityEntry.OriginalValues">
      <summary>获取由此对象表示的跟踪实体的原始属性值。原始值通常是实体的属性值，就像上次从数据库查询这些值一样。</summary>
      <returns>原始值。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry.Property(System.String)">
      <summary>获取一个对象，此对象表示该实体的标量属性或复杂属性。</summary>
      <returns>表示属性的对象。</returns>
      <param name="propertyName">属性的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry.Reference(System.String)">
      <summary>获取一个对象，该对象表示从该实体到其他实体的引用（即非集合）导航属性。</summary>
      <returns>表示导航属性的对象。</returns>
      <param name="navigationProperty">导航属性的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry.Reload">
      <summary>从数据库重新加载该实体时会用数据库中的值覆盖任何属性值。调用此方法后，该实体将处于“未更改”状态。</summary>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbEntityEntry.State">
      <summary>获取或设置实体的状态。</summary>
      <returns>状态。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry.ToString">
      <summary>返回数据库实体项的字符串表示形式。</summary>
      <returns>数据库实体项的字符串表示形式。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbEntityEntry`1">
      <summary>此类的实例提供对有关由 <see cref="T:System.Data.Entity.DbContext" /> 跟踪的实体的信息和控制的访问权。使用上下文的 Entity 或 Entities 方法获取此类型的对象。</summary>
      <typeparam name="TEntity">实体的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.Collection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.ICollection{``0}}})">
      <summary>获取一个对象，该对象表示从该实体到相关实体集合的集合导航属性。</summary>
      <returns>表示导航属性的对象。</returns>
      <param name="navigationProperty">表示导航属性的表达式。</param>
      <typeparam name="TElement">集合中的元素类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.Collection``1(System.String)">
      <summary>获取一个对象，该对象表示从该实体到相关实体集合的集合导航属性。</summary>
      <returns>表示导航属性的对象。</returns>
      <param name="navigationProperty">导航属性的名称。</param>
      <typeparam name="TElement">集合中的元素类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.Collection(System.String)">
      <summary>获取一个对象，该对象表示从该实体到相关实体集合的集合导航属性。</summary>
      <returns>表示导航属性的对象。</returns>
      <param name="navigationProperty">导航属性的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.ComplexProperty``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary>获取一个对象，此对象表示该实体的复杂属性。</summary>
      <returns>表示复杂属性的对象。</returns>
      <param name="property">属性。</param>
      <typeparam name="TComplexProperty">复杂属性的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.ComplexProperty``1(System.String)">
      <summary>获取一个对象，此对象表示该实体的复杂属性。</summary>
      <returns>表示复杂属性的对象。</returns>
      <param name="propertyName">复杂属性的名称。</param>
      <typeparam name="TComplexProperty">复杂属性的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.ComplexProperty(System.String)">
      <summary>获取一个对象，此对象表示该实体的复杂属性。</summary>
      <returns>表示复杂属性的对象。</returns>
      <param name="propertyName">复杂属性的名称。</param>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbEntityEntry`1.CurrentValues">
      <summary>获取由此对象表示的跟踪实体的当前属性值。</summary>
      <returns>当前值。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbEntityEntry`1.Entity">
      <summary>获取实体。</summary>
      <returns>返回 <see cref="T:System.Data.Entity.Infrastructure.DbPropertyValues" />。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.Equals(System.Data.Entity.Infrastructure.DbEntityEntry{`0})">
      <summary>确定指定的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" /> 是否等于此实例。如果两个 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" /> 实例都是同一 <see cref="T:System.Data.Entity.DbContext" /> 上的同一实体的项，则将这两个实例视为相等。</summary>
      <returns>如果指定的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" /> 等于此实例，则为 true；否则为 false。</returns>
      <param name="other">要与此实例进行比较的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" />。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.Equals(System.Object)">
      <summary>确定指定的 <see cref="T:System.Object" /> 是否等于此实例。如果两个 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" /> 实例都是同一 <see cref="T:System.Data.Entity.DbContext" /> 上的同一实体的项，则将这两个实例视为相等。</summary>
      <returns>如果指定对象等于此实例，则为 true；否则为 false。</returns>
      <param name="obj">要与此实例进行比较的 <see cref="T:System.Object" />。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.GetDatabaseValues">
      <summary>在数据库中查询跟踪实体的值的副本，因为当前它们位于数据库中。更改返回的字典中的值将不会更新数据库中的值。如果未在数据库中找到该实体，则返回 null。</summary>
      <returns>存储值。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.GetHashCode">
      <summary>返回此实例的哈希代码。</summary>
      <returns>此实例的哈希代码，适合在哈希算法和类似哈希表的数据结构中使用。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.GetType">
      <summary>获取当前数据库实体的类型。</summary>
      <returns>当前数据库实体的类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.GetValidationResult">
      <summary>验证此 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" /> 实例并返回验证结果。</summary>
      <returns>实体验证结果。如果改写 <see cref="M:System.Data.Entity.DbContext.ValidateEntity(System.Data.Entity.Infrastructure.DbEntityEntry,System.Collections.Generic.IDictionary{System.Object,System.Object})" /> 方法，则可能为 Null。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.Member(System.String)">
      <summary>获取表示实体成员的对象。返回的对象的运行时类型将随要求的成员类型的不同而不同。当前支持的成员类型及其返回类型为：引用导航属性：<see cref="T:System.Data.Entity.Infrastructure.DbReferenceEntry" />。集合导航属性：<see cref="T:System.Data.Entity.Infrastructure.DbCollectionEntry" />。基元/标量属性：<see cref="T:System.Data.Entity.Infrastructure.DbPropertyEntry" />。复杂属性：<see cref="T:System.Data.Entity.Infrastructure.DbComplexPropertyEntry" />。</summary>
      <returns>实体的成员。</returns>
      <param name="propertyName">成员名。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.Member``1(System.String)">
      <summary>获取表示实体成员的对象。返回的对象的运行时类型将随要求的成员类型的不同而不同。当前支持的成员类型及其返回类型为：引用导航属性：<see cref="T:System.Data.Entity.Infrastructure.DbReferenceEntry`2" />。集合导航属性：<see cref="T:System.Data.Entity.Infrastructure.DbCollectionEntry`2" />。基元/标量属性：<see cref="T:System.Data.Entity.Infrastructure.DbPropertyEntry`2" />。复杂属性：<see cref="T:System.Data.Entity.Infrastructure.DbComplexPropertyEntry`2" />。</summary>
      <returns>表示成员的对象。</returns>
      <param name="propertyName">成员名。</param>
      <typeparam name="TMember">成员的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.op_Implicit(System.Data.Entity.Infrastructure.DbEntityEntry{`0})~System.Data.Entity.Infrastructure.DbEntityEntry">
      <summary>返回由此对象表示的跟踪实体的非泛型 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" /> 类的新实例。</summary>
      <returns>返回 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" />。</returns>
      <param name="entry">项。</param>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbEntityEntry`1.OriginalValues">
      <summary>获取由此对象表示的跟踪实体的原始属性值。原始值通常是实体的属性值，就像上次从数据库查询这些值一样。</summary>
      <returns>原始值。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.Property``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary>获取一个对象，此对象表示该实体的标量属性或复杂属性。</summary>
      <returns>表示属性的对象。</returns>
      <param name="property">属性。</param>
      <typeparam name="TProperty">属性的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.Property``1(System.String)">
      <summary>获取一个对象，此对象表示该实体的标量属性或复杂属性。</summary>
      <returns>表示属性的对象。</returns>
      <param name="propertyName">属性的名称。</param>
      <typeparam name="TProperty">属性的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.Property(System.String)">
      <summary>获取一个对象，此对象表示该实体的标量属性或复杂属性。</summary>
      <returns>表示属性的对象。</returns>
      <param name="propertyName">属性的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.Reference``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary>获取一个对象，该对象表示从该实体到其他实体的引用（即非集合）导航属性。</summary>
      <returns>表示导航属性的对象。</returns>
      <param name="navigationProperty">表示导航属性的表达式。</param>
      <typeparam name="TProperty">属性的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.Reference(System.String)">
      <summary>获取一个对象，该对象表示从该实体到其他实体的引用（非集合）导航属性。</summary>
      <returns>表示导航属性的对象。</returns>
      <param name="navigationProperty">导航属性的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.Reference``1(System.String)">
      <summary>获取一个对象，该对象表示从该实体到其他实体的引用（即非集合）导航属性。</summary>
      <returns>表示导航属性的对象。</returns>
      <param name="navigationProperty">导航属性的名称。</param>
      <typeparam name="TProperty">属性的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.Reload">
      <summary>从数据库重新加载该实体时会用数据库中的值覆盖任何属性值。调用此方法后，该实体将处于“未更改”状态。</summary>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbEntityEntry`1.State">
      <summary>获取或设置实体的状态。</summary>
      <returns>状态。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbEntityEntry`1.ToString">
      <summary>返回数据库实体值的字符串表示形式。</summary>
      <returns>数据库实体值的字符串表示形式。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbMemberEntry">
      <summary>这是一个抽象基类，用于表示实体的标量/复杂属性或导航属性。标量和复杂属性使用派生类 <see cref="T:System.Data.Entity.Infrastructure.DbPropertyEntry" />，引用导航属性使用派生类 <see cref="T:System.Data.Entity.Infrastructure.DbReferenceEntry" />，而集合导航属性使用派生类 <see cref="T:System.Data.Entity.Infrastructure.DbCollectionEntry" />。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbMemberEntry.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Infrastructure.DbMemberEntry" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbMemberEntry.Cast``2">
      <summary>返回等效泛型 <see cref="T:System.Data.Entity.Infrastructure.DbMemberEntry`2" /> 对象。</summary>
      <returns>等效泛型对象。</returns>
      <typeparam name="TEntity">在其上声明该成员的实体的类型。</typeparam>
      <typeparam name="TProperty">属性的类型。</typeparam>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbMemberEntry.CurrentValue">
      <summary>获取或设置此属性的当前值。</summary>
      <returns>当前值。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbMemberEntry.EntityEntry">
      <summary>此成员所属的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" />。</summary>
      <returns>拥有此成员的实体的项。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbMemberEntry.Equals(System.Object)">
      <summary>返回指定的项是否等于当前查询。</summary>
      <returns>如果指定的项等于当前项，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的项。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbMemberEntry.GetHashCode">
      <summary>返回指定项的哈希函数。</summary>
      <returns>指定项的哈希函数。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbMemberEntry.GetType">
      <summary>获取当前项的类型。</summary>
      <returns>当前项的类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbMemberEntry.GetValidationErrors">
      <summary>验证此属性。</summary>
      <returns>
        <see cref="T:System.Data.Entity.Validation.DbValidationError" /> 对象的集合。决不会为 null。如果实体有效，则集合将为空。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbMemberEntry.Name">
      <summary>获取属性的名称。</summary>
      <returns>属性名称。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbMemberEntry.ToString">
      <summary>返回数据库属性值的字符串表示形式。</summary>
      <returns>数据库属性值的字符串表示形式。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbMemberEntry`2">
      <summary>一个抽象基类，用于表示实体的标量/复杂或导航属性。标量和复杂属性使用派生类 <see cref="T:System.Data.Entity.Infrastructure.DbPropertyEntry`2" />，引用导航属性使用派生类 <see cref="T:System.Data.Entity.Infrastructure.DbReferenceEntry`2" />，而集合导航属性使用派生类 <see cref="T:System.Data.Entity.Infrastructure.DbCollectionEntry`2" />。</summary>
      <typeparam name="TEntity">此属性所属的实体的类型。</typeparam>
      <typeparam name="TProperty">此属性所属的实体的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbMemberEntry`2.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Infrastructure.DbMemberEntry`2" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbMemberEntry`2.CurrentValue">
      <summary>获取或设置此属性的当前值。</summary>
      <returns>返回 <see cref="T:System.String" />。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbMemberEntry`2.EntityEntry">
      <summary>此成员所属的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" />。</summary>
      <returns>拥有此成员的实体的项。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbMemberEntry`2.Equals(System.Object)">
      <summary>返回指定的查询是否等于当前查询。</summary>
      <returns>如果指定的查询等于当前查询，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的查询。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbMemberEntry`2.GetHashCode">
      <summary>返回指定查询的哈希函数。</summary>
      <returns>指定查询的哈希函数。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbMemberEntry`2.GetType">
      <summary>获取当前查询的类型。</summary>
      <returns>当前查询的类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbMemberEntry`2.GetValidationErrors">
      <summary>验证此属性。</summary>
      <returns>
        <see cref="T:System.Data.Entity.Validation.DbValidationError" /> 对象的集合。决不会为 null。如果实体有效，则集合将为空。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbMemberEntry`2.Name">
      <summary>获取数据库成员项的名称。</summary>
      <returns>数据库成员项的名称。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbMemberEntry`2.op_Implicit(System.Data.Entity.Infrastructure.DbMemberEntry{`0,`1})~System.Data.Entity.Infrastructure.DbMemberEntry">
      <summary>返回由此对象表示的属性的非泛型 <see cref="T:System.Data.Entity.Infrastructure.DbMemberEntry" /> 类的新实例。</summary>
      <returns>返回 <see cref="T:System.Data.Entity.Infrastructure.DbMemberEntry" />。</returns>
      <param name="entry">项。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbMemberEntry`2.ToString">
      <summary>返回数据库属性值的字符串表示形式。</summary>
      <returns>数据库属性值的字符串表示形式。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbModel">
      <summary>表示由 <see cref="T:System.Data.Entity.DbModelBuilder" /> 创建的实体数据模型 (EDM)。<see cref="M:System.Data.Entity.Infrastructure.DbModel.Compile" /> 方法，可用来从 EDM 的表示形式转换到<see cref="T:System.Data.Entity.Infrastructure.DbCompiledModel" />，后者是适合缓存和创建 <see cref="T:System.Data.Entity.DbContext" /> 或 <see cref="T:System.Data.Objects.ObjectContext" /> 实例的模型的编译快照。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbModel.Compile">
      <summary>为此模型创建 <see cref="T:System.Data.Entity.Infrastructure.DbCompiledModel" />，它是适合缓存和创建 <see cref="T:System.Data.Entity.DbContext" /> 实例的编译快照。</summary>
      <returns>已编译的模型。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbPropertyEntry">
      <summary>非泛型版本的 <see cref="T:System.Data.Entity.Infrastructure.DbPropertyEntry`2" /> 类。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbPropertyEntry.Cast``2">
      <summary>返回等效泛型 <see cref="T:System.Data.Entity.Infrastructure.DbPropertyEntry`2" /> 对象。</summary>
      <returns>等效泛型对象。</returns>
      <typeparam name="TEntity">在其上声明该成员的实体的类型。</typeparam>
      <typeparam name="TProperty">属性的类型。</typeparam>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbPropertyEntry.CurrentValue">
      <summary>获取或设置此属性的当前值。</summary>
      <returns>当前值。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbPropertyEntry.EntityEntry">
      <summary>此属性所属的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" />。</summary>
      <returns>拥有此属性的实体的项。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbPropertyEntry.IsModified">
      <summary>获取或设置一个值，该值指示是否在从数据库加载此属性的值后修改了该值。</summary>
      <returns>如果修改了此实例，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbPropertyEntry.Name">
      <summary>获取属性名称。</summary>
      <returns>属性名称。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbPropertyEntry.OriginalValue">
      <summary>获取或设置此属性的原始值。</summary>
      <returns>原始值。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbPropertyEntry.ParentProperty">
      <summary>此属性嵌套在其中的属性的 <see cref="T:System.Data.Entity.Infrastructure.DbPropertyEntry" />。此方法仅为复杂对象的属性返回非 null 项；否则，它将为实体本身的属性返回 null。</summary>
      <returns>父复杂属性的项，如果这是一个实体属性，则为 null。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbPropertyEntry`2">
      <summary>此类的实例从 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" /> 的 Property 方法返回，并允许访问标量属性或复杂属性。</summary>
      <typeparam name="TEntity">此属性所属的实体的类型。</typeparam>
      <typeparam name="TProperty">属性的类型。</typeparam>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbPropertyEntry`2.CurrentValue">
      <summary>获取或设置此属性的当前值。</summary>
      <returns>返回 <see cref="T:System.Data.Entity.Infrastructure.DbPropertyEntry" />。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbPropertyEntry`2.EntityEntry">
      <summary>此属性所属的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" />。</summary>
      <returns>拥有此属性的实体的项。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbPropertyEntry`2.IsModified">
      <summary>获取或设置一个值，该值指示是否在从数据库加载此属性的值后修改了该值。</summary>
      <returns>如果修改了此实例，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbPropertyEntry`2.Name">
      <summary>获取属性名称。</summary>
      <returns>属性名称。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbPropertyEntry`2.op_Implicit(System.Data.Entity.Infrastructure.DbPropertyEntry{`0,`1})~System.Data.Entity.Infrastructure.DbPropertyEntry">
      <summary>返回由此对象表示的属性的非泛型 <see cref="T:System.Data.Entity.Infrastructure.DbPropertyEntry" /> 类的新实例。</summary>
      <returns>返回 <see cref="T:System.Data.Entity.Infrastructure.DbPropertyEntry" />。</returns>
      <param name="entry">项。</param>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbPropertyEntry`2.OriginalValue">
      <summary>获取或设置此属性的原始值。</summary>
      <returns>返回 <see cref="T:System.String" />。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbPropertyEntry`2.ParentProperty">
      <summary>此属性嵌套在其中的属性的 <see cref="T:System.Data.Entity.Infrastructure.DbPropertyEntry" />。此方法仅为复杂对象的属性返回非 null 项；它将为实体本身的属性返回 null。</summary>
      <returns>父复杂属性的项，如果这是一个实体属性，则为 null。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbPropertyValues">
      <summary>基础实体或复杂对象的所有属性的集合。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbPropertyValues.Clone">
      <summary>创建一个包含此字典中所有属性的副本的新字典。对新字典所做的更改不会反映在此字典中，反之亦然。</summary>
      <returns>此字典的副本。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbPropertyValues.Equals(System.Object)">
      <summary>返回指定的值是否等于当前值。</summary>
      <returns>如果指定的值等于当前值，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的值。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbPropertyValues.GetHashCode">
      <summary>返回指定值的哈希函数。</summary>
      <returns>指定值的哈希函数。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbPropertyValues.GetType">
      <summary>获取当前值的类型。</summary>
      <returns>当前值的类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbPropertyValues.GetValue``1(System.String)">
      <summary>获取属性的值，正如使用索引属性 getter 但将类型设置为泛型参数的类型一样。这在使用嵌套字典以避免编写包含大量强制转换的表达式时特别有用。</summary>
      <returns>返回 <see cref="T:System.Type" />，它是 <paramref name="propertyName" /> 指定的属性的值。</returns>
      <param name="propertyName">属性的名称。</param>
      <typeparam name="TValue">属性的类型。</typeparam>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbPropertyValues.Item(System.String)">
      <summary>获取或设置带指定属性名称的属性的值。该值可能为此类的嵌套实例。</summary>
      <returns>属性的值。</returns>
      <param name="propertyName">要检索的属性的名称。</param>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbPropertyValues.PropertyNames">
      <summary>获取作为只读集的此字典中所有属性的名称集。</summary>
      <returns>属性名称。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbPropertyValues.SetValues(System.Data.Entity.Infrastructure.DbPropertyValues)">
      <summary>通过从其他字典中读取值来设置此字典的值。另一个字典必须基于与此字典相同的类型，或基于派生自此字典的类型的类型。</summary>
      <param name="propertyValues">从中读取值的字典。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbPropertyValues.SetValues(System.Object)">
      <summary>通过从给定对象中读取值来设置此字典的值。给定对象可以是任何类型。如果对象具有任何与字典中的属性名称匹配且可读取的名称的属性，则将读取这些属性。其他属性将被忽略。这样，便能从简单的数据传输对象 (DTO) 中复制属性。</summary>
      <param name="obj">从中读取值的对象。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbPropertyValues.ToObject">
      <summary>为此字典创建一个基础类型对象，并使用此字典中的属性值冻结该对象。</summary>
      <returns>已复制到新对象中的此字典的属性。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbPropertyValues.ToString">
      <summary>返回数据库属性值的字符串表示形式。</summary>
      <returns>数据库属性值的字符串表示形式。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbProviderInfo">
      <summary>存储有关数据库提供程序的信息。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbProviderInfo.#ctor(System.String,System.String)">
      <summary>初始化 <see cref="T:System.Data.Entity.Infrastructure.DbProviderInfo" /> 类的新实例。</summary>
      <param name="providerInvariantName">应使用指定 SQL Server Compact Edition 版本的提供程序固定名称。</param>
      <param name="providerManifestToken">Schema 元素上的提供程序的清单标记信息。</param>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbProviderInfo.ProviderInvariantName">
      <summary>获取指定应使用的 SQL Server Compact Edition 版本的提供程序固定名称。</summary>
      <returns>数据库提供程序的数据提供程序。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbProviderInfo.ProviderManifestToken">
      <summary>获取 Schema 元素上的提供程序的清单标记信息。</summary>
      <returns>清单标记。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbQuery">
      <summary>表示针对 <see cref="T:System.Data.Entity.DbContext" /> 的非泛型 LINQ to Entities 查询。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery.AsNoTracking">
      <summary>返回一个新查询，其中返回的实体将不会在 <see cref="T:System.Data.Entity.DbContext" /> 中进行缓存。</summary>
      <returns>应用了 NoTracking 的新查询。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery.Cast``1">
      <summary>返回等效泛型 <see cref="T:System.Data.Entity.Infrastructure.DbQuery`1" /> 对象。</summary>
      <returns>泛型集对象。</returns>
      <typeparam name="TElement">已为其创建查询的元素的类型。</typeparam>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbQuery.ElementType">
      <summary>IQueryable 元素类型。</summary>
      <returns>元素类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery.Equals(System.Object)">
      <summary>返回指定的查询是否等于当前查询。</summary>
      <returns>如果指定的查询等于当前查询，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的查询。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery.GetHashCode">
      <summary>返回指定查询的哈希函数。</summary>
      <returns>指定查询的哈希函数。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery.GetType">
      <summary>获取当前查询的类型。</summary>
      <returns>当前查询的类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery.Include(System.String)">
      <summary>返回针对 <see cref="T:System.Data.Entity.DbContext" /> 的非泛型 LINQ to Entities 查询。</summary>
      <returns>包含的 LINQ 查询。</returns>
      <param name="path">包含的 LINQ 的路径。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery.System#Collections#IEnumerable#GetEnumerator">
      <summary>获取此查询的枚举会导致对存储区执行此查询。</summary>
      <returns>查询的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbQuery.System#ComponentModel#IListSource#ContainsListCollection">
      <summary>返回 false。</summary>
      <returns>false</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery.System#ComponentModel#IListSource#GetList">
      <summary>引发一个异常，该异常指示不支持直接绑定到存储查询。</summary>
      <returns>决不返回；总是引发。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbQuery.System#Linq#IQueryable#Expression">
      <summary>IQueryable LINQ 表达式。</summary>
      <returns>返回 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbQuery.System#Linq#IQueryable#Provider">
      <summary>IQueryable 提供程序。</summary>
      <returns>返回 <see cref="T:System.Linq.IQueryProvider" />。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery.ToString">
      <summary>返回基础查询的 <see cref="T:System.String" /> 表示形式。</summary>
      <returns>查询字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbQuery`1">
      <summary>表示针对 <see cref="T:System.Data.Entity.DbContext" /> 的 LINQ to Entities 查询。</summary>
      <typeparam name="TResult">要查询的实体的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery`1.AsNoTracking">
      <summary>返回一个新查询，其中返回的实体将不会在 <see cref="T:System.Data.Entity.DbContext" /> 中进行缓存。</summary>
      <returns>应用了 NoTracking 的新查询。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery`1.Equals(System.Object)">
      <summary>返回指定的查询是否等于当前查询。</summary>
      <returns>如果指定的查询等于当前查询，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的查询。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery`1.GetHashCode">
      <summary>返回指定查询的哈希函数。</summary>
      <returns>指定查询的哈希函数。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery`1.GetType">
      <summary>获取当前查询的类型。</summary>
      <returns>当前查询的类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery`1.Include(System.String)">
      <summary>返回针对 <see cref="T:System.Data.Entity.DbContext" /> 的包含的 LINQ to Entities 查询。</summary>
      <returns>包含的 LINQ。</returns>
      <param name="path">包含的 LINQ 的路径。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery`1.op_Implicit(System.Data.Entity.Infrastructure.DbQuery{`0})~System.Data.Entity.Infrastructure.DbQuery">
      <summary>返回此查询的非泛型 <see cref="T:System.Data.Entity.Infrastructure.DbQuery" /> 类的新实例。</summary>
      <returns>返回 <see cref="T:System.Data.Entity.Infrastructure.DbQuery" />。</returns>
      <param name="entry">项。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>获取此查询的枚举会导致对存储区执行此查询。</summary>
      <returns>返回此查询的 <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>获取此查询的枚举会导致对存储区执行此查询。</summary>
      <returns>返回此查询的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbQuery`1.System#ComponentModel#IListSource#ContainsListCollection">
      <summary>返回 false。</summary>
      <returns>false</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery`1.System#ComponentModel#IListSource#GetList">
      <summary>引发一个异常，该异常指示不支持直接绑定到存储查询。</summary>
      <returns>决不返回；总是引发。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbQuery`1.System#Linq#IQueryable#ElementType">
      <summary>IQueryable 元素类型。</summary>
      <returns>返回 <see cref="T:System.Type" />。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbQuery`1.System#Linq#IQueryable#Expression">
      <summary>IQueryable LINQ 表达式。</summary>
      <returns>返回 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbQuery`1.System#Linq#IQueryable#Provider">
      <summary>IQueryable 提供程序。</summary>
      <returns>返回 <see cref="T:System.Linq.IQueryProvider" />。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbQuery`1.ToString">
      <summary>返回基础查询的 <see cref="T:System.String" /> 表示形式。</summary>
      <returns>查询字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbReferenceEntry">
      <summary>非泛型版本的 <see cref="T:System.Data.Entity.Infrastructure.DbReferenceEntry`2" /> 类。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbReferenceEntry.Cast``2">
      <summary>返回等效泛型 <see cref="T:System.Data.Entity.Infrastructure.DbReferenceEntry`2" /> 对象。</summary>
      <returns>等效泛型对象。</returns>
      <typeparam name="TEntity">在其上声明该成员的实体的类型。</typeparam>
      <typeparam name="TProperty">属性的类型。</typeparam>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbReferenceEntry.CurrentValue">
      <summary>获取或设置导航属性的当前值。当前值为导航属性引用的实体。</summary>
      <returns>当前值。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbReferenceEntry.EntityEntry">
      <summary>此导航属性所属的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" />。</summary>
      <returns>拥有此导航属性的实体的项。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbReferenceEntry.IsLoaded">
      <summary>获取一个值，该值指示是否已从数据库加载实体。</summary>
      <returns>如果加载了实体，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbReferenceEntry.Load">
      <summary>从数据库加载实体。请注意，如果该实体已在数据库中，则不会使用数据库中的值覆盖该实体。</summary>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbReferenceEntry.Name">
      <summary>获取属性名称。</summary>
      <returns>属性名称。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbReferenceEntry.Query">
      <summary>返回将用于从数据库加载此实体的查询。可使用 LINQ 修改返回的查询以便在数据库中执行筛选或操作。</summary>
      <returns>针对实体的查询。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbReferenceEntry`2">
      <summary>此类的实例从 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" /> 的 Reference 方法返回，并且允许对实体的引用导航属性执行各种操作（如加载）。</summary>
      <typeparam name="TEntity">此属性所属的实体的类型。</typeparam>
      <typeparam name="TProperty">属性的类型。</typeparam>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbReferenceEntry`2.CurrentValue">
      <summary>获取或设置导航属性的当前值。当前值为导航属性引用的实体。</summary>
      <returns>返回 <see cref="T:System.Linq.IQueryable`1" />。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbReferenceEntry`2.EntityEntry">
      <summary>此导航属性所属的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry`1" />。</summary>
      <returns>拥有此导航属性的实体的项。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbReferenceEntry`2.IsLoaded">
      <summary>获取一个值，该值指示是否已从数据库加载实体。</summary>
      <returns>如果加载了实体，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbReferenceEntry`2.Load">
      <summary>从数据库加载实体。如果该实体已在数据库中，则不会使用数据库中的值覆盖该实体。</summary>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbReferenceEntry`2.Name">
      <summary>获取属性名称。</summary>
      <returns>属性名称。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbReferenceEntry`2.op_Implicit(System.Data.Entity.Infrastructure.DbReferenceEntry{`0,`1})~System.Data.Entity.Infrastructure.DbReferenceEntry">
      <summary> 返回由此对象表示的导航属性的非泛型 <see cref="T:System.Data.Entity.Infrastructure.DbReferenceEntry" /> 类的新实例。</summary>
      <returns>返回 <see cref="T:System.Data.Entity.Infrastructure.DbReferenceEntry" />。</returns>
      <param name="entry">项。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbReferenceEntry`2.Query">
      <summary>返回将用于从数据库加载此实体的查询。可使用 LINQ 修改返回的查询以便在数据库中执行筛选或操作。</summary>
      <returns>针对实体的查询。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbSqlQuery">
      <summary>表示实体的一个 SQL 查询，该查询从 <see cref="T:System.Data.Entity.DbContext" /> 创建并通过该上下文中的连接来执行。此类的实例是从该实体类型的 <see cref="T:System.Data.Entity.DbSet" /> 实例中获取的。创建此对象时不会执行该查询；只要枚举该查询，该查询就会执行（例如，使用 foreach）。非实体的 SQL 查询是使用 <see cref="P:System.Data.Entity.DbContext.Database" /> 创建的。请参见 <see cref="T:System.Data.Entity.Infrastructure.DbSqlQuery`1" /> 了解此类的泛型版本。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbSqlQuery.AsNoTracking">
      <summary>返回一个新查询，其中查询的结果将不由关联的 <see cref="T:System.Data.Entity.DbContext" /> 跟踪。</summary>
      <returns>应用了 no-tracking 的新查询。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbSqlQuery.Equals(System.Object)">
      <summary>返回指定的查询是否等于当前查询。</summary>
      <returns>如果指定的查询等于当前查询，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的查询。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbSqlQuery.GetEnumerator">
      <summary>执行查询并返回元素的枚举器。</summary>
      <returns>一个 <see cref="T:System.Collections.IEnumerator" /> 对象，可用于循环访问元素。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbSqlQuery.GetHashCode">
      <summary>返回指定查询的哈希函数。</summary>
      <returns>指定查询的哈希函数。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbSqlQuery.GetType">
      <summary>获取当前查询的类型。</summary>
      <returns>当前查询的类型。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbSqlQuery.System#ComponentModel#IListSource#ContainsListCollection">
      <summary>返回 false。</summary>
      <returns>false</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbSqlQuery.System#ComponentModel#IListSource#GetList">
      <summary>引发一个异常，该异常指示不支持直接绑定到存储查询。</summary>
      <returns>决不返回；总是引发。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbSqlQuery.ToString">
      <summary>返回一个包含 SQL 字符串的 <see cref="T:System.String" />，该字符串是在创建查询时设置的。不包括参数。</summary>
      <returns>表示此实例的 <see cref="T:System.String" />。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbSqlQuery`1">
      <summary>表示实体的一个 SQL 查询，该查询从 <see cref="T:System.Data.Entity.DbContext" /> 创建并通过该上下文中的连接来执行。此类的实例是从该实体类型的 <see cref="T:System.Data.Entity.DbSet`1" /> 实例中获取的。创建此对象时不会执行该查询；只要枚举该查询，该查询就会执行（例如，使用 foreach）。非实体的 SQL 查询是使用 <see cref="P:System.Data.Entity.DbContext.Database" /> 创建的。请参见 <see cref="T:System.Data.Entity.Infrastructure.DbSqlQuery" /> 了解此类的非泛型版本。</summary>
      <typeparam name="TEntity">实体。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbSqlQuery`1.AsNoTracking">
      <summary>返回一个新查询，其中查询的结果将不由关联的 <see cref="T:System.Data.Entity.DbContext" /> 跟踪。</summary>
      <returns>应用了 no-tracking 的新查询。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbSqlQuery`1.Equals(System.Object)">
      <summary>返回指定的查询是否等于当前查询。</summary>
      <returns>如果指定的查询等于当前查询，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的查询。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbSqlQuery`1.GetEnumerator">
      <summary>执行查询并返回元素的枚举器。</summary>
      <returns>元素的枚举器。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbSqlQuery`1.GetHashCode">
      <summary>返回指定查询的哈希函数。</summary>
      <returns>指定查询的哈希函数。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbSqlQuery`1.GetType">
      <summary>获取当前查询的类型。</summary>
      <returns>当前查询的类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbSqlQuery`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>执行查询并返回元素的枚举器。</summary>
      <returns>一个 <see cref="T:System.Collections.IEnumerator" /> 对象，可用于循环访问元素。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbSqlQuery`1.System#ComponentModel#IListSource#ContainsListCollection">
      <summary>返回 false。</summary>
      <returns>false</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbSqlQuery`1.System#ComponentModel#IListSource#GetList">
      <summary>引发一个异常，该异常指示不支持直接绑定到存储查询。</summary>
      <returns>决不返回；总是引发。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbSqlQuery`1.ToString">
      <summary>返回一个包含 SQL 字符串的 <see cref="T:System.String" />，该字符串是在创建查询时设置的。不包括参数。</summary>
      <returns> 表示此实例的 <see cref="T:System.String" />。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbUpdateConcurrencyException">
      <summary>如果预期的行为是，实体的 SaveChanges 将导致数据库更新，而实际上未影响数据库中的任何行，则表明 <see cref="T:System.Data.Entity.DbContext" /> 引发了异常。这通常指示，当前已并发更新数据库，并且应匹配的并发标记实际上并不匹配。为了安全起见，不会序列化此异常引用的状态项，并且在序列化后对状态项的访问将返回 null。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbUpdateConcurrencyException.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Infrastructure.DbUpdateException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbUpdateConcurrencyException.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Data.Entity.Infrastructure.DbUpdateException" /> 类的新实例。</summary>
      <param name="message">消息。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbUpdateConcurrencyException.#ctor(System.String,System.Exception)">
      <summary>初始化 <see cref="T:System.Data.Entity.Infrastructure.DbUpdateException" /> 类的新实例。</summary>
      <param name="message">消息。</param>
      <param name="innerException">内部异常。</param>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.DbUpdateException">
      <summary>表示在数据库更新过程中遇到的异常。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbUpdateException.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Infrastructure.DbUpdateException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbUpdateException.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Data.Entity.Infrastructure.DbUpdateException" /> 类的新实例。</summary>
      <param name="message">消息。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.DbUpdateException.#ctor(System.String,System.Exception)">
      <summary>初始化 <see cref="T:System.Data.Entity.Infrastructure.DbUpdateException" /> 类的新实例。</summary>
      <param name="message">消息。</param>
      <param name="innerException">内部异常。</param>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.DbUpdateException.Entries">
      <summary>获取表示无法保存到数据库的实体的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" /> 对象。</summary>
      <returns>无法保存到数据库的实体。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.EdmMetadata">
      <summary>表示用于存储有关数据库中 EDM 的元数据的实体。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.EdmMetadata.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Infrastructure.EdmMetadata" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.EdmMetadata.Id">
      <summary>获取或设置元数据实体的标识符，该标识符当前总是为 1。</summary>
      <returns>标识符。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.EdmMetadata.ModelHash">
      <summary>获取或设置模型哈希，用于检查自从模型中创建数据库后，模型是否发生了更改。</summary>
      <returns>模型哈希。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.EdmMetadata.TryGetModelHash(System.Data.Entity.DbContext)">
      <summary>尝试为给定上下文获取 Code First 计算的模型哈希。如果未在 Code First 模式中使用上下文，则此方法将返回 null。</summary>
      <returns>哈希字符串。</returns>
      <param name="context">上下文。</param>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.EdmxWriter">
      <summary>包含用于访问由 Code First 创建的 EDMX 格式的实体数据模型的方法。通常，在需要查看 Code First 内部创建的模型时，这些方法将用于调试。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.EdmxWriter.WriteEdmx(System.Data.Entity.DbContext,System.Xml.XmlWriter)">
      <summary>将 Code First 用于给定上下文，然后以 EDMX 形式将生成的实体数据模型写入给定编写器。此方法只能用于使用 Code First 且内部创建模型的上下文实例。此方法不能用于使用 Database First 或 Model First 创建的上下文、使用预先存在的 <see cref="T:System.Data.Objects.ObjectContext" /> 创建的上下文或使用预先存在的 <see cref="T:System.Data.Entity.Infrastructure.DbCompiledModel" /> 创建的上下文。</summary>
      <param name="context">上下文。</param>
      <param name="writer">编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.EdmxWriter.WriteEdmx(System.Data.Entity.Infrastructure.DbModel,System.Xml.XmlWriter)">
      <summary>将给定的 <see cref="T:System.Data.Entity.Infrastructure.DbModel" /> 表示的实体数据模型以 EDMX 形式写入给定编写器。</summary>
      <param name="model">表示 EDM 的对象。</param>
      <param name="writer">编写器。</param>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.IDbConnectionFactory">
      <summary>此接口的实现用来基于给定的数据库名称创建某个数据库服务器类型的 DbConnection 对象。默认情况下，可在 <see cref="T:System.Data.Entity.Database" /> 类上设置一个实例以便创建所有 <see cref="T:System.Data.Entity.DbContext" /> 对象（不具有连接信息或只有数据库名称或连接字符串）以使用特定类型的数据库服务器。提供了此接口的两个实现：<see cref="T:System.Data.Entity.Infrastructure.SqlConnectionFactory" />，用于创建与 Microsoft SQL Server（包括 EXPRESS 版本）的连接；<see cref="T:System.Data.Entity.Infrastructure.SqlCeConnectionFactory" />，用于创建与 Microsoft SQL Server Compact Edition 的连接。可根据需要添加其他数据库服务器的其他实现。实现应视为是线程安全的或不可变的，因为实现可同时被多个线程访问。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.IDbConnectionFactory.CreateConnection(System.String)">
      <summary>基于给定的数据库名称或连接字符串创建连接。</summary>
      <returns>已初始化的 DbConnection。</returns>
      <param name="nameOrConnectionString">数据库名称或连接字符串。</param>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.IDbContextFactory`1">
      <summary>用于创建派生 <see cref="T:System.Data.Entity.DbContext" /> 实例的工厂。实现此接口可启用设计时服务以及没有公共默认构造函数的上下文类型的实例化。可在设计时创建派生 <see cref="T:System.Data.Entity.DbContext" /> 实例以启用特定设计时体验，如模型呈现和 DDL 生成。设计时服务可自动发现与派生 <see cref="T:System.Data.Entity.DbContext" /> 类型位于同一程序集中的此接口的实现。</summary>
      <typeparam name="TContext">此类型参数是协变式的。也就是说，您可以使用您指定的类型或者派生程度更大的任何类型。有关协变式和逆变式的详细信息，请参阅。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.IDbContextFactory`1.Create">
      <summary>创建派生 <see cref="T:System.Data.Entity.DbContext" /> 类型的新实例。</summary>
      <returns>
        <see cref="TContext" /> 的一个实例。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.IncludeMetadataConvention">
      <summary>此 <see cref="T:System.Data.Entity.DbModelBuilder" /> 约定将导致 <see cref="T:System.Data.Entity.DbModelBuilder" /> 在生成模型时包含有关该模型的元数据。当 <see cref="T:System.Data.Entity.DbContext" /> 按约定创建模型时，它会将此约定添加到由 <see cref="T:System.Data.Entity.DbModelBuilder" /> 使用的项的列表中。如果使用 <see cref="T:System.Data.Entity.DbContext" /> 创建数据库，则会导致将模型元数据写入数据库中。然后，可使用它来验证自上次对数据库使用模型后，模型是否发生了更改。可通过重写 <see cref="M:System.Data.Entity.DbContext.OnModelCreating(System.Data.Entity.DbModelBuilder)" /> 方法从 <see cref="T:System.Data.Entity.DbModelBuilder" /> 约定的列表中删除此约定。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.IncludeMetadataConvention.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Infrastructure.IncludeMetadataConvention" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.IObjectContextAdapter">
      <summary>由可提供 <see cref="P:System.Data.Entity.Infrastructure.IObjectContextAdapter.ObjectContext" /> 实例的对象实现的接口。<see cref="T:System.Data.Entity.DbContext" /> 类实现此接口以提供对基础 ObjectContext 的访问。</summary>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.IObjectContextAdapter.ObjectContext">
      <summary>获取对象上下文。</summary>
      <returns>对象上下文。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.LocalDbConnectionFactory">
      <summary>     此类的实例用来基于给定的数据库名称或连接字符串创建 SQL Server LocalDb 的 DbConnection 对象。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.LocalDbConnectionFactory.#ctor(System.String)">
      <summary>     为给定版本的 LocalDb 创建连接工厂的新实例。对于 SQL Server 2012 LocalDb，请使用“v11.0”。</summary>
      <param name="localDbVersion">要使用的 LocalDb 版本。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.LocalDbConnectionFactory.#ctor(System.String,System.String)">
      <summary>     为给定版本的 LocalDb 创建连接工厂的新实例。对于 SQL Server 2012 LocalDb，请使用“v11.0”。</summary>
      <param name="localDbVersion">要使用的 LocalDb 版本。</param>
      <param name="baseConnectionString">     用于“初始目录”、“数据源”和“AttachDbFilename”之外的数据库选项的连接字符串。调用 CreateConnection 时，将基于数据库名称在此字符串前添加“初始目录”和“AttachDbFilename”。将基于 LocalDbVersion 参数设置“数据源”。</param>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.LocalDbConnectionFactory.BaseConnectionString">
      <summary>     用于“初始目录”、“数据源”和“AttachDbFilename”之外的数据库选项的连接字符串。调用 CreateConnection 时，将基于数据库名称在此字符串前添加“初始目录”和“AttachDbFilename”。将基于 LocalDbVersion 参数设置“数据源”。默认值为“Integrated Security=True; MultipleActiveResultSets=True;”。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.LocalDbConnectionFactory.CreateConnection(System.String)">
      <summary>     基于给定的数据库名称或连接字符串为 SQL Server LocalDb 创建连接。如果给定的字符串包含“=”字符，则将其视为一个完整的连接字符串，否则仅将其视为数据库名称。</summary>
      <returns>已初始化的 DbConnection。</returns>
      <param name="nameOrConnectionString">数据库名称或连接字符串。</param>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.ModelContainerConvention">
      <summary>此 <see cref="T:System.Data.Entity.DbModelBuilder" /> 约定将派生的 <see cref="T:System.Data.Entity.DbContext" /> 类的名称用作由 Code First 生成的概念模型的容器。</summary>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.ModelNamespaceConvention">
      <summary>此 <see cref="T:System.Data.Entity.DbModelBuilder" /> 约定将派生的 <see cref="T:System.Data.Entity.DbContext" /> 类的命名空间用作由 Code First 生成的概念模型的命名空间。</summary>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.ReplacementDbQueryWrapper`1">
      <summary>此类的实例用于内部创建常量表达式，这些表达式将插入表达式目录树中以替换对 <see cref="T:System.Data.Entity.Infrastructure.DbQuery`1" /> 和 <see cref="T:System.Data.Entity.Infrastructure.DbQuery" /> 的引用。</summary>
      <typeparam name="TElement">元素的类型。</typeparam>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.ReplacementDbQueryWrapper`1.Query">
      <summary>LINQ 表达式目录树中应有的公共属性。</summary>
      <returns>返回 <see cref="T:System.Data.Objects.ObjectQuery`1" />。查询。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.SqlCeConnectionFactory">
      <summary>此类的实例用来基于给定的数据库名称或连接字符串创建 SQL Server Compact Edition 的 DbConnection 对象。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.SqlCeConnectionFactory.#ctor(System.String)">
      <summary> 使用空的（默认） <see cref="P:System.Data.Entity.Infrastructure.SqlCeConnectionFactory.DatabaseDirectory" /> 和 <see cref="P:System.Data.Entity.Infrastructure.SqlCeConnectionFactory.BaseConnectionString" /> 属性创建新的连接工厂。</summary>
      <param name="providerInvariantName">应使用指定 SQL Server Compact Edition 版本的提供程序固定名称。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.SqlCeConnectionFactory.#ctor(System.String,System.String,System.String)">
      <summary>使用给定的 <see cref="P:System.Data.Entity.Infrastructure.SqlCeConnectionFactory.DatabaseDirectory" /> 和 <see cref="P:System.Data.Entity.Infrastructure.SqlCeConnectionFactory.BaseConnectionString" /> 属性创建新的连接工厂。</summary>
      <param name="providerInvariantName">应使用指定 SQL Server Compact Edition 版本的提供程序固定名称。</param>
      <param name="databaseDirectory">用于添加到数据库名称前面的路径，SQL Server Compact Edition 在创建或读取数据库文件时将使用它来构成文件名。空字符串会导致 SQL Server Compact Edition 将其默认位置用作数据库文件位置。</param>
      <param name="baseConnectionString">要用于“数据源”之外的数据库的选项的连接字符串。在调用 CreateConnection 时，将基于数据库名称在此字符串前预置数据源。</param>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.SqlCeConnectionFactory.BaseConnectionString">
      <summary>要用于“数据源”之外的数据库的选项的连接字符串。在调用 CreateConnection 时，将基于数据库名称在此字符串前预置数据源。默认值为空字符串，即表示不使用任何其他选项。</summary>
      <returns>使用的连接字符串。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.SqlCeConnectionFactory.CreateConnection(System.String)">
      <summary>基于给定的数据库名称或连接字符串为 SQL Server Compact Edition 创建连接。如果给定的字符串包含“=”字符，则将其视为一个完整的连接字符串，否则仅将其视为数据库名称。</summary>
      <returns>已初始化的 DbConnection。</returns>
      <param name="nameOrConnectionString">数据库名称或连接字符串。</param>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.SqlCeConnectionFactory.DatabaseDirectory">
      <summary>用于添加到数据库名称前面的路径，SQL Server Compact Edition 在创建或读取数据库文件时将使用它来构成文件名。默认值为“|DataDirectory|”，即表示文件将放置在指定的数据目录中。</summary>
      <returns>数据库的路径。</returns>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.SqlCeConnectionFactory.ProviderInvariantName">
      <summary>应使用指定 SQL Server Compact Edition 版本的提供程序固定名称。</summary>
      <returns>提供程序固定名称。</returns>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.SqlConnectionFactory">
      <summary> 此类的实例用来基于给定的数据库名称或连接字符串创建 SQL Server 的 DbConnection 对象。默认情况下，将建立与“.\SQLEXPRESS”的连接。在构造工厂实例时，可通过更改基础连接字符串来更改此设置。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.SqlConnectionFactory.#ctor">
      <summary>使用“Data Source=.\SQLEXPRESS; Integrated Security=True; MultipleActiveResultSets=True”的默认 BaseConnectionString 属性创建新的连接工厂。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.SqlConnectionFactory.#ctor(System.String)">
      <summary>使用给定的 BaseConnectionString 属性创建新的连接工厂。</summary>
      <param name="baseConnectionString">要用于“初始目录”之外的数据库的选项的连接字符串。在调用 CreateConnection 时，将基于数据库名称在此字符串前预置“初始目录”。</param>
    </member>
    <member name="P:System.Data.Entity.Infrastructure.SqlConnectionFactory.BaseConnectionString">
      <summary>要用于“初始目录”之外的数据库的选项的连接字符串。在调用 CreateConnection 时，将基于数据库名称在此字符串前预置“初始目录”。默认值为“Data Source=.\SQLEXPRESS; Integrated Security=True; MultipleActiveResultSets=True”。</summary>
      <returns>连接字符串。</returns>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.SqlConnectionFactory.CreateConnection(System.String)">
      <summary>基于给定的数据库名称或连接字符串为 SQL Server 创建连接。如果给定的字符串包含“=”字符，则将其视为一个完整的连接字符串，否则仅将其视为数据库名称。</summary>
      <returns>已初始化的 DbConnection。</returns>
      <param name="nameOrConnectionString">数据库名称或连接字符串。</param>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.SuppressDbSetInitializationAttribute">
      <summary>此特性可应用于整个派生的 <see cref="T:System.Data.Entity.DbContext" /> 类或该类上的单个 <see cref="T:System.Data.Entity.DbSet`1" /> 或 <see cref="T:System.Data.Entity.IDbSet`1" /> 属性。应用此特性后，任何发现的 <see cref="T:System.Data.Entity.DbSet`1" /> 或 <see cref="T:System.Data.Entity.IDbSet`1" /> 属性仍将包含在模型中，但将不再自动进行初始化。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.SuppressDbSetInitializationAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Infrastructure.SuppressDbSetInitializationAttribute" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Data.Entity.Infrastructure.UnintentionalCodeFirstException">
      <summary>当上下文从 Database First 或 Model First 模式中的 <see cref="T:System.Data.Entity.DbContext" /> 模板生成，并在 Code First 模式中使用时将引发异常。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.UnintentionalCodeFirstException.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Infrastructure.UnintentionalCodeFirstException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.UnintentionalCodeFirstException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>初始化 <see cref="T:System.Data.Entity.Infrastructure.UnintentionalCodeFirstException" /> 类的新实例。</summary>
      <param name="info">保存序列化对象数据的对象。</param>
      <param name="context">有关源或目标的上下文信息。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.UnintentionalCodeFirstException.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Data.Entity.Infrastructure.UnintentionalCodeFirstException" /> 类的新实例。</summary>
      <param name="message">消息。</param>
    </member>
    <member name="M:System.Data.Entity.Infrastructure.UnintentionalCodeFirstException.#ctor(System.String,System.Exception)">
      <summary>初始化 <see cref="T:System.Data.Entity.Infrastructure.UnintentionalCodeFirstException" /> 类的新实例。</summary>
      <param name="message">消息。</param>
      <param name="innerException">内部异常。</param>
    </member>
    <member name="T:System.Data.Entity.Migrations.DbMigration">
      <summary>表示基于代码的迁移的基类。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Migrations.DbMigration" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.AddColumn(System.String,System.String,System.Func{System.Data.Entity.Migrations.Builders.ColumnBuilder,System.Data.Entity.Migrations.Model.ColumnModel},System.Object)">
      <summary>     添加将列添加到现有表的操作。</summary>
      <param name="table">     列将添加到的表的名称。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="name">     要添加的列的名称。</param>
      <param name="columnAction">     指定要添加的列的操作。     即 c =&amp;gt; c.Int(nullable: false, defaultValue: 3)。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.AddForeignKey(System.String,System.String,System.String,System.String,System.Boolean,System.String,System.Object)">
      <summary>     添加创建新外键约束的操作。</summary>
      <param name="dependentTable">     包含外键列的表。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="dependentColumn">外键列。</param>
      <param name="principalTable">     包含此外键引用的列的表。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="principalColumn">     此外键引用的列。如果未提供任何值，则将引用主体表的主键。</param>
      <param name="cascadeDelete">     指示是否应针对外键关系配置级联删除的值。如果未提供任何值，则将关闭级联删除。</param>
      <param name="name">     数据库中的外键约束的名称。如果未提供任何值，则将生成唯一名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.AddForeignKey(System.String,System.String[],System.String,System.String[],System.Boolean,System.String,System.Object)">
      <summary>     添加创建新外键约束的操作。</summary>
      <param name="dependentTable">     包含外键列的表。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="dependentColumns">外键列。</param>
      <param name="principalTable">     包含此外键引用的列的表。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="principalColumns">     此外键引用的列。如果未提供任何值，则将引用主体表的主键。</param>
      <param name="cascadeDelete">     指示是否应针对外键关系配置级联删除的值。如果未提供任何值，则将关闭级联删除。</param>
      <param name="name">     数据库中的外键约束的名称。如果未提供任何值，则将生成唯一名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.AddPrimaryKey(System.String,System.String,System.String,System.Object)">
      <summary>     添加创建新主键的操作。</summary>
      <param name="table">     包含主键列的表。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="column">主键列。</param>
      <param name="name">     数据库中的主键的名称。如果未提供任何值，则将生成唯一名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.AddPrimaryKey(System.String,System.String[],System.String,System.Object)">
      <summary>     添加基于多列创建新主键的操作。</summary>
      <param name="table">     包含主键列的表。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="columns">主键列。</param>
      <param name="name">     数据库中的主键的名称。如果未提供任何值，则将生成唯一名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.AlterColumn(System.String,System.String,System.Func{System.Data.Entity.Migrations.Builders.ColumnBuilder,System.Data.Entity.Migrations.Model.ColumnModel},System.Object)">
      <summary>     添加更改现有列的定义的操作。</summary>
      <param name="table">     列所在表的名称。     架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="name">要更改的列的名称。</param>
      <param name="columnAction">     指定列的新定义的操作。     即 c =&amp;gt; c.String(nullable: false, defaultValue: "none")。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.CreateIndex(System.String,System.String,System.Boolean,System.String,System.Object)">
      <summary>     添加在单列上创建索引的操作。</summary>
      <param name="table">     要在其上创建索引的表的名称。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="column">要在其上创建索引的列的名称。</param>
      <param name="unique">     指示此索引是否是唯一索引的值。如果未提供任何值，则将创建非唯一索引。</param>
      <param name="name">     用于数据库中的索引的名称。如果未提供任何值，则将生成唯一名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.CreateIndex(System.String,System.String[],System.Boolean,System.String,System.Object)">
      <summary>     添加在多列上创建索引的操作。</summary>
      <param name="table">     要在其上创建索引的表的名称。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="columns">要在其上创建索引的列的名称。</param>
      <param name="unique">     指示此索引是否是唯一索引的值。如果未提供任何值，则将创建非唯一索引。</param>
      <param name="name">     用于数据库中的索引的名称。如果未提供任何值，则将生成唯一名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.CreateTable``1(System.String,System.Func{System.Data.Entity.Migrations.Builders.ColumnBuilder,``0},System.Object)">
      <summary>     添加创建新表的操作。</summary>
      <returns>允许进一步配置表创建操作的对象。</returns>
      <param name="name">表的名称。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="columnsAction">     指定要包含在表中的列的操作。     即 t =&amp;gt; new { Id = t.Int(identity: true), Name = t.String() }。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
      <typeparam name="TColumns">     此表中的列创建表操作。无需指定此类型，将根据您提供的 columnsAction 参数推断此类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.Down">
      <summary>     要在降级过程中执行的操作。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.DropColumn(System.String,System.String,System.Object)">
      <summary>     添加删除现有列的操作。</summary>
      <param name="table">     要从中删除列的表的名称。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="name">要删除的列的名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.DropForeignKey(System.String,System.String,System.Object)">
      <summary>     添加基于外键约束名称删除外键约束的操作。</summary>
      <param name="dependentTable">     包含外键列的表。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="name">数据库中的外键约束的名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.DropForeignKey(System.String,System.String,System.String,System.String,System.Object)">
      <summary>     添加基于外键约束面向的列删除外键约束的操作。</summary>
      <param name="dependentTable">     包含外键列的表。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="dependentColumn">外键列。</param>
      <param name="principalTable">     包含此外键引用的列的表。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="principalColumn">此外键引用的列。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.DropForeignKey(System.String,System.String[],System.String,System.Object)">
      <summary>     添加基于外键约束面向的列删除外键约束的操作。</summary>
      <param name="dependentTable">     包含外键列的表。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="dependentColumns">外键列。</param>
      <param name="principalTable">     包含此外键引用的列的表。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.DropIndex(System.String,System.String,System.Object)">
      <summary>     添加基于索引名称删除索引的操作。</summary>
      <param name="table">     要从中删除索引的表的名称。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="name">要删除的索引的名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.DropIndex(System.String,System.String[],System.Object)">
      <summary>     添加基于索引面向的列删除索引的操作。</summary>
      <param name="table">     要从中删除索引的表的名称。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="columns">索引面向的列的名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.DropPrimaryKey(System.String,System.Object)">
      <summary>     添加删除使用默认名称创建的现有主键的操作。</summary>
      <param name="table">     包含主键列的表。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.DropPrimaryKey(System.String,System.String,System.Object)">
      <summary>     添加删除没有默认名称的现有主键的操作。</summary>
      <param name="table">     包含主键列的表。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="name">要删除的主键的名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.DropTable(System.String,System.Object)">
      <summary>     添加删除表的操作。</summary>
      <param name="name">     要删除的表的名称。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.Equals(System.Object)">
      <summary>确定此实例是否等于指定对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 True；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的对象。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.GetHashCode">
      <summary>以指定类型获取参数或列的数据。</summary>
      <returns>当前对象的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.GetType">
      <summary>返回当前实例的运行时类型。</summary>
      <returns>Type 实例，它表示当前实例确切的运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.MemberwiseClone">
      <summary>创建当前对象的浅表副本。</summary>
      <returns>当前对象的浅表副本。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.MoveTable(System.String,System.String,System.Object)">
      <summary>     添加将表移至新架构的操作。</summary>
      <param name="name">     要移动的表的名称。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="newSchema">表将移至的架构。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.RenameColumn(System.String,System.String,System.String,System.Object)">
      <summary>     添加重命名列的操作。</summary>
      <param name="table">     包含要重命名的列的表的名称。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="name">要重命名的列的名称。</param>
      <param name="newName">列的新名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.RenameTable(System.String,System.String,System.Object)">
      <summary>     添加重命名表的操作。若要更改表的架构，请使用 MoveTable。</summary>
      <param name="name">     要重命名的表的名称。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="newName">     表的新名称。架构名称是可选的，如果未指定架构，则将假定 dbo。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.Sql(System.String,System.Boolean,System.Object)">
      <summary>     添加执行 SQL 命令的操作。</summary>
      <param name="sql">要执行的 SQL。</param>
      <param name="suppressTransaction">     指示是否应在用于迁移过程的事务之外执行 SQL 的值。如果未提供任何值，则将在事务内执行 SQL。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.ToString">
      <summary>返回表示当前对象的字符串。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigration.Up">
      <summary>     要在升级过程中执行的操作。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.DbMigrationsConfiguration">
      <summary>与对给定模型使用迁移相关的配置。通常将创建派生自 <see cref="T:System.Data.Entity.Migrations.DbMigrationsConfiguration`1" /> 的配置类，而不是使用此类。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigrationsConfiguration.#ctor">
      <summary>     初始化 DbMigrationsConfiguration 类的新实例。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.DbMigrationsConfiguration.AutomaticMigrationDataLossAllowed">
      <summary>     获取或设置指示是否可接受自动迁移期间的数据丢失的值。如果设置为 false，则将在数据丢失可能作为自动迁移一部分出现时引发异常。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.DbMigrationsConfiguration.AutomaticMigrationsEnabled">
      <summary>     获取或设置指示迁移数据库时是否可使用自动迁移的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.DbMigrationsConfiguration.CodeGenerator">
      <summary>     获取或设置为迁移搭建基架时要使用的代码生成器。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.DbMigrationsConfiguration.ContextType">
      <summary>     获取或设置表示要迁移的模型的派生 DbContext。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigrationsConfiguration.GetSqlGenerator(System.String)">
      <summary>     获取设置为用于给定数据库提供程序的 SQL 生成器。</summary>
      <returns>为数据库提供程序设置的 SQL 生成器。</returns>
      <param name="providerInvariantName">要为之获取 SQL 生成器的数据库提供程序的名称。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.DbMigrationsConfiguration.MigrationsAssembly">
      <summary>     获取或设置包含基于代码的迁移的程序集。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.DbMigrationsConfiguration.MigrationsDirectory">
      <summary>     获取或设置其中存储基于代码的迁移的子目录。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.DbMigrationsConfiguration.MigrationsNamespace">
      <summary>     获取或设置用于基于代码的迁移的命名空间。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigrationsConfiguration.SetSqlGenerator(System.String,System.Data.Entity.Migrations.Sql.MigrationSqlGenerator)">
      <summary>     添加新的要同于给定数据库提供程序的 SQL 生成器。</summary>
      <param name="providerInvariantName">为之设置 SQL 生成器的数据库提供程序的名称。</param>
      <param name="migrationSqlGenerator">要使用的 SQL 生成器。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.DbMigrationsConfiguration.TargetDatabase">
      <summary>     获取或设置用于重写要迁移的数据库的连接的值。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.DbMigrationsConfiguration`1">
      <summary>表示与对给定模型使用迁移相关的配置。</summary>
      <typeparam name="TContext">上下文。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigrationsConfiguration`1.#ctor">
      <summary>     初始化 <see cref="T:System.Data.Entity.Migrations.DbMigrationsConfiguration`1" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigrationsConfiguration`1.Equals(System.Object)">
      <summary>返回一个值，该值指示此实例是否与指定的对象相等。</summary>
      <returns>如果指定的对象等于当前对象，则为 True；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的对象。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigrationsConfiguration`1.GetHashCode">
      <summary>以指定类型获取参数或列的数据。</summary>
      <returns>当前对象的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigrationsConfiguration`1.GetType">
      <summary>返回当前实例的运行时类型。</summary>
      <returns>Type 实例，它表示当前实例确切的运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigrationsConfiguration`1.MemberwiseClone">
      <summary>创建当前对象的浅表副本。</summary>
      <returns>当前对象的浅表副本。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigrationsConfiguration`1.Seed(`0)">
      <summary>     在升级到最新迁移以允许更新种子数据后运行。</summary>
      <param name="context">要用于更新种子数据的上下文。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigrationsConfiguration`1.ToString">
      <summary>返回表示当前对象的字符串。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.DbMigrator">
      <summary>用于将现有迁移应用于数据库。此类可用于到任何给定迁移的升级和降级。若要基于对模型的更改为迁移搭建基架，请使用 <see cref="T:System.Data.Entity.Migrations.Design.MigrationScaffolder" />。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigrator.#ctor(System.Data.Entity.Migrations.DbMigrationsConfiguration)">
      <summary>     初始化 DbMigrator 类的新实例。</summary>
      <param name="configuration">用于迁移过程的配置。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.DbMigrator.Configuration">
      <summary>     获取要用于迁移过程的配置。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigrator.GetDatabaseMigrations">
      <summary>     获取已应用于目标数据库的所有迁移。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigrator.GetLocalMigrations">
      <summary>     获取已在配置的迁移程序集中定义的所有迁移。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigrator.GetPendingMigrations">
      <summary>     获取已在程序集中定义但尚未应用于目标数据库的所有迁移。</summary>
    </member>
    <member name="F:System.Data.Entity.Migrations.DbMigrator.InitialDatabase">
      <summary>     表示应用任何迁移前数据库的状态的迁移 Id。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.DbMigrator.Update(System.String)">
      <summary>     将目标数据库更新到给定迁移。</summary>
      <param name="targetMigration">要升级/降级到的迁移。</param>
    </member>
    <member name="T:System.Data.Entity.Migrations.IDbSetExtensions">
      <summary>
        <see cref="T:System.Data.Entity.IDbSet`1" /> 的一组扩展方法。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.IDbSetExtensions.AddOrUpdate``1(System.Data.Entity.IDbSet{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Object}},``0[])">
      <summary>调用 SaveChanges 时，按键添加或更新实体。等效于数据库术语中的“upsert”操作。此方法在使用迁移设置数据的种子时很有用。</summary>
      <param name="set">用于执行创建、读取、更新和删除操作的 <see cref="T:System.Data.Entity.IDbSet`1" /> 对象。</param>
      <param name="identifierExpression">指定在确定是应执行添加操作还是更新操作时应使用的属性的表达式。</param>
      <param name="entities">要添加或更新的实体。</param>
      <typeparam name="TEntity">实体的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Migrations.IDbSetExtensions.AddOrUpdate``1(System.Data.Entity.IDbSet{``0},``0[])">
      <summary>调用 SaveChanges 时，按键添加或更新实体。等效于数据库术语中的“upsert”操作。此方法在使用迁移设置数据的种子时很有用。</summary>
      <param name="set">用于执行创建、读取、更新和删除操作的 <see cref="T:System.Data.Entity.IDbSet`1" /> 对象。</param>
      <param name="entities">要添加或更新的实体。</param>
      <typeparam name="TEntity">实体的类型。</typeparam>
    </member>
    <member name="T:System.Data.Entity.Migrations.Builders.ColumnBuilder">
      <summary>表示用于配置列的帮助器类。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Migrations.Builders.ColumnBuilder" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.Binary(System.Nullable{System.Boolean},System.Nullable{System.Int32},System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.Byte[],System.String,System.Boolean,System.String,System.String)">
      <summary>创建新的列定义以存储二进制数据。</summary>
      <returns>新构造的列定义。</returns>
      <param name="nullable">指示列是否允许 null 值的值。</param>
      <param name="maxLength">数组数据的最大允许长度。</param>
      <param name="fixedLength">指示是否应将所有数据填充至最大长度的值。</param>
      <param name="isMaxLength">指示是否应使用数据库提供程序支持的最大长度的值。</param>
      <param name="defaultValue">用作此列的默认值的常量值。</param>
      <param name="defaultValueSql">用作此列的默认值的 SQL 表达式。</param>
      <param name="timestamp">指示是否应将此列配置为时间戳的值。</param>
      <param name="name">列的名称。</param>
      <param name="storeType">用于此列的提供程序特定数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.Boolean(System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.String,System.String,System.String)">
      <summary>创建新的列定义以存储布尔数据。</summary>
      <returns>新构造的列定义。</returns>
      <param name="nullable">指示列是否允许 null 值的值。</param>
      <param name="defaultValue">用作此列的默认值的常量值。</param>
      <param name="defaultValueSql">用作此列的默认值的 SQL 表达式。</param>
      <param name="name">列的名称。</param>
      <param name="storeType">用于此列的提供程序特定数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.Byte(System.Nullable{System.Boolean},System.Boolean,System.Nullable{System.Byte},System.String,System.String,System.String)">
      <summary>创建新的列定义以存储字节数据。</summary>
      <returns>新构造的列定义。</returns>
      <param name="nullable">指示列是否允许 null 值的值。</param>
      <param name="identity">指示插入过程中数据库是否会为此列生成值的值。</param>
      <param name="defaultValue">用作此列的默认值的常量值。</param>
      <param name="defaultValueSql">用作此列的默认值的 SQL 表达式。</param>
      <param name="name">列的名称。</param>
      <param name="storeType">用于此列的提供程序特定数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.DateTime(System.Nullable{System.Boolean},System.Nullable{System.Byte},System.Nullable{System.DateTime},System.String,System.String,System.String)">
      <summary>创建新的列定义以存储 DateTime 数据。</summary>
      <returns>新构造的列定义。</returns>
      <param name="nullable">指示列是否允许 null 值的值。</param>
      <param name="precision">列的精度。</param>
      <param name="defaultValue">用作此列的默认值的常量值。</param>
      <param name="defaultValueSql">用作此列的默认值的 SQL 表达式。</param>
      <param name="name">列的名称。</param>
      <param name="storeType">用于此列的提供程序特定数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.DateTimeOffset(System.Nullable{System.Boolean},System.Nullable{System.Byte},System.Nullable{System.DateTimeOffset},System.String,System.String,System.String)">
      <summary>创建新的列定义以存储 DateTimeOffset 数据。</summary>
      <returns>新构造的列定义。</returns>
      <param name="nullable">指示列是否允许 null 值的值。</param>
      <param name="precision">列的精度。</param>
      <param name="defaultValue">用作此列的默认值的常量值。</param>
      <param name="defaultValueSql">用作此列的默认值的 SQL 表达式。</param>
      <param name="name">列的名称。</param>
      <param name="storeType">用于此列的提供程序特定数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.Decimal(System.Nullable{System.Boolean},System.Nullable{System.Byte},System.Nullable{System.Byte},System.Nullable{System.Decimal},System.String,System.String,System.String,System.Boolean)">
      <summary>创建新的列定义以存储 Decimal 数据。</summary>
      <returns>新构造的列定义。</returns>
      <param name="nullable">指示列是否允许 null 值的值。</param>
      <param name="precision">列的数值精度。</param>
      <param name="scale">列的数值刻度。</param>
      <param name="defaultValue">用作此列的默认值的常量值。</param>
      <param name="defaultValueSql">用作此列的默认值的 SQL 表达式。</param>
      <param name="name">列的名称。</param>
      <param name="storeType">用于此列的提供程序特定数据类型。</param>
      <param name="identity">指示插入过程中数据库是否会为此列生成值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.Double(System.Nullable{System.Boolean},System.Nullable{System.Double},System.String,System.String,System.String)">
      <summary>创建新的列定义以存储 Double 数据。</summary>
      <returns>新构造的列定义。</returns>
      <param name="nullable">指示列是否允许 null 值的值。</param>
      <param name="defaultValue">用作此列的默认值的常量值。</param>
      <param name="defaultValueSql">用作此列的默认值的 SQL 表达式。</param>
      <param name="name">列的名称。</param>
      <param name="storeType">用于此列的提供程序特定数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.Equals(System.Object)">
      <summary>指示指定的对象是否等于当前对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的对象。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.Geography(System.Nullable{System.Boolean},System.Data.Spatial.DbGeography,System.String,System.String,System.String)">
      <summary>创建新的列定义以存储 geography 数据。</summary>
      <returns>新构造的列定义。</returns>
      <param name="nullable">指示列是否允许 null 值的值。</param>
      <param name="defaultValue">用作此列的默认值的常量值。</param>
      <param name="defaultValueSql">用作此列的默认值的 SQL 表达式。</param>
      <param name="name">列的名称。</param>
      <param name="storeType">用于此列的提供程序特定数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.Geometry(System.Nullable{System.Boolean},System.Data.Spatial.DbGeometry,System.String,System.String,System.String)">
      <summary>创建新的列定义以存储 geometry 数据。</summary>
      <returns>新构造的列定义。</returns>
      <param name="nullable">指示列是否允许 null 值的值。</param>
      <param name="defaultValue">用作此列的默认值的常量值。</param>
      <param name="defaultValueSql">用作此列的默认值的 SQL 表达式。</param>
      <param name="name">列的名称。</param>
      <param name="storeType">用于此列的提供程序特定数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.GetHashCode">
      <summary>返回此实例的哈希代码。</summary>
      <returns>此实例的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.GetType">
      <summary>获取当前实例的类型。</summary>
      <returns>当前实例的类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.Guid(System.Nullable{System.Boolean},System.Boolean,System.Nullable{System.Guid},System.String,System.String,System.String)">
      <summary>创建新的列定义以存储 GUID 数据。</summary>
      <returns>新构造的列定义。</returns>
      <param name="nullable">指示列是否允许 null 值的值。</param>
      <param name="identity">指示插入过程中数据库是否会为此列生成值的值。</param>
      <param name="defaultValue">用作此列的默认值的常量值。</param>
      <param name="defaultValueSql">用作此列的默认值的 SQL 表达式。</param>
      <param name="name">列的名称。</param>
      <param name="storeType">用于此列的提供程序特定数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.Int(System.Nullable{System.Boolean},System.Boolean,System.Nullable{System.Int32},System.String,System.String,System.String)">
      <summary>创建新的列定义以存储 Integer 数据。</summary>
      <returns>新构造的列定义。</returns>
      <param name="nullable">指示列是否允许 null 值的值。</param>
      <param name="identity">指示插入过程中数据库是否会为此列生成值的值。</param>
      <param name="defaultValue">用作此列的默认值的常量值。</param>
      <param name="defaultValueSql">用作此列的默认值的 SQL 表达式。</param>
      <param name="name">列的名称。</param>
      <param name="storeType">用于此列的提供程序特定数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.Long(System.Nullable{System.Boolean},System.Boolean,System.Nullable{System.Int64},System.String,System.String,System.String)">
      <summary>创建新的列定义以存储 Long 数据。</summary>
      <returns>新构造的列定义。</returns>
      <param name="nullable">指示列是否允许 null 值的值。</param>
      <param name="identity">指示插入过程中数据库是否会为此列生成值的值。</param>
      <param name="defaultValue">用作此列的默认值的常量值。</param>
      <param name="defaultValueSql">用作此列的默认值的 SQL 表达式。</param>
      <param name="name">列的名称。</param>
      <param name="storeType">用于此列的提供程序特定数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.MemberwiseClone">
      <summary>创建当前对象的浅表副本。</summary>
      <returns>当前对象的浅表副本。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.Short(System.Nullable{System.Boolean},System.Boolean,System.Nullable{System.Int16},System.String,System.String,System.String)">
      <summary>创建新的列定义以存储 Short 数据。</summary>
      <returns>新构造的列定义。</returns>
      <param name="nullable">指示列是否允许 null 值的值。</param>
      <param name="identity">指示插入过程中数据库是否会为此列生成值的值。</param>
      <param name="defaultValue">用作此列的默认值的常量值。</param>
      <param name="defaultValueSql">用作此列的默认值的 SQL 表达式。</param>
      <param name="name">列的名称。</param>
      <param name="storeType">用于此列的提供程序特定数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.Single(System.Nullable{System.Boolean},System.Nullable{System.Single},System.String,System.String,System.String)">
      <summary>创建新的列定义以存储 Single 数据。</summary>
      <returns>新构造的列定义。</returns>
      <param name="nullable">指示列是否允许 null 值的值。</param>
      <param name="defaultValue">用作此列的默认值的常量值。</param>
      <param name="defaultValueSql">用作此列的默认值的 SQL 表达式。</param>
      <param name="name">列的名称。</param>
      <param name="storeType">用于此列的提供程序特定数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.String(System.Nullable{System.Boolean},System.Nullable{System.Int32},System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.String,System.String,System.String,System.String)">
      <summary>创建新的列定义以存储字符串数据。</summary>
      <returns>新构造的列定义。</returns>
      <param name="nullable">指示列是否允许 null 值的值。</param>
      <param name="maxLength">字符串数据的最大允许长度。</param>
      <param name="fixedLength">指示是否应将所有数据填充至最大长度的值。</param>
      <param name="isMaxLength">指示是否应使用数据库提供程序支持的最大长度的值。</param>
      <param name="unicode">指示列是否支持 Unicode 内容的值。</param>
      <param name="defaultValue">用作此列的默认值的常量值。</param>
      <param name="defaultValueSql">用作此列的默认值的 SQL 表达式。</param>
      <param name="name">列的名称。</param>
      <param name="storeType">用于此列的提供程序特定数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.Time(System.Nullable{System.Boolean},System.Nullable{System.Byte},System.Nullable{System.TimeSpan},System.String,System.String,System.String)">
      <summary>创建新的列定义以存储 Time 数据。</summary>
      <returns>新构造的列定义。</returns>
      <param name="nullable">指示列是否允许 null 值的值。</param>
      <param name="precision">列的精度。</param>
      <param name="defaultValue">用作此列的默认值的常量值。</param>
      <param name="defaultValueSql">用作此列的默认值的 SQL 表达式。</param>
      <param name="name">列的名称。</param>
      <param name="storeType">用于此列的提供程序特定数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.ColumnBuilder.ToString">
      <summary>返回表示当前对象的字符串。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.Builders.TableBuilder`1">
      <summary>用于进一步配置从 <see cref="T:System.Data.Entity.Migrations.DbMigration" /> 上的 CreateTable 调用创建的表的帮助器类。</summary>
      <typeparam name="TColumns">表中列的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.TableBuilder`1.#ctor(System.Data.Entity.Migrations.Model.CreateTableOperation,System.Data.Entity.Migrations.DbMigration)">
      <summary>初始化 <see cref="T:System.Data.Entity.Migrations.Builders.TableBuilder`1" /> 类的新实例。</summary>
      <param name="createTableOperation">要进一步配置的表创建操作。</param>
      <param name="migration">其中创建了表的迁移。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.TableBuilder`1.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的对象。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.TableBuilder`1.ForeignKey(System.String,System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Boolean,System.String,System.Object)">
      <summary>指定要在表上创建的外键约束。</summary>
      <returns>对象自身，以便多个调用可以链接在一起。</returns>
      <param name="principalTable">该外键约束面向的表的名称。</param>
      <param name="dependentKeyExpression">表示外键属性的 lambda 表达式。如果外键由多个属性组成，则请指定包括这些属性的匿名类型。</param>
      <param name="cascadeDelete">指示是否应在外键约束上配置级联删除的值。</param>
      <param name="name">此外键约束的名称。如果未提供名称，则将计算默认名称。</param>
      <param name="anonymousArguments">提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.TableBuilder`1.GetHashCode">
      <summary>获取当前对象的哈希代码。</summary>
      <returns>当前对象的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.TableBuilder`1.GetType">
      <summary>获取当前对象的类型。</summary>
      <returns>当前对象的类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.TableBuilder`1.Index(System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Boolean,System.Object)">
      <summary>指定要在表上创建的索引。</summary>
      <returns>对象自身，以便多个调用可以链接在一起。</returns>
      <param name="indexExpression">表示要编制索引的属性的 lambda 表达式。如果要对外键属性编制索引，则请指定包括这些属性的匿名类型。</param>
      <param name="unique">指示此索引是否是唯一索引的值。</param>
      <param name="anonymousArguments">提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.TableBuilder`1.MemberwiseClone">
      <summary>创建当前对象的浅表副本。</summary>
      <returns>当前对象的浅表副本。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.TableBuilder`1.PrimaryKey(System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.String,System.Object)">
      <summary>指定表的主键。</summary>
      <returns>对象自身，以便多个调用可以链接在一起。</returns>
      <param name="keyExpression">一个 lambda 表达式，表示要用作主键的属性。如果主键由多个属性组成，则指定一个包括这些属性的匿名类型。</param>
      <param name="name">主键的名称。如果未提供，则将生成默认名称。</param>
      <param name="anonymousArguments">提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Builders.TableBuilder`1.ToString">
      <summary>返回当前对象的字符串表示形式。</summary>
      <returns>当前对象的字符串表示形式。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator">
      <summary>为基于代码的迁移生成 C# 代码。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Byte)">
      <summary>生成代码以指定 <see cref="T:System.Byte" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Byte[])">
      <summary>生成代码以指定 <see cref="T:System.Byte[]" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Collections.Generic.IEnumerable{System.Data.Entity.Migrations.Model.MigrationOperation},System.String,System.String)">
      <summary>生成用户可查看和编辑的主代码文件。</summary>
      <returns>生成的代码。</returns>
      <param name="operations">迁移要执行的操作。</param>
      <param name="namespace">其中应生成代码的命名空间。</param>
      <param name="className">应生成的类的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Collections.Generic.IEnumerable{System.String},System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以指定使用 lambda 表达式的一组列名称。</summary>
      <param name="columns">要为之生成代码的列。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.AddColumnOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.AddColumnOperation" />。</summary>
      <param name="addColumnOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.AddForeignKeyOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.AddForeignKeyOperation" />。</summary>
      <param name="addForeignKeyOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.AddPrimaryKeyOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.AddPrimaryKeyOperation" />。</summary>
      <param name="addPrimaryKeyOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.AlterColumnOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.AlterColumnOperation" />。</summary>
      <param name="alterColumnOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.ColumnModel,System.Data.Entity.Migrations.Utilities.IndentedTextWriter,System.Boolean)">
      <summary>生成代码以指定 <see cref="T:System.Data.Entity.Migrations.Model.ColumnModel" /> 的定义。</summary>
      <param name="column">要为之生成代码的列定义。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
      <param name="emitName">指示定义中是否包含列名称的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.CreateIndexOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.CreateIndexOperation" />。</summary>
      <param name="createIndexOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.CreateTableOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.CreateTableOperation" />。</summary>
      <param name="createTableOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.DropColumnOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.DropColumnOperation" />。</summary>
      <param name="dropColumnOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.DropForeignKeyOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.DropForeignKeyOperation" />。</summary>
      <param name="dropForeignKeyOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.DropIndexOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.DropIndexOperation" />。</summary>
      <param name="dropIndexOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.DropPrimaryKeyOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.DropPrimaryKeyOperation" />。</summary>
      <param name="dropPrimaryKeyOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.DropTableOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.DropTableOperation" />。</summary>
      <param name="dropTableOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.MoveTableOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.MoveTableOperation" />。</summary>
      <param name="moveTableOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.RenameColumnOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.RenameColumnOperation" />。</summary>
      <param name="renameColumnOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.RenameTableOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.RenameTableOperation" />。</summary>
      <param name="renameTableOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.SqlOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.SqlOperation" />。</summary>
      <param name="sqlOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Spatial.DbGeography)">
      <summary>生成代码以指定 <see cref="T:System.Data.Spatial.DbGeography" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Data.Spatial.DbGeometry)">
      <summary>生成代码以指定 <see cref="T:System.Data.Spatial.DbGeometry" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.DateTime)">
      <summary>生成代码以指定 <see cref="T:System.DateTime" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.DateTimeOffset)">
      <summary>生成代码以指定 <see cref="T:System.DateTimeOffset" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Decimal)">
      <summary>生成代码以指定 <see cref="T:System.Decimal" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Guid)">
      <summary>生成代码以指定 <see cref="T:System.Guid" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Int64)">
      <summary>生成代码以指定 <see cref="T:System.Int64" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Object)">
      <summary>生成代码以指定数据类型未知的列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.Single)">
      <summary>生成代码以指定 <see cref="T:System.Single" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.String)">
      <summary>生成代码以指定 <see cref="T:System.String" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.String,System.Collections.Generic.IEnumerable{System.Data.Entity.Migrations.Model.MigrationOperation},System.String,System.String,System.String,System.String)">
      <summary>使用迁移元数据生成代码隐藏文件。</summary>
      <returns>生成的代码。</returns>
      <param name="migrationId">迁移的唯一标识符。</param>
      <param name="operations">迁移要执行的操作。</param>
      <param name="sourceModel">要存储在迁移元数据中的源模型。</param>
      <param name="targetModel">要存储在迁移元数据中的目标模型。</param>
      <param name="namespace">其中应生成代码的命名空间。</param>
      <param name="className">应生成的类的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.String,System.String,System.String,System.String,System.String)">
      <summary>使用迁移元数据生成代码隐藏文件。</summary>
      <returns>生成的代码。</returns>
      <param name="migrationId">迁移的唯一标识符。</param>
      <param name="sourceModel">要存储在迁移元数据中的源模型。</param>
      <param name="targetModel">要存储在迁移元数据中的目标模型。</param>
      <param name="namespace">其中应生成代码的命名空间。</param>
      <param name="className">应生成的类的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Generate(System.TimeSpan)">
      <summary>生成代码以指定 <see cref="T:System.TimeSpan" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.GenerateInline(System.Data.Entity.Migrations.Model.AddForeignKeyOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行作为 <see cref="T:System.Data.Entity.Migrations.Model.CreateTableOperation" /> 一部分的 <see cref="T:System.Data.Entity.Migrations.Model.AddForeignKeyOperation" />。</summary>
      <param name="addForeignKeyOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.GenerateInline(System.Data.Entity.Migrations.Model.AddPrimaryKeyOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行作为 <see cref="T:System.Data.Entity.Migrations.Model.CreateTableOperation" /> 一部分的 <see cref="T:System.Data.Entity.Migrations.Model.AddPrimaryKeyOperation" />。</summary>
      <param name="addPrimaryKeyOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.GenerateInline(System.Data.Entity.Migrations.Model.CreateIndexOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行作为 <see cref="T:System.Data.Entity.Migrations.Model.CreateTableOperation" /> 一部分的 <see cref="T:System.Data.Entity.Migrations.Model.CreateIndexOperation" />。</summary>
      <param name="createIndexOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.Quote(System.String)">
      <summary>使用相应的转义将标识符用引号引起来以允许它存储在字符串中。</summary>
      <returns>保存的标识符。</returns>
      <param name="identifier">要用引号引起来的标识符。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.ScrubName(System.String)">
      <summary>从数据库体系结构名称中删除所有无效字符。</summary>
      <returns>已擦除的名称。</returns>
      <param name="name">要擦除的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.TranslateColumnType(System.Data.Metadata.Edm.PrimitiveTypeKind)">
      <summary>获取用于给定数据类型的列的类型名称。</summary>
      <returns>要在生成的迁移中使用的类型名称。</returns>
      <param name="primitiveTypeKind">要转换的数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.WriteClassEnd(System.String,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>为以 WriteClassStart 开头的类生成结束代码。</summary>
      <param name="namespace">其中应生成代码的命名空间。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.WriteClassStart(System.String,System.String,System.Data.Entity.Migrations.Utilities.IndentedTextWriter,System.String,System.Boolean,System.Collections.Generic.IEnumerable{System.String})">
      <summary>使用语句和类定义生成命名空间。</summary>
      <param name="namespace">其中应生成代码的命名空间。</param>
      <param name="className">应生成的类的名称。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
      <param name="base">生成的类的基类。</param>
      <param name="designer">指示是否应为代码隐藏文件生成此类的值。</param>
      <param name="namespaces">将为其添加 using 指令的命名空间。如果为 null，则将使用从 GetDefaultNamespaces 返回的命名空间。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.CSharpMigrationCodeGenerator.WriteProperty(System.String,System.String,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成属性以在代码隐藏文件中返回源或目标模型。</summary>
      <param name="name">属性的名称。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="T:System.Data.Entity.Migrations.Design.MigrationCodeGenerator">
      <summary>为基于代码的迁移生成代码的提供程序的基类。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.MigrationCodeGenerator.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Migrations.Design.MigrationCodeGenerator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.MigrationCodeGenerator.Generate(System.String,System.Collections.Generic.IEnumerable{System.Data.Entity.Migrations.Model.MigrationOperation},System.String,System.String,System.String,System.String)">
      <summary>     生成应添加到用户项目的代码。</summary>
      <returns>生成的代码。</returns>
      <param name="migrationId">迁移的唯一标识符。</param>
      <param name="operations">迁移要执行的操作。</param>
      <param name="sourceModel">要存储在迁移元数据中的源模型。</param>
      <param name="targetModel">要存储在迁移元数据中的目标模型。</param>
      <param name="namespace">其中应生成代码的命名空间。</param>
      <param name="className">应生成的类的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.MigrationCodeGenerator.GetDefaultNamespaces(System.Boolean)">
      <summary> 为生成的所有代码获取必须输出为“using”或“Imports”指令的默认命名空间。</summary>
      <returns>命名空间名称的有序列表。</returns>
      <param name="designer">指示是否应为代码隐藏文件生成此类的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.MigrationCodeGenerator.GetNamespaces(System.Collections.Generic.IEnumerable{System.Data.Entity.Migrations.Model.MigrationOperation})">
      <summary> 获取必须输出为可处理给定操作生成的代码的“using”或“Imports”指令的命名空间。</summary>
      <returns>命名空间名称的有序列表。</returns>
      <param name="operations">将为其生成代码的操作。</param>
    </member>
    <member name="T:System.Data.Entity.Migrations.Design.MigrationScaffolder">
      <summary>为基于代码的迁移搭建基架以将挂起的模型更改应用于数据库。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.MigrationScaffolder.#ctor(System.Data.Entity.Migrations.DbMigrationsConfiguration)">
      <summary>初始化 <see cref="T:System.Data.Entity.Migrations.Design.MigrationScaffolder" /> 类的新实例。</summary>
      <param name="migrationsConfiguration">要用于搭建基架的配置。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Design.MigrationScaffolder.Namespace">
      <summary> 获取或设置迁移生成的代码中使用的命名空间。默认情况下，此命名空间与传入构造函数的迁移配置对象上的 MigrationsNamespace 相同。对于 VB.NET 项目，此命名空间将需要更新为将项目的根命名空间考虑进去。</summary>
      <returns>迁移生成的代码中使用的命名空间。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.MigrationScaffolder.Scaffold(System.String)">
      <summary>     为基于代码的迁移搭建基架以将挂起的所有模型更改应用于数据库。</summary>
      <returns>已搭建基架的迁移。</returns>
      <param name="migrationName">要用于已搭建基架的迁移的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.MigrationScaffolder.Scaffold(System.String,System.Boolean)">
      <summary>为基于代码的迁移搭建基架以将挂起的所有模型更改应用于数据库。</summary>
      <returns>已搭建基架的迁移。</returns>
      <param name="migrationName">要用于已搭建基架的迁移的名称。</param>
      <param name="ignoreChanges">如果排除模型更改，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.MigrationScaffolder.ScaffoldInitialCreate">
      <summary> 为与之前运行的数据库初始值设定项对应的基于代码的初始迁移搭建基架。</summary>
      <returns>已搭建基架的迁移。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.Design.ScaffoldedMigration">
      <summary>   表示已搭建基架并准备写入文件的基于代码的迁移。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.ScaffoldedMigration.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Migrations.Design.ScaffoldedMigration" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Design.ScaffoldedMigration.DesignerCode">
      <summary>     获取或设置应存储在代码隐藏文件中的已搭建基架的迁移代码。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Design.ScaffoldedMigration.Directory">
      <summary>     获取或设置此迁移应保存在其中的用户项目中的子目录。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Design.ScaffoldedMigration.Language">
      <summary>     获取或设置用于此迁移的编程语言。通常用于生成的代码的文件扩展名。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Design.ScaffoldedMigration.MigrationId">
      <summary>     获取或设置此迁移的唯一标识符。通常用于生成的代码的文件名。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Design.ScaffoldedMigration.UserCode">
      <summary>     获取或设置用户可编辑的已搭建基架的迁移代码。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.Design.ToolingException">
      <summary>表示在 <see cref="T:System.Data.Entity.Migrations.Design.ToolingFacade" /> 的另一个 AppDomain 中运行操作时出现的异常。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.ToolingException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>使用序列化数据初始化 <see cref="T:System.Data.Entity.Migrations.Design.ToolingException" /> 类的新实例。</summary>
      <param name="info">有关引发的异常的序列化对象数据。</param>
      <param name="context">有关源或目标的上下文信息。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.ToolingException.#ctor(System.String,System.String,System.String)">
      <summary>使用指定的错误、内部异常类型和堆栈跟踪初始化 <see cref="T:System.Data.Entity.Migrations.Design.ToolingException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误消息。</param>
      <param name="innerType">已引发异常的类型。</param>
      <param name="innerStackTrace">已引发异常的堆栈跟踪。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.ToolingException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>使用序列化目标对象时所需的数据填充 <see cref="T:System.Runtime.Serialization.SerializationInfo" />。</summary>
      <param name="info">要填充数据的 <see cref="T:System.Runtime.Serialization.SerializationInfo" />。</param>
      <param name="context">此序列化的目标。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Design.ToolingException.InnerStackTrace">
      <summary>获取已引发异常的堆栈跟踪。</summary>
      <returns>已引发异常的堆栈跟踪。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Design.ToolingException.InnerType">
      <summary>获取已引发异常的类型。</summary>
      <returns>已引发异常的类型。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.Design.ToolingFacade">
      <summary>表示设计时工具用于运行需要与正在 Visual Studio 中编辑的应用程序进行交互的迁移相关命令的帮助器类。由于应用程序处于编辑状态，因此需要在单独的 AppDomain 中加载程序集以确保始终加载的是最新版本。还将复制启动项目中的 App/Web.config 文件以确保应用所有配置。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.ToolingFacade.#ctor(System.String,System.String,System.String,System.String,System.String,System.Data.Entity.Infrastructure.DbConnectionInfo)">
      <summary>初始化 <see cref="T:System.Data.Entity.Migrations.Design.ToolingFacade" /> 类的新实例。</summary>
      <param name="assemblyName">包含要使用的迁移配置的程序集的名称。</param>
      <param name="configurationTypeName">要使用的迁移配置的命名空间限定名称。</param>
      <param name="workingDirectory">包含已编译程序集的工作目录。</param>
      <param name="configurationFilePath">启动项目中的配置文件的路径。</param>
      <param name="dataDirectory">启动项目中的应用程序数据目录的路径。通常，App_Data 目录用于 Web 应用程序，或者工作目录用于可执行文件。</param>
      <param name="connectionStringInfo">与要迁移的数据库的连接。如果未提供，则将使用上下文的默认连接。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.ToolingFacade.Dispose">
      <summary>释放由 <see cref="T:System.Data.Entity.Migrations.Design.ToolingFacade" /> 使用的所有资源。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.ToolingFacade.Dispose(System.Boolean)">
      <summary>释放由 <see cref="T:System.Data.Entity.Migrations.Design.ToolingFacade" /> 使用的所有资源。</summary>
      <param name="disposing">如果同时释放托管资源和非托管资源，则为 true；如果只释放非托管资源，则为 false。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.ToolingFacade.Finalize">
      <summary>释放由 facade 使用的所有非托管资源。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.ToolingFacade.GetContextTypes">
      <summary>获取派生自 <see cref="T:System.Data.Entity.DbContext" /> 的所有类型的完全限定名。</summary>
      <returns>找到的所有上下文类型。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.ToolingFacade.GetDatabaseMigrations">
      <summary>获取已应用于数据库的所有迁移的列表。</summary>
      <returns>已应用迁移的 ID。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.ToolingFacade.GetPendingMigrations">
      <summary>获取尚未应用于数据库的所有迁移的列表。</summary>
      <returns>挂起的迁移的 ID。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Design.ToolingFacade.LogInfoDelegate">
      <summary>获取或设置要运行以记录信息的操作。</summary>
      <returns>要运行以记录信息的操作。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Design.ToolingFacade.LogVerboseDelegate">
      <summary>获取或设置要运行以记录详细信息的操作。</summary>
      <returns>要运行以记录详细信息的操作。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Design.ToolingFacade.LogWarningDelegate">
      <summary>获取或设置要运行以记录警告的操作。</summary>
      <returns>要运行以记录警告的操作。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.ToolingFacade.Scaffold(System.String,System.String,System.String,System.Boolean)">
      <summary>为基于代码的迁移搭建基架以应用所有挂起的模型更改。</summary>
      <returns>已搭建基架的迁移。</returns>
      <param name="migrationName">生成的迁移的名称。</param>
      <param name="language">生成的迁移的编程语言。</param>
      <param name="rootNamespace">迁移将添加到的项目的根命名空间。</param>
      <param name="ignoreChanges">如果排除模型更改，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.ToolingFacade.ScaffoldInitialCreate(System.String,System.String)">
      <summary>为与之前运行的数据库初始值设定项对应的基于代码的初始迁移搭建基架。</summary>
      <returns>已搭建基架的迁移。</returns>
      <param name="language">生成的迁移的编程语言。</param>
      <param name="rootNamespace">迁移将添加到的项目的根命名空间。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.ToolingFacade.ScriptUpdate(System.String,System.String,System.Boolean)">
      <summary>生成 SQL 脚本以在两个迁移之间进行迁移。</summary>
      <returns>生成的 SQL 脚本。</returns>
      <param name="sourceMigration">要从其进行更新的迁移。如果未提供，则将生成更新当前数据库的脚本。</param>
      <param name="targetMigration">要更新到的迁移。如果未提供，则将生成更新到最新迁移的脚本。</param>
      <param name="force">指示是否可接受自动迁移期间的数据丢失的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.ToolingFacade.Update(System.String,System.Boolean)">
      <summary>将数据库更新到指定的迁移。</summary>
      <param name="targetMigration">要迁移到的迁移的 ID。如果未提供，则会将数据库更新到最新迁移。</param>
      <param name="force">指示是否可接受自动迁移期间的数据丢失的值。</param>
    </member>
    <member name="T:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator">
      <summary>为基于代码的迁移生成 Visual Basic .Net 代码。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Byte)">
      <summary>生成代码以指定 <see cref="T:System.Byte" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Byte[])">
      <summary>生成代码以指定 <see cref="T:System.Byte[]" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Collections.Generic.IEnumerable{System.Data.Entity.Migrations.Model.MigrationOperation},System.String,System.String)">
      <summary>生成用户可查看和编辑的主代码文件。</summary>
      <returns>生成的代码。</returns>
      <param name="operations">迁移要执行的操作。</param>
      <param name="namespace">其中应生成代码的命名空间。</param>
      <param name="className">应生成的类的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Collections.Generic.IEnumerable{System.String},System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以指定使用 lambda 表达式的一组列名称。</summary>
      <param name="columns">要为之生成代码的列。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.AddColumnOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.AddColumnOperation" />。</summary>
      <param name="addColumnOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.AddForeignKeyOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.AddForeignKeyOperation" />。</summary>
      <param name="addForeignKeyOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.AddPrimaryKeyOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.AddPrimaryKeyOperation" />。</summary>
      <param name="addPrimaryKeyOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.AlterColumnOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.AlterColumnOperation" />。</summary>
      <param name="alterColumnOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.ColumnModel,System.Data.Entity.Migrations.Utilities.IndentedTextWriter,System.Boolean)">
      <summary>生成代码以指定 <see cref="T:System.Data.Entity.Migrations.Model.ColumnModel" /> 的定义。</summary>
      <param name="column">要为之生成代码的列定义。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
      <param name="emitName">指示定义中是否包含列名称的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.CreateIndexOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.CreateIndexOperation" />。</summary>
      <param name="createIndexOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.CreateTableOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.CreateTableOperation" />。</summary>
      <param name="createTableOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.DropColumnOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.DropColumnOperation" />。</summary>
      <param name="dropColumnOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.DropForeignKeyOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>     生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.DropForeignKeyOperation" />。</summary>
      <param name="dropForeignKeyOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.DropIndexOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>     生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.DropIndexOperation" />。</summary>
      <param name="dropIndexOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.DropPrimaryKeyOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>     生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.DropPrimaryKeyOperation" />。</summary>
      <param name="dropPrimaryKeyOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.DropTableOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>     生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.DropTableOperation" />。</summary>
      <param name="dropTableOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.MoveTableOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>     生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.MoveTableOperation" />。</summary>
      <param name="moveTableOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.RenameColumnOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>     生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.RenameColumnOperation" />。</summary>
      <param name="renameColumnOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.RenameTableOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>     生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.RenameTableOperation" />。</summary>
      <param name="renameTableOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Entity.Migrations.Model.SqlOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>     生成代码以执行 <see cref="T:System.Data.Entity.Migrations.Model.SqlOperation" />。</summary>
      <param name="sqlOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Spatial.DbGeography)">
      <summary>     生成代码以指定 <see cref="T:System.Data.Spatial.DbGeography" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Data.Spatial.DbGeometry)">
      <summary>     生成代码以指定 <see cref="T:System.Data.Spatial.DbGeometry" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.DateTime)">
      <summary>     生成代码以指定 <see cref="T:System.DateTime" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.DateTimeOffset)">
      <summary>     生成代码以指定 <see cref="T:System.DateTimeOffset" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Decimal)">
      <summary>     生成代码以指定 <see cref="T:System.Decimal" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Guid)">
      <summary>     生成代码以指定 <see cref="T:System.Guid" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Int64)">
      <summary>     生成代码以指定 <see cref="T:System.Int64" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Object)">
      <summary>     生成代码以指定数据类型未知的列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.Single)">
      <summary>     生成代码以指定 <see cref="T:System.Single" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.String)">
      <summary>     生成代码以指定 <see cref="T:System.String" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.String,System.Collections.Generic.IEnumerable{System.Data.Entity.Migrations.Model.MigrationOperation},System.String,System.String,System.String,System.String)">
      <summary>使用迁移元数据生成代码隐藏文件。</summary>
      <returns>生成的代码。</returns>
      <param name="migrationId">迁移的唯一标识符。</param>
      <param name="operations">迁移要执行的操作。</param>
      <param name="sourceModel">要存储在迁移元数据中的源模型。</param>
      <param name="targetModel">要存储在迁移元数据中的目标模型。</param>
      <param name="namespace">其中应生成代码的命名空间。</param>
      <param name="className">应生成的类的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.String,System.String,System.String,System.String,System.String)">
      <summary>使用迁移元数据生成代码隐藏文件。</summary>
      <returns>生成的代码。</returns>
      <param name="migrationId">迁移的唯一标识符。</param>
      <param name="sourceModel">要存储在迁移元数据中的源模型。</param>
      <param name="targetModel">要存储在迁移元数据中的目标模型。</param>
      <param name="namespace">其中应生成代码的命名空间。</param>
      <param name="className">应生成的类的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Generate(System.TimeSpan)">
      <summary>     生成代码以指定 <see cref="T:System.TimeSpan" /> 列的默认值。</summary>
      <returns>表示默认值的代码。</returns>
      <param name="defaultValue">要用作默认值的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.GenerateInline(System.Data.Entity.Migrations.Model.AddForeignKeyOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>     生成代码以执行作为 <see cref="T:System.Data.Entity.Migrations.Model.CreateTableOperation" /> 一部分的 <see cref="T:System.Data.Entity.Migrations.Model.AddForeignKeyOperation" />。</summary>
      <param name="addForeignKeyOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.GenerateInline(System.Data.Entity.Migrations.Model.AddPrimaryKeyOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>     生成代码以执行作为 <see cref="T:System.Data.Entity.Migrations.Model.CreateTableOperation" /> 一部分的 <see cref="T:System.Data.Entity.Migrations.Model.AddPrimaryKeyOperation" />。</summary>
      <param name="addPrimaryKeyOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.GenerateInline(System.Data.Entity.Migrations.Model.CreateIndexOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>     生成代码以执行作为 <see cref="T:System.Data.Entity.Migrations.Model.CreateTableOperation" /> 一部分的 <see cref="T:System.Data.Entity.Migrations.Model.CreateIndexOperation" />。</summary>
      <param name="createIndexOperation">要为之生成代码的操作。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.Quote(System.String)">
      <summary>     使用相应的转义将标识符用引号引起来以允许它存储在字符串中。</summary>
      <returns>保存的标识符。</returns>
      <param name="identifier">要用引号引起来的标识符。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.ScrubName(System.String)">
      <summary>     从数据库体系结构名称中删除所有无效字符。</summary>
      <returns>已擦除的名称。</returns>
      <param name="name">要擦除的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.TranslateColumnType(System.Data.Metadata.Edm.PrimitiveTypeKind)">
      <summary>     获取用于给定数据类型的列的类型名称。</summary>
      <returns>要在生成的迁移中使用的类型名称。</returns>
      <param name="primitiveTypeKind">要转换的数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.WriteClassEnd(System.String,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary> 为以 WriteClassStart 开头的类生成结束代码。</summary>
      <param name="namespace">其中应生成代码的命名空间。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.WriteClassStart(System.String,System.String,System.Data.Entity.Migrations.Utilities.IndentedTextWriter,System.String,System.Boolean,System.Collections.Generic.IEnumerable{System.String})">
      <summary>     使用语句和类定义生成命名空间。</summary>
      <param name="namespace">其中应生成代码的命名空间。</param>
      <param name="className">应生成的类的名称。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
      <param name="base">生成的类的基类。</param>
      <param name="designer">指示是否应为代码隐藏文件生成此类的值。</param>
      <param name="namespaces">将为其添加 Imports 指令的命名空间。如果为 null，则将使用从 GetDefaultNamespaces 返回的命名空间。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Design.VisualBasicMigrationCodeGenerator.WriteProperty(System.String,System.String,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>     生成属性以在代码隐藏文件中返回源或目标模型。</summary>
      <param name="name">属性的名称。</param>
      <param name="writer">生成的代码要添加到的文本编写器。</param>
    </member>
    <member name="T:System.Data.Entity.Migrations.History.HistoryRow">
      <summary>   此类由 Code First 迁移用于读取和写入数据库中的迁移历史记录。此类不可由其他代码使用并且只能是公共的，以便 Entity Framework 在部分信任模式下运行时可以访问它。以后可能会更改或删除此类。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.History.HistoryRow.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Migrations.History.HistoryRow" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.History.HistoryRow.CreatedOn">
      <summary>     获取或设置此迁移历史记录条目的创建日期和时间。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.History.HistoryRow.MigrationId">
      <summary>     获取或设置此行表示的迁移的 ID。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.History.HistoryRow.Model">
      <summary>     获取或设置此迁移应用后模型的状态。</summary>
      <returns>返回 <see cref="T:System.Byte" />。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.History.HistoryRow.ProductVersion">
      <summary>     获取或设置创建此条目的实体框架的版本。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.Infrastructure.AutomaticDataLossException">
      <summary>表示自动迁移将导致数据丢失时出现的错误。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.AutomaticDataLossException.#ctor(System.String)">
      <summary>     初始化 AutomaticDataLossException 类的新实例。</summary>
      <param name="message">描述错误的消息。</param>
    </member>
    <member name="T:System.Data.Entity.Migrations.Infrastructure.AutomaticMigrationsDisabledException">
      <summary>表示在应用最后一个迁移后仍然存在挂起的模型更改并且禁用了自动迁移时出现的错误。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.AutomaticMigrationsDisabledException.#ctor(System.String)">
      <summary>     初始化 AutomaticMigrationsDisabledException 类的新实例。</summary>
      <param name="message">描述错误的消息。</param>
    </member>
    <member name="T:System.Data.Entity.Migrations.Infrastructure.IMigrationMetadata">
      <summary>提供有关基于代码的迁移的其他元数据。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Infrastructure.IMigrationMetadata.Id">
      <summary>     获取迁移的唯一标识符。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Infrastructure.IMigrationMetadata.Source">
      <summary>     获取此迁移运行前模型的状态。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Infrastructure.IMigrationMetadata.Target">
      <summary>     获取此迁移运行后模型的状态。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.Infrastructure.MigrationsException">
      <summary>表示 Code First 迁移管道内出现的错误。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.MigrationsException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary> 使用序列化数据初始化 MigrationsException 类的新实例。</summary>
      <param name="info">包含有关正在引发的异常的序列化对象数据的 <see cref="T:System.Runtime.Serialization.SerializationInfo" />。</param>
      <param name="context">包含有关源或目标的上下文信息的 <see cref="T:System.Runtime.Serialization.StreamingContext" />。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.MigrationsException.#ctor(System.String)">
      <summary> 初始化 MigrationsException 类的新实例。</summary>
      <param name="message">描述错误的消息。</param>
    </member>
    <member name="T:System.Data.Entity.Migrations.Infrastructure.MigrationsLogger">
      <summary>可用于迁移过程的记录器的基类。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.MigrationsLogger.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Migrations.Infrastructure.MigrationsLogger" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.MigrationsLogger.Info(System.String)">
      <summary>     记录信息性消息。</summary>
      <param name="message">要记录的消息。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.MigrationsLogger.Verbose(System.String)">
      <summary>     记录只应在用户请求详细输出时呈现给他们的一些其他信息。</summary>
      <param name="message">要记录的消息。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.MigrationsLogger.Warning(System.String)">
      <summary>     记录用户应了解的警告。</summary>
      <param name="message">要记录的消息。</param>
    </member>
    <member name="T:System.Data.Entity.Migrations.Infrastructure.MigratorBase">
      <summary>包装核心 <see cref="T:System.Data.Entity.Migrations.DbMigrator" /> 的修饰器的基类。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.MigratorBase.#ctor(System.Data.Entity.Migrations.Infrastructure.MigratorBase)">
      <summary>     初始化 MigratorBase 类的新实例。</summary>
      <param name="innerMigrator">此修饰器将包装的迁移程序。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Infrastructure.MigratorBase.Configuration">
      <summary>     获取要用于迁移过程的配置。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.MigratorBase.GetDatabaseMigrations">
      <summary>     获取已应用于数据库的迁移的列表。</summary>
      <returns>迁移 Id 的列表</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.MigratorBase.GetLocalMigrations">
      <summary>     获取程序集中定义的迁移的列表。</summary>
      <returns>迁移 Id 的列表</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.MigratorBase.GetPendingMigrations">
      <summary>     获取尚未应用于数据库的挂起的迁移的列表。</summary>
      <returns>迁移 Id 的列表</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.MigratorBase.Update">
      <summary>     将目标数据库更新到最新迁移。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.MigratorBase.Update(System.String)">
      <summary>     将目标数据库更新到给定迁移。</summary>
      <param name="targetMigration">要升级/降级到的迁移。</param>
    </member>
    <member name="T:System.Data.Entity.Migrations.Infrastructure.MigratorLoggingDecorator">
      <summary>在迁移操作期间提供日志记录的修饰器。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.MigratorLoggingDecorator.#ctor(System.Data.Entity.Migrations.Infrastructure.MigratorBase,System.Data.Entity.Migrations.Infrastructure.MigrationsLogger)">
      <summary>     初始化 MigratorLoggingDecorator 类的新实例。</summary>
      <param name="innerMigrator">此修饰器将包装的迁移程序。</param>
      <param name="logger">消息将写入到的记录器。</param>
    </member>
    <member name="T:System.Data.Entity.Migrations.Infrastructure.MigratorScriptingDecorator">
      <summary>表示生成 SQL 脚本而不是将更改应用于数据库的修饰器。使用此修饰器包装 <see cref="T:System.Data.Entity.Migrations.DbMigrator" /> 将防止 <see cref="T:System.Data.Entity.Migrations.DbMigrator" /> 将任何更改应用于目标数据库。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.MigratorScriptingDecorator.#ctor(System.Data.Entity.Migrations.Infrastructure.MigratorBase)">
      <summary>初始化 <see cref="T:System.Data.Entity.Migrations.Infrastructure.MigratorScriptingDecorator" /> 类的新实例。</summary>
      <param name="innerMigrator">此修饰器将包装的迁移程序。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Infrastructure.MigratorScriptingDecorator.ScriptUpdate(System.String,System.String)">
      <summary>将数据库更新到目标迁移。</summary>
      <returns>生成的 SQL 脚本。</returns>
      <param name="sourceMigration">源迁移。</param>
      <param name="targetMigration">要升级/降级到的迁移。</param>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.AddColumnOperation">
      <summary>表示将添加到表中的列。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.AddColumnOperation.#ctor(System.String,System.Data.Entity.Migrations.Model.ColumnModel,System.Object)">
      <summary>     初始化 AddColumnOperation 类的新实例。</summary>
      <param name="table">列应添加到的表的名称。</param>
      <param name="column">将添加的列的详细信息。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.AddColumnOperation.Column">
      <summary>     获取将添加的列的详细信息。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.AddColumnOperation.Inverse">
      <summary>     获取表示删除已添加列的操作。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.AddColumnOperation.IsDestructiveChange">
      <summary>获取 {insert text here}。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.AddColumnOperation.Table">
      <summary>     获取列应添加到的表的名称。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.AddForeignKeyOperation">
      <summary>表示将添加到表中的外键约束。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.AddForeignKeyOperation.#ctor(System.Object)">
      <summary>     初始化 AddForeignKeyOperation 类的新实例。还应填充 PrincipalTable、PrincipalColumns、DependentTable 和 DependentColumns 属性。</summary>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.AddForeignKeyOperation.CascadeDelete">
      <summary>     获取或设置指示是否应在外键约束上配置级联删除的值。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.AddForeignKeyOperation.CreateCreateIndexOperation">
      <summary>     获取在外键列上创建索引的操作。</summary>
      <returns>添加索引的操作。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.AddForeignKeyOperation.Inverse">
      <summary>     获取删除外键约束的操作。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.AddForeignKeyOperation.IsDestructiveChange">
      <summary>获取指示更改是否具有破坏性的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.AddForeignKeyOperation.PrincipalColumns">
      <summary>     外键约束应面向的列的名称。</summary>
      <returns>列名称。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.AddPrimaryKeyOperation">
      <summary>表示向表添加主键。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.AddPrimaryKeyOperation.#ctor(System.Object)">
      <summary>     初始化 AddPrimaryKeyOperation 类的新实例。还应填充 Table 和 Columns 属性。</summary>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.AddPrimaryKeyOperation.Inverse">
      <summary>     获取删除主键的操作。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.AlterColumnOperation">
      <summary>表示更改现有列。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.AlterColumnOperation.#ctor(System.String,System.Data.Entity.Migrations.Model.ColumnModel,System.Boolean,System.Data.Entity.Migrations.Model.AlterColumnOperation,System.Object)">
      <summary>     初始化 AlterColumnOperation 类的新实例。</summary>
      <param name="table">列所属表的名称。</param>
      <param name="column">列应更改为的内容的详细信息。</param>
      <param name="isDestructiveChange">指示此更改是否会导致数据丢失的值。</param>
      <param name="inverse">还原对此列的更改的操作。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.AlterColumnOperation.#ctor(System.String,System.Data.Entity.Migrations.Model.ColumnModel,System.Boolean,System.Object)">
      <summary>     初始化 AlterColumnOperation 类的新实例。</summary>
      <param name="table">列所属表的名称。</param>
      <param name="column">列应更改为的内容的详细信息。</param>
      <param name="isDestructiveChange">指示此更改是否会导致数据丢失的值。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.AlterColumnOperation.Column">
      <summary>     获取列的新定义。</summary>
      <returns>列定义。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.AlterColumnOperation.Inverse">
      <summary>     获取表示还原更改的操作。无法自动计算反向，如果未向构造函数提供反向，则此属性将返回 null。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.AlterColumnOperation.IsDestructiveChange">
      <summary>获取指示更改是否具有破坏性的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.AlterColumnOperation.Table">
      <summary>     获取列所属表的名称。</summary>
      <returns>表名称。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.ColumnModel">
      <summary>表示有关列的信息。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.ColumnModel.#ctor(System.Data.Metadata.Edm.PrimitiveTypeKind)">
      <summary>     初始化此类的新实例。</summary>
      <param name="type">此列的数据类型。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.ColumnModel.#ctor(System.Data.Metadata.Edm.PrimitiveTypeKind,System.Data.Metadata.Edm.TypeUsage)">
      <summary>     初始化此类的新实例。</summary>
      <param name="type">此列的数据类型。</param>
      <param name="typeUsage">     有关数据类型的其他详细信息。这些信息包括最大长度、可为空属性等详细信息。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ColumnModel.ClrDefaultValue">
      <summary>     获取与此列的数据库类型对应的 CLR 类型的默认值。</summary>
      <returns>默认值。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ColumnModel.ClrType">
      <summary>     获取与此列的数据库类型对应的 CLR 类型。</summary>
      <returns>CLR 类型。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ColumnModel.DefaultValue">
      <summary>     获取或设置用作此列的默认值的常量值。</summary>
      <returns>此列的默认值。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ColumnModel.DefaultValueSql">
      <summary>     获取或设置用作此列的默认值的 SQL 表达式。</summary>
      <returns>此列的默认值。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ColumnModel.IsFixedLength">
      <summary>     获取或设置指示此列的长度是否固定的值。仅对数组数据类型有效。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ColumnModel.IsIdentity">
      <summary>     获取或设置指示此列的值是否由使用标识模式的数据库生成的值。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.ColumnModel.IsNarrowerThan(System.Data.Entity.Migrations.Model.ColumnModel,System.Data.Common.DbProviderManifest)">
      <summary>     确定此列的数据类型是否窄于另一列的。用于确定将提供的列定义更改为此定义是否会导致数据丢失。</summary>
      <returns>如果此列的数据类型更窄，则为 True。</returns>
      <param name="column">要与之比较的列。</param>
      <param name="providerManifest">正使用的数据库提供程序的详细信息。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ColumnModel.IsNullable">
      <summary>     获取或设置指示此列是否可存储 null 值的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ColumnModel.IsTimestamp">
      <summary>     获取或设置指示是否应将此列配置为时间戳的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ColumnModel.IsUnicode">
      <summary>     获取或设置指示此列是否支持 Unicode 字符的值。仅对文本数据类型有效。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ColumnModel.MaxLength">
      <summary>     获取或设置此列的最大长度。仅对数组数据类型有效。</summary>
      <returns>最大长度。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ColumnModel.Name">
      <summary>     获取或设置列名称。</summary>
      <returns>名称。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ColumnModel.Precision">
      <summary>     获取或设置此列的精度。仅对十进制数据类型有效。</summary>
      <returns>精度。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ColumnModel.Scale">
      <summary>     获取或设置此列的刻度。仅对十进制数据类型有效。</summary>
      <returns>刻度。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ColumnModel.StoreType">
      <summary>     获取或设置用于此列的提供程序特定数据类型。</summary>
      <returns>数据类型。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ColumnModel.Type">
      <summary>     获取此列的数据类型。</summary>
      <returns>数据类型。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ColumnModel.TypeUsage">
      <summary>     获取有关此列的数据类型的其他详细信息。这些信息包括最大长度、可为空属性等详细信息。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.CreateIndexOperation">
      <summary>表示创建数据库索引。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.CreateIndexOperation.#ctor(System.Object)">
      <summary>     初始化 CreateIndexOperation 类的新实例。还应填充 Table 和 Columns 属性。</summary>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.CreateIndexOperation.Inverse">
      <summary>     获取删除此索引的操作。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.CreateIndexOperation.IsDestructiveChange">
      <summary>获取指示更改是否具有破坏性的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.CreateIndexOperation.IsUnique">
      <summary>     获取或设置指示此索引是否是唯一索引的值。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.CreateTableOperation">
      <summary>表示创建表。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.CreateTableOperation.#ctor(System.String,System.Object)">
      <summary>     初始化 CreateTableOperation 类的新实例。</summary>
      <param name="name">要创建的表的名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.CreateTableOperation.Columns">
      <summary>     获取要包含在新表中的列。</summary>
      <returns>要包含的列。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.CreateTableOperation.Inverse">
      <summary>     获取删除表的操作。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.CreateTableOperation.IsDestructiveChange">
      <summary>获取指示更改是否具有破坏性的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.CreateTableOperation.Name">
      <summary>     获取要创建的表的名称。</summary>
      <returns>名称。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.CreateTableOperation.PrimaryKey">
      <summary>     获取或设置新表的主键。</summary>
      <returns>主键。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.DeleteHistoryOperation">
      <summary>表示将从迁移历史记录表中删除新记录。迁移历史记录表用于存储已应用于数据库的迁移的日志。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.DeleteHistoryOperation.#ctor(System.String,System.String,System.Object)">
      <summary>     初始化 DeleteHistoryOperation 类的新实例。</summary>
      <param name="table">迁移历史记录表的名称。</param>
      <param name="migrationId">要删除的迁移记录的 Id。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.DropColumnOperation">
      <summary>表示将从表中删除的列。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.DropColumnOperation.#ctor(System.String,System.String,System.Data.Entity.Migrations.Model.AddColumnOperation,System.Object)">
      <summary>     初始化 DropColumnOperation 类的新实例。</summary>
      <param name="table">应从中删除列的表的名称。</param>
      <param name="name">要删除的列的名称。</param>
      <param name="inverse">表示还原删除操作的操作。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.DropColumnOperation.#ctor(System.String,System.String,System.Object)">
      <summary>     初始化 DropColumnOperation 类的新实例。</summary>
      <param name="table">应从中删除列的表的名称。</param>
      <param name="name">要删除的列的名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.DropColumnOperation.Inverse">
      <summary>     获取表示还原删除列的操作。无法自动计算反向，如果未向构造函数提供反向，则此属性将返回 null。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.DropColumnOperation.IsDestructiveChange">
      <summary>获取指示更改是否具有破坏性的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.DropColumnOperation.Name">
      <summary>     获取要删除的列的名称。</summary>
      <returns>列名称。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.DropColumnOperation.Table">
      <summary>     获取应从中删除列的表的名称。</summary>
      <returns>表名称。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.DropForeignKeyOperation">
      <summary>表示将从表中删除的外键约束。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.DropForeignKeyOperation.#ctor(System.Data.Entity.Migrations.Model.AddForeignKeyOperation,System.Object)">
      <summary>     初始化 DropForeignKeyOperation 类的新实例。</summary>
      <param name="inverse">表示还原删除外键约束的操作。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.DropForeignKeyOperation.#ctor(System.Object)">
      <summary>     初始化 DropForeignKeyOperation 类的新实例。还应填充 PrincipalTable、DependentTable 和 DependentColumns 属性。</summary>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.DropForeignKeyOperation.CreateDropIndexOperation">
      <summary>     获取删除外键列上关联的索引的操作。</summary>
      <returns>删除索引的操作。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.DropForeignKeyOperation.Inverse">
      <summary>     获取表示还原删除外键约束的操作。无法自动计算反向，如果未向构造函数提供反向，则此属性将返回 null。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.DropForeignKeyOperation.IsDestructiveChange">
      <summary>获取指示更改是否具有破坏性的值。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.DropIndexOperation">
      <summary>表示删除现有索引。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.DropIndexOperation.#ctor(System.Data.Entity.Migrations.Model.CreateIndexOperation,System.Object)">
      <summary>     初始化 DropIndexOperation 类的新实例。</summary>
      <param name="inverse">表示还原删除索引的操作。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.DropIndexOperation.#ctor(System.Object)">
      <summary>     初始化 DropIndexOperation 类的新实例。</summary>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.DropIndexOperation.Inverse">
      <summary>     获取表示还原删除索引的操作。无法自动计算反向，如果未向构造函数提供反向，则此属性将返回 null。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.DropIndexOperation.IsDestructiveChange">
      <summary>获取指示更改是否具有破坏性的值。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.DropPrimaryKeyOperation">
      <summary>表示从表中删除主键。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.DropPrimaryKeyOperation.#ctor(System.Object)">
      <summary>     初始化 DropPrimaryKeyOperation 类的新实例。还应填充 Table 和 Columns 属性。</summary>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.DropPrimaryKeyOperation.Inverse">
      <summary>     获取添加主键的操作。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.DropTableOperation">
      <summary>表示删除现有表。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.DropTableOperation.#ctor(System.String,System.Data.Entity.Migrations.Model.CreateTableOperation,System.Object)">
      <summary>     初始化 DropTableOperation 类的新实例。</summary>
      <param name="name">要删除的表的名称。</param>
      <param name="inverse">表示还原删除表的操作。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.DropTableOperation.#ctor(System.String,System.Object)">
      <summary>     初始化 DropTableOperation 类的新实例。</summary>
      <param name="name">要删除的表的名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.DropTableOperation.Inverse">
      <summary>     获取表示还原删除表的操作。无法自动计算反向，如果未向构造函数提供反向，则此属性将返回 null。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.DropTableOperation.IsDestructiveChange">
      <summary>获取指示更改是否具有破坏性的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.DropTableOperation.Name">
      <summary>     获取要删除的表的名称。</summary>
      <returns>表名称。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.ForeignKeyOperation">
      <summary>影响外键约束的更改的基类。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.ForeignKeyOperation.#ctor(System.Object)">
      <summary>     初始化 ForeignKeyOperation 类的新实例。</summary>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ForeignKeyOperation.DependentColumns">
      <summary>     外键列的名称。</summary>
      <returns>列名称。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ForeignKeyOperation.DependentTable">
      <summary>     获取或设置其中存在外键列的表的名称。</summary>
      <returns>表名称。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ForeignKeyOperation.HasDefaultName">
      <summary>     获取指示是否为此外键约束提供了特定名称的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ForeignKeyOperation.Name">
      <summary>     获取或设置此外键约束的名称。如果未提供名称，则将计算默认名称。</summary>
      <returns>约束名称。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.ForeignKeyOperation.PrincipalTable">
      <summary>     获取或设置外键约束面向的表的名称。</summary>
      <returns>表名称。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.HistoryOperation">
      <summary>影响迁移历史记录表的操作的常规基类。迁移历史记录表用于存储已应用于数据库的迁移的日志。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.HistoryOperation.#ctor(System.String,System.String,System.Object)">
      <summary>     初始化 HistoryOperation 类的新实例。</summary>
      <param name="table">迁移历史记录表的名称。</param>
      <param name="migrationId">受影响的迁移的名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.HistoryOperation.IsDestructiveChange">
      <summary>获取指示更改是否具有破坏性的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.HistoryOperation.MigrationId">
      <summary>     获取受影响的迁移的名称。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.HistoryOperation.Table">
      <summary>     获取迁移历史记录表的名称。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.IndexOperation">
      <summary>影响索引的操作的常规基类。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.IndexOperation.#ctor(System.Object)">
      <summary>     初始化 IndexOperation 类的新实例。</summary>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.IndexOperation.Columns">
      <summary>     获取或设置已编制索引的列。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.IndexOperation.HasDefaultName">
      <summary>     获取指示是否为此索引提供了特定名称的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.IndexOperation.Name">
      <summary>     获取或设置此索引的名称。如果未提供名称，则将计算默认名称。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.IndexOperation.Table">
      <summary>     获取或设置此索引所属的表。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.InsertHistoryOperation">
      <summary>表示将新记录插入迁移历史记录表。迁移历史记录表用于存储已应用于数据库的迁移的日志。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.InsertHistoryOperation.#ctor(System.String,System.String,System.Byte[],System.Object)">
      <summary>     初始化 InsertHistoryOperation 类的新实例。</summary>
      <param name="table">迁移历史记录表的名称。</param>
      <param name="migrationId">要插入的迁移记录的 Id。</param>
      <param name="model">要存储在模型列中的值。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.InsertHistoryOperation.IsDestructiveChange">
      <summary>获取 {insert text here}。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.InsertHistoryOperation.Model">
      <summary>     获取存储在历史记录表中表示迁移的目标模型的值。</summary>
      <returns>返回 <see cref="T:System.Byte" />。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.InsertHistoryOperation.ProductVersion">
      <summary>     获取存储在历史记录表中指示用于生成此迁移的实体框架版本的值。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.MigrationOperation">
      <summary>表示修改数据库架构的操作。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.MigrationOperation.#ctor(System.Object)">
      <summary>     初始化 MigrationOperation 类的新实例。</summary>
      <param name="anonymousArguments">       使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.MigrationOperation.AnonymousArguments">
      <summary>     获取提供程序可能会处理的其他参数。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.MigrationOperation.Inverse">
      <summary>     获取将还原此操作的操作。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.MigrationOperation.IsDestructiveChange">
      <summary>     获取指示此操作是否会导致数据丢失的值。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.MoveTableOperation">
      <summary>表示将表从一个架构移至另一个架构。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.MoveTableOperation.#ctor(System.String,System.String,System.Object)">
      <summary>     初始化 MoveTableOperation 类的新实例。</summary>
      <param name="name">要移动的表的名称。</param>
      <param name="newSchema">要将表移至的架构的名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.MoveTableOperation.Inverse">
      <summary>     获取将表移回其原始架构的操作。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.MoveTableOperation.IsDestructiveChange">
      <summary>获取指示更改是否具有破坏性的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.MoveTableOperation.Name">
      <summary>     获取要移动的表的名称。</summary>
      <returns>名称。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.MoveTableOperation.NewSchema">
      <summary>     获取要将表移至的架构的名称。</summary>
      <returns>架构名称。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.PrimaryKeyOperation">
      <summary>用于表示影响主键的操作的常规基类。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.PrimaryKeyOperation.#ctor(System.Object)">
      <summary>     初始化 PrimaryKeyOperation 类的新实例。</summary>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.PrimaryKeyOperation.Columns">
      <summary>     获取组成主键的列。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.PrimaryKeyOperation.HasDefaultName">
      <summary>     获取指示是否为此主键提供了特定名称的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.PrimaryKeyOperation.IsDestructiveChange">
      <summary>获取指示此操作是否会导致数据丢失的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.PrimaryKeyOperation.Name">
      <summary>     获取或设置此主键的名称。如果未提供名称，则将计算默认名称。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.PrimaryKeyOperation.Table">
      <summary>     获取或设置包含主键的表的名称。</summary>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.RenameColumnOperation">
      <summary>表示重命名现有列。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.RenameColumnOperation.#ctor(System.String,System.String,System.String,System.Object)">
      <summary>     初始化 RenameColumnOperation 类的新实例。</summary>
      <param name="table">列所属表的名称。</param>
      <param name="name">要重命名的列的名称。</param>
      <param name="newName">列的新名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.RenameColumnOperation.Inverse">
      <summary>     获取还原重命名的操作。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.RenameColumnOperation.IsDestructiveChange">
      <summary>获取指示更改是否具有破坏性的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.RenameColumnOperation.Name">
      <summary>     获取要重命名的列的名称。</summary>
      <returns>名称。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.RenameColumnOperation.NewName">
      <summary>     获取列的新名称。</summary>
      <returns>新名称。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.RenameColumnOperation.Table">
      <summary>     获取列所属表的名称。</summary>
      <returns>表名称。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.RenameTableOperation">
      <summary>  表示重命名现有表。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.RenameTableOperation.#ctor(System.String,System.String,System.Object)">
      <summary>     初始化 RenameTableOperation 类的新实例。</summary>
      <param name="name">要重命名的表的名称。</param>
      <param name="newName">表的新名称。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.RenameTableOperation.Inverse">
      <summary>     获取还原重命名的操作。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.RenameTableOperation.IsDestructiveChange">
      <summary>获取指示更改是否具有破坏性的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.RenameTableOperation.Name">
      <summary>     获取要重命名的表的名称。</summary>
      <returns>名称。</returns>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.RenameTableOperation.NewName">
      <summary>     获取表的新名称。</summary>
      <returns>新名称。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.Model.SqlOperation">
      <summary>表示要直接对目标数据库执行的提供程序特定 SQL 语句。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Model.SqlOperation.#ctor(System.String,System.Object)">
      <summary>     初始化 SqlOperation 类的新实例。</summary>
      <param name="sql">要执行的 SQL。</param>
      <param name="anonymousArguments">     提供程序可能会处理的其他参数。使用匿名类型语法指定参数，如“new { SampleArgument = "MyValue" }”。</param>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.SqlOperation.IsDestructiveChange">
      <summary>获取指示此操作是否会导致数据丢失的值。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.SqlOperation.Sql">
      <summary>     获取要执行的 SQL。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Model.SqlOperation.SuppressTransaction">
      <summary>     获取或设置指示是否应在用于进行迁移过程事务的事务范围外执行此语句的值。如果设置为 true，则在迁移过程失败时，不会回滚此操作。</summary>
      <returns>如果在迁移失败时不回滚此操作，则为 true。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.Sql.MigrationSqlGenerator">
      <summary>   将提供程序不可知的迁移操作转换为数据库提供程序特定 SQL 命令的提供程序的常规基类。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.MigrationSqlGenerator.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Migrations.Sql.MigrationSqlGenerator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.MigrationSqlGenerator.Generate(System.Collections.Generic.IEnumerable{System.Data.Entity.Migrations.Model.MigrationOperation},System.String)">
      <summary>     将一组迁移操作转换为数据库提供程序特定 SQL。</summary>
      <returns>为执行迁移操作而要执行的 SQL 语句的列表。</returns>
      <param name="migrationOperations">要转换的操作。</param>
      <param name="providerManifestToken">表示将面向的数据库版本的标记。</param>
    </member>
    <member name="T:System.Data.Entity.Migrations.Sql.MigrationStatement">
      <summary>表示已转换为 SQL 语句的迁移操作。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.MigrationStatement.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Migrations.Sql.MigrationStatement" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Sql.MigrationStatement.Sql">
      <summary>     获取或设置为执行此迁移操作而要执行的 SQL。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Sql.MigrationStatement.SuppressTransaction">
      <summary>     获取或设置指示是否应在用于进行迁移过程事务的事务范围外执行此语句的值。如果设置为 true，则在迁移过程失败时，不会回滚此操作。</summary>
      <returns>如果操作在迁移过程失败时不会回滚，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.Sql.SqlCeMigrationSqlGenerator">
      <summary>表示可将提供程序不可知的迁移操作转换为可对 Microsoft SQL Server Compact Edition 运行的 SQL 命令的提供程序。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlCeMigrationSqlGenerator.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Migrations.Sql.SqlCeMigrationSqlGenerator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlCeMigrationSqlGenerator.CreateConnection">
      <summary>为当前提供程序创建空连接。允许派生提供程序使用 <see cref="T:System.Data.SqlClient.SqlConnection" /> 之外的连接。</summary>
      <returns>当前提供程序的空连接。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlCeMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.AlterColumnOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.AlterColumnOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="alterColumnOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlCeMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropColumnOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.DropColumnOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="dropColumnOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlCeMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropIndexOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.DropIndexOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="dropIndexOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlCeMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.MoveTableOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.MoveTableOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="moveTableOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlCeMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.RenameColumnOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.RenameColumnOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="renameColumnOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlCeMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.RenameTableOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.RenameTableOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="renameTableOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlCeMigrationSqlGenerator.Generate(System.DateTime)">
      <summary>生成 SQL 以指定将在列上设置的常量 DateTime 默认值。此方法只生成实际值，而不生成 SQL 设置默认值。</summary>
      <returns>表示默认值的 SQL。</returns>
      <param name="defaultValue">要设置的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlCeMigrationSqlGenerator.GenerateCreateSchema(System.String)">
      <summary>生成 SQL 以创建数据库架构。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="schema">要创建的数据库架构。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlCeMigrationSqlGenerator.GenerateMakeSystemTable(System.Data.Entity.Migrations.Model.CreateTableOperation)">
      <summary>生成 SQL 以将表标记为系统表。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="createTableOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlCeMigrationSqlGenerator.Name(System.String)">
      <summary>生成带引号的名称。提供的名称可能包含或不包含架构。</summary>
      <returns>带引号的名称。</returns>
      <param name="name">要用引号引起来的名称。</param>
    </member>
    <member name="T:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator">
      <summary>表示可将提供程序不可知的迁移操作转换为可对 Microsoft SQL Server 运行的 SQL 命令的提供程序。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.BuildColumnType(System.Data.Entity.Migrations.Model.ColumnModel)">
      <summary>生成 SQL 以指定列的数据类型。此方法只生成实际类型，而不生成 SQL 创建列。</summary>
      <returns>表示数据类型的 SQL。</returns>
      <param name="column">其中生成 SQL 的列。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.CreateConnection">
      <summary>为当前提供程序创建空连接。允许派生提供程序使用 <see cref="T:System.Data.SqlClient.SqlConnection" /> 之外的连接。</summary>
      <returns>当前提供程序的空连接。</returns>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Boolean)">
      <summary>生成 SQL 以指定将在列上设置的常量 bool 默认值。此方法只生成实际值，而不生成 SQL 设置默认值。</summary>
      <returns>表示默认值的 SQL。</returns>
      <param name="defaultValue">要设置的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Byte[])">
      <summary>生成 SQL 以指定将在列上设置的常量 byte[] 默认值。此方法只生成实际值，而不生成 SQL 设置默认值。</summary>
      <returns>表示默认值的 SQL。</returns>
      <param name="defaultValue">要设置的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Collections.Generic.IEnumerable{System.Data.Entity.Migrations.Model.MigrationOperation},System.String)">
      <summary>将一组迁移操作转换为 Microsoft SQL Server 特定 SQL。</summary>
      <returns>为执行迁移操作而要执行的 SQL 语句的列表。</returns>
      <param name="migrationOperations">要转换的操作。</param>
      <param name="providerManifestToken">表示将面向的 SQL Server 版本的标记（即“2005”，“2008”）。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.AddColumnOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.AddColumnOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="addColumnOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.AddForeignKeyOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.AddForeignKeyOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="addForeignKeyOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.AddPrimaryKeyOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.AddPrimaryKeyOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="addPrimaryKeyOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.AlterColumnOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.AlterColumnOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="alterColumnOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.CreateIndexOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.CreateIndexOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="createIndexOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.CreateTableOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.CreateTableOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="createTableOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DeleteHistoryOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.DeleteHistoryOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="deleteHistoryOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropColumnOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.DropColumnOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="dropColumnOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropForeignKeyOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.DropForeignKeyOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="dropForeignKeyOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropIndexOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.DropIndexOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="dropIndexOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropPrimaryKeyOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.DropPrimaryKeyOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="dropPrimaryKeyOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropTableOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.DropTableOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="dropTableOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.InsertHistoryOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.InsertHistoryOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="insertHistoryOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.MoveTableOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.MoveTableOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="moveTableOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.RenameColumnOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.RenameColumnOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="renameColumnOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.RenameTableOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.RenameTableOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="renameTableOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.SqlOperation)">
      <summary>为 <see cref="T:System.Data.Entity.Migrations.Model.SqlOperation" /> 生成 SQL。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="sqlOperation">为之生成 SQL 的操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Spatial.DbGeography)">
      <summary>生成 SQL 以指定将在列上设置的常量 geogrpahy 默认值。此方法只生成实际值，而不生成 SQL 设置默认值。</summary>
      <returns>表示默认值的 SQL。</returns>
      <param name="defaultValue">要设置的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Data.Spatial.DbGeometry)">
      <summary>生成 SQL 以指定将在列上设置的常量 geometry 默认值。此方法只生成实际值，而不生成 SQL 设置默认值。</summary>
      <returns>表示默认值的 SQL。</returns>
      <param name="defaultValue">要设置的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.DateTime)">
      <summary>生成 SQL 以指定将在列上设置的常量 DateTime 默认值。此方法只生成实际值，而不生成 SQL 设置默认值。</summary>
      <returns>表示默认值的 SQL。</returns>
      <param name="defaultValue">要设置的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.DateTimeOffset)">
      <summary>生成 SQL 以指定将在列上设置的常量 DateTimeOffset 默认值。此方法只生成实际值，而不生成 SQL 设置默认值。</summary>
      <returns>表示默认值的 SQL。</returns>
      <param name="defaultValue">要设置的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Guid)">
      <summary>生成 SQL 以指定将在列上设置的常量 Guid 默认值。此方法只生成实际值，而不生成 SQL 设置默认值。</summary>
      <returns>表示默认值的 SQL。</returns>
      <param name="defaultValue">要设置的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.Object)">
      <summary>生成 SQL 以指定将在列上设置的常量默认值。此方法只生成实际值，而不生成 SQL 设置默认值。</summary>
      <returns>表示默认值的 SQL。</returns>
      <param name="defaultValue">要设置的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.String)">
      <summary>生成 SQL 以指定将在列上设置的常量字符串默认值。此方法只生成实际值，而不生成 SQL 设置默认值。</summary>
      <returns>表示默认值的 SQL。</returns>
      <param name="defaultValue">要设置的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Generate(System.TimeSpan)">
      <summary>生成 SQL 以指定将在列上设置的常量 TimeSpan 默认值。此方法只生成实际值，而不生成 SQL 设置默认值。</summary>
      <returns>表示默认值的 SQL。</returns>
      <param name="defaultValue">要设置的值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.GenerateCreateSchema(System.String)">
      <summary>生成 SQL 以创建数据库架构。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="schema">要创建的数据库架构。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.GenerateMakeSystemTable(System.Data.Entity.Migrations.Model.CreateTableOperation)">
      <summary>生成 SQL 以将表标记为系统表。应使用 Statement 方法添加生成的 SQL。</summary>
      <param name="createTableOperation">要标记为系统表的表。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Name(System.String)">
      <summary>生成带引号的名称。提供的名称可能包含或不包含架构。</summary>
      <returns>生成的带引号的名称。</returns>
      <param name="name">要用引号引起来的名称。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Quote(System.String)">
      <summary>将 SQL Server 的标识符用引号引起来。</summary>
      <returns>SQL Server 的带引号的标识符。</returns>
      <param name="identifier">要用引号引起来的标识符。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Statement(System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
      <summary>添加新的要针对数据库执行的 Statement。</summary>
      <param name="writer">包含要执行的 SQL 的编写器。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Statement(System.String,System.Boolean)">
      <summary>添加新的要针对数据库执行的 Statement。</summary>
      <param name="sql">要执行的语句。</param>
      <param name="suppressTransaction">指示是否应在用于进行迁移过程事务的事务范围外执行此语句的值。如果设置为 true，则在迁移过程失败时，不会回滚此操作。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Sql.SqlServerMigrationSqlGenerator.Writer">
      <summary>获取新的可用于生成 SQL 的 <see cref="T:System.Data.Entity.Migrations.Utilities.IndentedTextWriter" />。这只是用于创建编写器的帮助器方法。写入编写器将不会导致注册 SQL 来执行。必须将生成的 SQL 传递给 Statement 方法。</summary>
      <returns>用于 SQL 生成的空文本编写器。</returns>
    </member>
    <member name="T:System.Data.Entity.Migrations.Utilities.IndentedTextWriter">
      <summary>  与 T:System.CodeDom.Compiler.IndentedTextWriter 相同，但以部分信任的方式运行。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.#ctor(System.IO.TextWriter)">
      <summary>     使用指定的文本编写器和默认 Tab 字符串，初始化 IndentedTextWriter 类的新实例。</summary>
      <param name="writer">用于输出的 <see cref="T:System.IO.TextWriter" />。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.#ctor(System.IO.TextWriter,System.String)">
      <summary>     使用指定的文本编写器和 Tab 字符串，初始化 IndentedTextWriter 类的新实例。</summary>
      <param name="writer">用于输出的 <see cref="T:System.IO.TextWriter" />。</param>
      <param name="tabString">用于缩进的 Tab 字符串。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Close">
      <summary>     关闭正在向其写入的文档。</summary>
    </member>
    <member name="F:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.DefaultTabString">
      <summary>     指定默认 Tab 字符串。此字段为常数。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Encoding">
      <summary>     获取供文本编写器使用的编码。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Flush">
      <summary>     刷新流。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Indent">
      <summary>     获取或设置要缩进的空格数。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.InnerWriter">
      <summary>     获取要使用的 <see cref="T:System.IO.TextWriter" />。</summary>
    </member>
    <member name="P:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.NewLine">
      <summary>     获取或设置要使用的换行符。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.OutputTabs">
      <summary>     根据 <see cref="P:System.CodeDom.Compiler.IndentedTextWriter.Indent" /> 属性为每个缩进级别输出一次 Tab 字符串。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Write(System.Boolean)">
      <summary>     将 Boolean 值的文本表示形式写入文本流。</summary>
      <param name="value">要写入的 Boolean 值。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Write(System.Char)">
      <summary>     将字符写入文本流。</summary>
      <param name="value">要写入的字符。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Write(System.Char[])">
      <summary>     将字符数组写入文本流。</summary>
      <param name="buffer">要写入的字符数组。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Write(System.Char[],System.Int32,System.Int32)">
      <summary>     将字符的子数组写入文本流。</summary>
      <param name="buffer">要从中写出数据的字符数组。</param>
      <param name="index">在缓冲区中开始索引。</param>
      <param name="count">要写入的字符数。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Write(System.Double)">
      <summary>     将 Double 的文本表示形式写入文本流。</summary>
      <param name="value">要写入的 double。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Write(System.Int32)">
      <summary>     将整数的文本表示形式写入文本流。</summary>
      <param name="value">要写入的整数。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Write(System.Int64)">
      <summary>     将 8 字节整数的文本表示形式写入文本流。</summary>
      <param name="value">要写入的 8 字节整数。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Write(System.Object)">
      <summary>     将对象的文本表示形式写入文本流。</summary>
      <param name="value">要写入的对象。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Write(System.Single)">
      <summary>     将 Single 的文本表示形式写入文本流。</summary>
      <param name="value">要写入的 single。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Write(System.String)">
      <summary>     将指定的字符串写入文本流。</summary>
      <param name="s">要写入的字符串。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Write(System.String,System.Object)">
      <summary>     使用与指定语义相同的语义写出已格式化的字符串。</summary>
      <param name="format">格式化字符串。</param>
      <param name="arg0">要写入已格式化字符串的对象。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Write(System.String,System.Object,System.Object)">
      <summary>     使用与指定语义相同的语义写出已格式化的字符串。</summary>
      <param name="format">要使用的格式化字符串。</param>
      <param name="arg0">第一个要写入已格式化字符串的对象。</param>
      <param name="arg1">第二个要写入已格式化字符串的对象。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.Write(System.String,System.Object[])">
      <summary>     使用与指定语义相同的语义写出已格式化的字符串。</summary>
      <param name="format">要使用的格式化字符串。</param>
      <param name="arg">要输出的参数数组。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.WriteLine">
      <summary>     写入一个行结束符。</summary>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.WriteLine(System.Boolean)">
      <summary>     将后跟行结束符的 Boolean 的文本表示形式写入文本流。</summary>
      <param name="value">要写入的 Boolean。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.WriteLine(System.Char)">
      <summary>     将后跟行结束符的字符写入文本流。</summary>
      <param name="value">要写入的字符。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.WriteLine(System.Char[])">
      <summary>     将后跟行结束符的字符数组写入文本流。</summary>
      <param name="buffer">要写入的字符数组。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.WriteLine(System.Char[],System.Int32,System.Int32)">
      <summary>     将后跟行结束符的字符子数组写入文本流。</summary>
      <param name="buffer">要从中写出数据的字符数组。</param>
      <param name="index">在缓冲区中开始索引。</param>
      <param name="count">要写入的字符数。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.WriteLine(System.Double)">
      <summary>     将后跟行结束符的 Double 的文本表示形式写入文本流。</summary>
      <param name="value">要写入的 double。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.WriteLine(System.Int32)">
      <summary>     将后跟行结束符的整数的文本表示形式写入文本流。</summary>
      <param name="value">要写入的整数。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.WriteLine(System.Int64)">
      <summary>     将后跟行结束符的 8 字节整数的文本表示形式写入文本流。</summary>
      <param name="value">要写入的 8 字节整数。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.WriteLine(System.Object)">
      <summary>     将后跟行结束符的对象的文本表示形式写入文本流。</summary>
      <param name="value">要写入的对象。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.WriteLine(System.Single)">
      <summary>     将后跟行结束符的 Single 的文本表示形式写入文本流。</summary>
      <param name="value">要写入的 single。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.WriteLine(System.String)">
      <summary>     将后跟行结束符的指定字符串写入文本流。</summary>
      <param name="s">要写入的字符串。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.WriteLine(System.String,System.Object)">
      <summary>     使用与指定语义相同的语义写出后跟行结束符的已格式化字符串。</summary>
      <param name="format">格式化字符串。</param>
      <param name="arg0">要写入已格式化字符串的对象。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.WriteLine(System.String,System.Object,System.Object)">
      <summary>     使用与指定语义相同的语义写出后跟行结束符的已格式化字符串。</summary>
      <param name="format">要使用的格式化字符串。</param>
      <param name="arg0">第一个要写入已格式化字符串的对象。</param>
      <param name="arg1">第二个要写入已格式化字符串的对象。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.WriteLine(System.String,System.Object[])">
      <summary>     使用与指定语义相同的语义写出后跟行结束符的已格式化字符串。</summary>
      <param name="format">要使用的格式化字符串。</param>
      <param name="arg">要输出的参数数组。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.WriteLine(System.UInt32)">
      <summary>     将后跟行结束符的 UInt32 的文本表示形式写入文本流。</summary>
      <param name="value">要输出的 UInt32。</param>
    </member>
    <member name="M:System.Data.Entity.Migrations.Utilities.IndentedTextWriter.WriteLineNoTabs(System.String)">
      <summary>     将指定字符串写入无制表符的行。</summary>
      <param name="s">要写入的字符串。</param>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.ComplexTypeConfiguration`1">
      <summary> 允许为模型中的复杂类型执行配置。可以使用 <see cref="M:System.Data.Entity.DbModelBuilder.ComplexType``1" /> 方法来获取 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.Types.ComplexTypeConfiguration" />，也可以使用 <see cref="P:System.Data.Entity.DbModelBuilder.Configurations" /> 属性来注册从 <see cref="T:System.Data.Entity.ModelConfiguration.ComplexTypeConfiguration`1" /> 派生的自定义类型。</summary>
      <typeparam name="TComplexType">要配置的复杂类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.ComplexTypeConfiguration`1.#ctor">
      <summary> 初始化 <see cref="T:System.Data.Entity.ModelConfiguration.ComplexTypeConfiguration`1" /> 的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.ComplexTypeConfiguration`1.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.ComplexTypeConfiguration`1.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.ComplexTypeConfiguration`1.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.ComplexTypeConfiguration`1.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1">
      <summary>允许为模型中的实体类型执行配置。可以通过对 <see cref="T:System.Data.Entity.DbModelBuilder" /> 使用 Entity 方法来获取 <see cref="T:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1" />，也可以通过对 <see cref="T:System.Data.Entity.DbModelBuilder" /> 使用 Configurations 属性来注册从 <see cref="T:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1" /> 派生的自定义类型。</summary>
      <typeparam name="TEntityType">表示实体类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1" /> 的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1.HasEntitySetName(System.String)">
      <summary> 配置要用于此实体类型的实体集名称。只能为每个集中的基类型配置实体集名称。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="entitySetName">实体集的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1.HasKey``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary> 配置此实体类型的主键属性。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="keyExpression"> 表示要用作主键的属性的 lambda 表达式。例如，在 C# 中为 t =&gt; t.Id    ，在 Visual Basic .Net 中为 Function(t) t.Id。如果主键由多个属性组成，则指定一个包括这些属性的匿名类型。例如，在 C# 中为 t =&gt; new { t.Id1, t.Id2 }，在 Visual Basic .Net 中为 Function(t) New From { t.Id1, t.Id2 }。</param>
      <typeparam name="TKey">键的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1.HasMany``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.ICollection{``0}}})">
      <summary> 从此实体类型配置一对多关系。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
      <param name="navigationPropertyExpression"> 表示关系的导航属性的 lambda 表达式。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .Net 中为 Function(t) t.MyProperty。</param>
      <typeparam name="TTargetEntity">关系另一端的实体的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1.HasOptional``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary> 从此实体类型配置可选关系。实体类型的实例将能保存到数据库，而无需指定此关系。数据库中的外键可为 null。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
      <param name="navigationPropertyExpression"> 表示关系的导航属性的 lambda 表达式。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .Net 中为 Function(t) t.MyProperty。</param>
      <typeparam name="TTargetEntity">关系另一端的实体的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1.HasRequired``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary> 通过此实体类型配置必需关系。除非指定此关系，否则实体类型的实例将无法保存到数据库。数据库中的外键不可为 null。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
      <param name="navigationPropertyExpression"> 表示关系的导航属性的 lambda 表达式。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .Net 中为 Function(t) t.MyProperty。</param>
      <typeparam name="TTargetEntity">关系另一端的实体的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1.Map``1(System.Action{System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration{``0}})">
      <summary> 允许与派生的实体类型映射到数据库架构的方式相关的高级配置。对此方法的调用是可累加的，后续调用不会重写由之前对此方法的调用所执行的配置。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="derivedTypeMapConfigurationAction">对 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration`1" /> 执行配置的操作。</param>
      <typeparam name="TDerived">要配置的派生实体类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1.Map(System.Action{System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration{`0}})">
      <summary> 允许与此实体类型映射到数据库架构的方式相关的高级配置。默认情况下，任何配置都还会应用于从此实体类型派生的任何类型。可以使用配置派生类型的此方法的特定重载，或使用派生类型的 <see cref="T:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1" />，来配置派生类型。可以使用对此方法的多个调用，在多个表之间拆分实体的属性。调用是累加性的，后续调用不会重写之前对此方法的调用已执行的配置。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="entityMappingConfigurationAction">对 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration`1" /> 执行配置的操作。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1.ToTable(System.String)">
      <summary>配置此实体类型映射到的表名。</summary>
      <param name="tableName">表的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1.ToTable(System.String,System.String)">
      <summary> 配置此实体类型映射到的表名。</summary>
      <param name="tableName">表的名称。</param>
      <param name="schemaName">表的数据库架构。</param>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.ModelValidationException">
      <summary> 在模型创建过程中，生成无效模型时由 <see cref="T:System.Data.Entity.DbModelBuilder" /> 引发的异常。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.ModelValidationException.#ctor">
      <summary> 初始化 <see cref="T:System.Data.Entity.ModelConfiguration.ModelValidationException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.ModelValidationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>初始化 <see cref="T:System.Data.Entity.ModelConfiguration.ModelValidationException" /> 类的新实例。</summary>
      <param name="info">序列化信息。</param>
      <param name="context">上下文。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.ModelValidationException.#ctor(System.String)">
      <summary> 初始化 <see cref="T:System.Data.Entity.ModelConfiguration.ModelValidationException" /> 类的新实例。</summary>
      <param name="message">异常消息。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.ModelValidationException.#ctor(System.String,System.Exception)">
      <summary> 初始化 <see cref="T:System.Data.Entity.ModelConfiguration.ModelValidationException" /> 类的新实例。</summary>
      <param name="message">异常消息。</param>
      <param name="innerException">内部异常。</param>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.AssociationMappingConfiguration">
      <summary> 用于执行关系配置的基类。此配置功能由 Code First Fluent API 公开，请参见 <see cref="T:System.Data.Entity.DbModelBuilder" />。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.AssociationMappingConfiguration.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.AssociationMappingConfiguration" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration">
      <summary> 用于配置实体类型或复杂类型的 <see cref="T:System.byte[]" /> 属性。此配置功能由 Code First Fluent API 公开，请参见 <see cref="T:System.Data.Entity.DbModelBuilder" />。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.HasColumnName(System.String)">
      <summary> 配置用于存储属性的数据库列的名称。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnName">列的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.HasColumnOrder(System.Nullable{System.Int32})">
      <summary> 配置用于存储属性的数据库列的顺序。如果实体类型具有组合键，则此方法还用于指定键顺序。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnOrder">此列应在数据库表中出现的顺序。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.HasColumnType(System.String)">
      <summary> 配置用于存储属性的数据库列的数据类型。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnType">特定于数据库提供程序的数据类型的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.HasDatabaseGeneratedOption(System.Nullable{System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption})">
      <summary>     配置数据库如何生成属性的值。</summary>
      <returns>同一个 BinaryPropertyConfiguration 实例，以便多个调用可以链接在一起。</returns>
      <param name="databaseGeneratedOption">     用于在数据库中生成属性值的模式。设置“null”将从属性中移除数据库生成的模式方面。设置“null”将导致与指定“None”相同的运行时行为。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.HasMaxLength(System.Nullable{System.Int32})">
      <summary> 将属性配置为具有指定的最大长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="value"> 属性的最大长度。设置“null”将从属性中移除任何最大长度限制。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.IsConcurrencyToken">
      <summary> 将属性配置为用作开放式并发标记。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.IsConcurrencyToken(System.Nullable{System.Boolean})">
      <summary> 配置属性是否要用作开放式并发标记。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="concurrencyToken"> 指示属性是否为并发标记的值。指定“null”将从属性中移除并发标记方面。指定“null”将导致与指定“false”相同的运行时行为。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.IsFixedLength">
      <summary> 将属性配置为固定长度。使用 HasMaxLength 可设置属性的固定长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.IsMaxLength">
      <summary> 将属性配置为允许使用数据库提供程序支持的最大长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.IsOptional">
      <summary> 将属性配置为可选属性。用于存储此属性的数据库列将可以为 null。<see cref="T:System.byte[]" /> 属性在默认情况下是可选的。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.IsRequired">
      <summary> 将属性配置为必需属性。用于存储此属性的数据库列将不可以为 null。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.IsRowVersion">
      <summary> 将属性配置为数据库中的行版本。实际数据类型将因使用的数据库提供程序而异。将属性设置为行版本会自动将属性配置为开放式并发标记。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.IsVariableLength">
      <summary> 将属性配置为可变长度。<see cref="T:System.byte[]" /> 属性在默认情况下为可变长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.BinaryPropertyConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.CascadableNavigationPropertyConfiguration">
      <summary> 配置可以支持级联删除功能的关系。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.CascadableNavigationPropertyConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.CascadableNavigationPropertyConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.CascadableNavigationPropertyConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.CascadableNavigationPropertyConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.CascadableNavigationPropertyConfiguration.WillCascadeOnDelete">
      <summary> 将级联删除配置为对关系启用。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.CascadableNavigationPropertyConfiguration.WillCascadeOnDelete(System.Boolean)">
      <summary> 配置是否对关系启用级联删除。</summary>
      <param name="value">如果启用级联删除，则为 true；否则为 false。</param>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.ConfigurationRegistrar">
      <summary> 允许实体和复杂类型的派生配置类向 <see cref="T:System.Data.Entity.DbModelBuilder" /> 注册。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ConfigurationRegistrar.Add``1(System.Data.Entity.ModelConfiguration.ComplexTypeConfiguration{``0})">
      <summary> 将 <see cref="T:System.Data.Entity.ModelConfiguration.ComplexTypeConfiguration`1" /> 添加到 <see cref="T:System.Data.Entity.DbModelBuilder" /> 中。只能为模型中的每个类型添加一个 <see cref="T:System.Data.Entity.ModelConfiguration.ComplexTypeConfiguration`1" />。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.ConfigurationRegistrar" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="complexTypeConfiguration">要添加的复杂类型配置</param>
      <typeparam name="TComplexType">要配置的复杂类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ConfigurationRegistrar.Add``1(System.Data.Entity.ModelConfiguration.EntityTypeConfiguration{``0})">
      <summary> 将 <see cref="T:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1" /> 添加到 <see cref="T:System.Data.Entity.DbModelBuilder" /> 中。只能为模型中的每个类型添加一个 <see cref="T:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1" />。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.ConfigurationRegistrar" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="entityTypeConfiguration">要添加的实体类型配置。</param>
      <typeparam name="TEntityType">要配置的实体类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ConfigurationRegistrar.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ConfigurationRegistrar.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ConfigurationRegistrar.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ConfigurationRegistrar.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.ConventionsConfiguration">
      <summary> 允许自定义 <see cref="T:System.Data.Entity.DbModelBuilder" /> 实例使用的约定。当前，移除一个或多个默认约定是支持的唯一操作。默认约定可以在 <see cref="N:System.Data.Entity.ModelConfiguration.Conventions" /> 命名空间中找到。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ConventionsConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ConventionsConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ConventionsConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ConventionsConfiguration.Remove``1">
      <summary>禁用 <see cref="T:System.Data.Entity.DbModelBuilder" /> 的约定。可在 <see cref="N:System.Data.Entity.ModelConfiguration.Conventions" /> 命名空间中找到可移除的默认约定。</summary>
      <typeparam name="TConvention">要禁用的约定的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ConventionsConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration">
      <summary> 用于配置实体类型或复杂类型的 <see cref="T:System.DateTime" /> 属性。此配置功能由 Code First Fluent API 公开。有关更多信息，请参见 <see cref="T:System.Data.Entity.DbModelBuilder" />。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration.HasColumnName(System.String)">
      <summary> 配置用于存储属性的数据库列的名称。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnName">列的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration.HasColumnOrder(System.Nullable{System.Int32})">
      <summary> 配置用于存储属性的数据库列的顺序。如果实体类型具有组合键，则此方法还用于指定键顺序。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnOrder">此列应在数据库表中出现的顺序。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration.HasColumnType(System.String)">
      <summary> 配置用于存储属性的数据库列的数据类型。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnType">特定于数据库提供程序的数据类型的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration.HasDatabaseGeneratedOption(System.Nullable{System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption})">
      <summary>     配置数据库如何生成属性的值。</summary>
      <returns>同一个 DateTimePropertyConfiguration 实例，以便多个调用可以链接在一起。</returns>
      <param name="databaseGeneratedOption">     用于在数据库中生成属性值的模式。设置“null”将从属性中移除数据库生成的模式方面。设置“null”将导致与指定“None”相同的运行时行为。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration.HasPrecision(System.Byte)">
      <summary> 配置属性的精度。如果数据库提供程序不支持列的数据类型的精度，则忽略该值。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="value">属性的精度。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration.IsConcurrencyToken">
      <summary> 将属性配置为用作开放式并发标记。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration.IsConcurrencyToken(System.Nullable{System.Boolean})">
      <summary> 配置属性是否要用作开放式并发标记。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="concurrencyToken"> 指示属性是否为并发标记的值。指定“null”将从属性中移除并发标记方面。指定“null”将导致与指定“false”相同的运行时行为。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration.IsOptional">
      <summary> 将属性配置为可选属性。用于存储此属性的数据库列将可以为 null。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration.IsRequired">
      <summary> 将属性配置为必需属性。用于存储此属性的数据库列将不可以为 null。<see cref="T:System.DateTime" /> 属性在默认情况下是必需的。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DateTimePropertyConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration">
      <summary> 用于配置实体类型或复杂类型的 <see cref="T:System.decimal" /> 属性。此配置功能由 Code First Fluent API 公开，请参见 <see cref="T:System.Data.Entity.DbModelBuilder" />。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration.HasColumnName(System.String)">
      <summary> 配置用于存储属性的数据库列的名称。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnName">列的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration.HasColumnOrder(System.Nullable{System.Int32})">
      <summary> 配置用于存储属性的数据库列的顺序。如果实体类型具有组合键，则此方法还用于指定键顺序。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnOrder">此列应在数据库表中出现的顺序。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration.HasColumnType(System.String)">
      <summary> 配置用于存储属性的数据库列的数据类型。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnType">特定于数据库提供程序的数据类型的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration.HasDatabaseGeneratedOption(System.Nullable{System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption})">
      <summary>     配置数据库如何生成属性的值。</summary>
      <returns>同一个 DecimalPropertyConfiguration 实例，以便多个调用可以链接在一起。</returns>
      <param name="databaseGeneratedOption">     用于在数据库中生成属性值的模式。设置“null”将从属性中移除数据库生成的模式方面。设置“null”将导致与指定“None”相同的运行时行为。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration.HasPrecision(System.Byte,System.Byte)">
      <summary> 配置属性的精度和小数位数。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="precision">属性的精度。</param>
      <param name="scale">属性的小数位数。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration.IsConcurrencyToken">
      <summary> 将属性配置为用作开放式并发标记。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration.IsConcurrencyToken(System.Nullable{System.Boolean})">
      <summary> 配置属性是否要用作开放式并发标记。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="concurrencyToken"> 指示属性是否为并发标记的值。指定“null”将从属性中移除并发标记方面。指定“null”将导致与指定“false”相同的运行时行为。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration.IsOptional">
      <summary> 将属性配置为可选属性。用于存储此属性的数据库列将可以为 null。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration.IsRequired">
      <summary> 将属性配置为必需属性。用于存储此属性的数据库列将不可以为 null。<see cref="T:System.decimal" /> 属性在默认情况下是必需的。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DecimalPropertyConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.DependentNavigationPropertyConfiguration`1">
      <summary> 配置可以支持在对象模型中公开的外键属性的关系。此配置功能由 Code First Fluent API 公开，请参见 <see cref="T:System.Data.Entity.DbModelBuilder" />。</summary>
      <typeparam name="TDependentEntityType">从属实体类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DependentNavigationPropertyConfiguration`1.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DependentNavigationPropertyConfiguration`1.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DependentNavigationPropertyConfiguration`1.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DependentNavigationPropertyConfiguration`1.HasForeignKey``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary> 将关系配置为使用在对象模型中公开的外键属性。如果未在对象模型中公开外键属性，则使用 Map 方法。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
      <param name="foreignKeyExpression">表示要用作外键的属性的 lambda 表达式。如果外键由多个属性组成，请指定包括这些属性的匿名类型。在使用多个外键属性时，指定这些属性的顺序必须与为主体实体类型配置主键属性的顺序相同。</param>
      <typeparam name="TKey">键的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.DependentNavigationPropertyConfiguration`1.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration`1">
      <summary> 配置实体类型或实体类型中属性子集的表和列映射。此配置功能由 Code First Fluent API 公开，请参见 <see cref="T:System.Data.Entity.DbModelBuilder" />。</summary>
      <typeparam name="TEntityType">要映射的实体类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration`1.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration`1" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration`1.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration`1.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration`1.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration`1.MapInheritedProperties">
      <summary> 重新映射从基类型继承的所有属性。当将派生类型配置为映射到单独的表时，这会使所有属性都包含在该表中，而不仅仅是非继承的属性。这称为“每个具体类型一个表”(TPC) 映射。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration`1.Properties``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary> 配置将包含在此映射片段中的属性。如果未调用此方法，则将配置尚未包含在映射片段中的所有属性。</summary>
      <param name="propertiesExpression"> 针对包含要映射的属性的匿名类型的 lambda 表达式。</param>
      <typeparam name="TObject">包含要映射的属性的匿名类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration`1.Requires``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary> 配置用于区分继承层次结构中各个类型的鉴别器条件。</summary>
      <returns>用于进一步配置鉴别器条件的配置对象。</returns>
      <param name="property"> 表示要用于鉴别各个类型的属性的 lambda 表达式。</param>
      <typeparam name="TProperty">要用于鉴别各个类型的属性的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration`1.Requires(System.String)">
      <summary> 配置用于区分继承层次结构中的各个类型的鉴别器列。</summary>
      <returns>用于进一步配置鉴别器列和值的配置对象。</returns>
      <param name="discriminator">鉴别器列的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration`1.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration`1.ToTable(System.String)">
      <summary> 配置要映射到的表名。</summary>
      <param name="tableName">表的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.EntityMappingConfiguration`1.ToTable(System.String,System.String)">
      <summary> 配置要映射到的表名和架构。</summary>
      <param name="tableName">表的名称。</param>
      <param name="schemaName">表的架构。</param>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyAssociationMappingConfiguration">
      <summary>表示外键关联的映射配置。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyAssociationMappingConfiguration.Equals(System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyAssociationMappingConfiguration)">
      <summary>确定指定的 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyAssociationMappingConfiguration" /> 对象是否等于当前对象。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="other">要与当前对象进行比较的 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyAssociationMappingConfiguration" />。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyAssociationMappingConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyAssociationMappingConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyAssociationMappingConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyAssociationMappingConfiguration.MapKey(System.String[])">
      <summary> 配置外键的列名。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyAssociationMappingConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="keyColumnNames"> 外键列名。在使用多个外键属性时，指定这些属性的顺序必须与为目标实体类型配置主键属性的顺序相同。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyAssociationMappingConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyAssociationMappingConfiguration.ToTable(System.String)">
      <summary>配置外键列所在表的名称。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyAssociationMappingConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="tableName">要配置的表的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyAssociationMappingConfiguration.ToTable(System.String,System.String)">
      <summary>配置外键列所在表的名称和架构。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyAssociationMappingConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="tableName">表的名称。</param>
      <param name="schemaName">表的架构。</param>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyNavigationPropertyConfiguration">
      <summary>配置只能支持未在对象模型中公开的外键属性的关系。此配置功能由 Code First Fluent API 公开，请参见 <see cref="T:System.Data.Entity.DbModelBuilder" />。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyNavigationPropertyConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyNavigationPropertyConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyNavigationPropertyConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyNavigationPropertyConfiguration.Map(System.Action{System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyAssociationMappingConfiguration})">
      <summary>将关系配置为使用未在对象模型中公开的外键属性。可通过指定配置操作来自定义列和表。如果指定了空的配置操作，则约定将生成列名。如果在对象模型中公开了外键属性，则使用 HasForeignKey 方法。并非所有关系都支持在对象模型中公开外键属性。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
      <param name="configurationAction">配置外键列和表的操作。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ForeignKeyNavigationPropertyConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.LengthColumnConfiguration">
      <summary> 用于为实体类型或复杂类型配置具有长度方面的列。此配置功能由 Code First Fluent API 公开，请参见 <see cref="T:System.Data.Entity.DbModelBuilder" />。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.LengthColumnConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.LengthColumnConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.LengthColumnConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.LengthColumnConfiguration.HasMaxLength(System.Nullable{System.Int32})">
      <summary> 将列配置为具有指定的最大长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.LengthColumnConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="value"> 列的最大长度。将值设置为 null 将从列中移除任何最大长度限制，并且会将默认长度用于数据库列。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.LengthColumnConfiguration.IsFixedLength">
      <summary>将列配置为固定长度。</summary>
      <returns>如果列为固定长度，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.LengthColumnConfiguration.IsMaxLength">
      <summary> 将列配置为允许使用数据库提供程序支持的最大长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.LengthColumnConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.LengthColumnConfiguration.IsVariableLength">
      <summary> 将列配置为可变长度。</summary>
      <returns>如果列为可变长度，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.LengthColumnConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.LengthPropertyConfiguration">
      <summary> 用于为实体类型或复杂类型配置具有长度方面的属性。通过 Code First Fluent API 可使用此配置功能，请参见 <see cref="T:System.Data.Entity.DbModelBuilder" />。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.LengthPropertyConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.LengthPropertyConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.LengthPropertyConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.LengthPropertyConfiguration.HasMaxLength(System.Nullable{System.Int32})">
      <summary> 将属性配置为具有指定的最大长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.LengthPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="value"> 属性的最大长度。将值设置为 null 将从属性中移除任何最大长度限制，并且会将默认长度用于数据库列。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.LengthPropertyConfiguration.IsFixedLength">
      <summary> 将属性配置为固定长度。使用 HasMaxLength 可设置属性的固定长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.LengthPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.LengthPropertyConfiguration.IsMaxLength">
      <summary> 将属性配置为允许使用数据库提供程序支持的最大长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.LengthPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.LengthPropertyConfiguration.IsVariableLength">
      <summary> 将属性配置为可变长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.LengthPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.LengthPropertyConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.ManyNavigationPropertyConfiguration`2">
      <summary> 通过实体类型配置多关系。</summary>
      <typeparam name="TEntityType">关系源自的实体类型。</typeparam>
      <typeparam name="TTargetEntityType">作为关系目标的实体类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyNavigationPropertyConfiguration`2.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyNavigationPropertyConfiguration`2.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyNavigationPropertyConfiguration`2.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyNavigationPropertyConfiguration`2.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyNavigationPropertyConfiguration`2.WithMany">
      <summary> 将关系配置为 many:many，且在关系的另一端无导航属性。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyNavigationPropertyConfiguration`2.WithMany(System.Linq.Expressions.Expression{System.Func{`1,System.Collections.Generic.ICollection{`0}}})">
      <summary> 将关系配置为 many:many，且在关系的另一端有导航属性。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
      <param name="navigationPropertyExpression"> 一个 lambda 表达式，表示关系另一端的导航属性。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .Net 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyNavigationPropertyConfiguration`2.WithOptional">
      <summary> 将关系配置为 many:optional，且在关系的另一端无导航属性。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyNavigationPropertyConfiguration`2.WithOptional(System.Linq.Expressions.Expression{System.Func{`1,`0}})">
      <summary> 将关系配置为 many:optional，且在关系的另一端有导航属性。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
      <param name="navigationPropertyExpression"> 一个 lambda 表达式，表示关系另一端的导航属性。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .Net 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyNavigationPropertyConfiguration`2.WithRequired">
      <summary> 将关系配置为 many:required，且在关系的另一端无导航属性。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyNavigationPropertyConfiguration`2.WithRequired(System.Linq.Expressions.Expression{System.Func{`1,`0}})">
      <summary> 将关系配置为 many:required，且在关系的另一端有导航属性。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
      <param name="navigationPropertyExpression"> 一个 lambda 表达式，表示关系另一端的导航属性。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .Net 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration">
      <summary> 配置 many:many 关系的表和列映射。此配置功能由 Code First Fluent API 公开，请参见 <see cref="T:System.Data.Entity.DbModelBuilder" />。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration.Equals(System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration)">
      <summary>确定指定的 <see cref="System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration.IndependentAssociationMappingConfiguration" /> 对象是否等于当前对象。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="other">
        <see cref="System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration.IndependentAssociationMappingConfiguration" /> 对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration.MapLeftKey(System.String[])">
      <summary> 配置左外键的列名。左外键表示在对 <see cref="T:System.Data.Entity.ModelConfiguration.EntityTypeConfiguration`1" /> 调用 HasMany 时指定的导航属性。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="keyColumnNames"> 外键列名。在使用多个外键属性时，必须按照为目标实体类型配置主键属性的顺序来指定这些属性。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration.MapRightKey(System.String[])">
      <summary> 配置右外键的列名。右外键表示在 WithMany 调用中指定的导航属性。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="keyColumnNames"> 外键列名。在使用多个外键属性时，必须按照为目标实体类型配置主键属性的顺序来指定这些属性。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration.ToTable(System.String)">
      <summary> 配置关系的联接表名。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="tableName">表的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration.ToTable(System.String,System.String)">
      <summary> 配置关系的联接表名和架构。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="tableName">表的名称。</param>
      <param name="schemaName">表的架构。</param>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyNavigationPropertyConfiguration">
      <summary> 配置 many:many 关系。此配置功能由 Code First Fluent API 公开，请参见 <see cref="T:System.Data.Entity.DbModelBuilder" />。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyNavigationPropertyConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyNavigationPropertyConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyNavigationPropertyConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyNavigationPropertyConfiguration.Map(System.Action{System.Data.Entity.ModelConfiguration.Configuration.ManyToManyAssociationMappingConfiguration})">
      <summary> 配置用于存储关系的外键列和表。</summary>
      <param name="configurationAction">配置外键列和表的操作。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ManyToManyNavigationPropertyConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.NotNullConditionConfiguration">
      <summary> 配置一个条件，该条件用于基于赋给属性的值来区分继承层次结构中的各个类型。此配置功能由 Code First Fluent API 公开，请参见 <see cref="T:System.Data.Entity.DbModelBuilder" />。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.NotNullConditionConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.NotNullConditionConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.NotNullConditionConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.NotNullConditionConfiguration.HasValue">
      <summary> 将条件配置为属性中需要值。对于未向存储此属性的列赋值的行，假定这些行属于此实体类型的基类型。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.NotNullConditionConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.OptionalNavigationPropertyConfiguration`2">
      <summary>通过实体类型配置可选关系。</summary>
      <typeparam name="TEntityType">关系源自的实体类型。</typeparam>
      <typeparam name="TTargetEntityType">作为关系目标的实体类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.OptionalNavigationPropertyConfiguration`2.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.OptionalNavigationPropertyConfiguration`2.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.OptionalNavigationPropertyConfiguration`2.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.OptionalNavigationPropertyConfiguration`2.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.OptionalNavigationPropertyConfiguration`2.WithMany">
      <summary> 将关系配置为 optional:many，且在关系的另一端无导航属性。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.OptionalNavigationPropertyConfiguration`2.WithMany(System.Linq.Expressions.Expression{System.Func{`1,System.Collections.Generic.ICollection{`0}}})">
      <summary> 将关系配置为 optional:many，且在关系的另一端有导航属性。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
      <param name="navigationPropertyExpression"> 一个 lambda 表达式，表示关系另一端的导航属性。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .Net 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.OptionalNavigationPropertyConfiguration`2.WithOptionalDependent">
      <summary> 将关系配置为 optional:optional，且在关系的另一端无导航属性。要配置的实体类型将成为依赖对象，且包含主体的外键。作为关系目标的实体类型将成为关系中的主体。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.OptionalNavigationPropertyConfiguration`2.WithOptionalDependent(System.Linq.Expressions.Expression{System.Func{`1,`0}})">
      <summary> 将关系配置为 optional:optional，且在关系的另一端有导航属性。要配置的实体类型将成为依赖对象，且包含主体的外键。作为关系目标的实体类型将成为关系中的主体。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
      <param name="navigationPropertyExpression"> 一个 lambda 表达式，表示关系另一端的导航属性。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .Net 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.OptionalNavigationPropertyConfiguration`2.WithOptionalPrincipal">
      <summary> 将关系配置为 optional:optional，且在关系的另一端无导航属性。要配置的实体类型将成为关系中的主体。作为关系目标的实体类型将成为依赖对象，且包含主体的外键。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.OptionalNavigationPropertyConfiguration`2.WithOptionalPrincipal(System.Linq.Expressions.Expression{System.Func{`1,`0}})">
      <summary> 将关系配置为 optional:optional，且在关系的另一端有导航属性。要配置的实体类型将成为关系中的主体。作为关系目标的实体类型将成为依赖对象，且包含主体的外键。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
      <param name="navigationPropertyExpression"> 一个 lambda 表达式，表示关系另一端的导航属性。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.OptionalNavigationPropertyConfiguration`2.WithRequired">
      <summary>将关系的指定端配置为必需的，且在关系的另一端没有导航属性。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.OptionalNavigationPropertyConfiguration`2.WithRequired(System.Linq.Expressions.Expression{System.Func{`1,`0}})">
      <summary>将关系的指定端配置为必需的，且在关系的另一端有导航属性。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
      <param name="navigationPropertyExpression"> 一个 lambda 表达式，表示关系另一端的导航属性。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .Net 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.PrimitiveColumnConfiguration">
      <summary>通过实体类型配置基元列。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitiveColumnConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitiveColumnConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitiveColumnConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitiveColumnConfiguration.HasColumnOrder(System.Nullable{System.Int32})">
      <summary> 配置用于存储属性的基元列的顺序。如果实体类型具有组合键，则此方法还用于指定键顺序。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.PrimitiveColumnConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnOrder">此列应在数据库表中出现的顺序。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitiveColumnConfiguration.HasColumnType(System.String)">
      <summary> 配置用于存储属性的基元列的数据类型。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.PrimitiveColumnConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnType">特定于数据库提供程序的数据类型的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitiveColumnConfiguration.IsOptional">
      <summary>将基元列配置为可选列。</summary>
      <returns>如果列为可选列，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitiveColumnConfiguration.IsRequired">
      <summary>将基元列配置为必需列。</summary>
      <returns>如果列为必需列，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitiveColumnConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration">
      <summary> 用于配置实体类型或复杂类型的基元属性。此配置功能由 Code First Fluent API 公开，请参见 <see cref="T:System.Data.Entity.DbModelBuilder" />。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration.HasColumnName(System.String)">
      <summary> 配置用于存储属性的数据库列的名称。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnName">列的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration.HasColumnOrder(System.Nullable{System.Int32})">
      <summary> 配置用于存储属性的数据库列的顺序。如果实体类型具有组合键，则此方法还用于指定键顺序。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnOrder">此列应在数据库表中出现的顺序。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration.HasColumnType(System.String)">
      <summary> 配置用于存储属性的数据库列的数据类型。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnType">特定于数据库提供程序的数据类型的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration.HasDatabaseGeneratedOption(System.Nullable{System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption})">
      <summary>     配置数据库如何生成属性的值。</summary>
      <returns>同一个 PrimitivePropertyConfiguration 实例，以便多个调用可以链接在一起。</returns>
      <param name="databaseGeneratedOption">     用于在数据库中生成属性值的模式。设置“null”将从属性中移除数据库生成的模式方面。设置“null”将导致与指定“None”相同的运行时行为。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration.IsConcurrencyToken">
      <summary> 将属性配置为用作开放式并发标记。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration.IsConcurrencyToken(System.Nullable{System.Boolean})">
      <summary> 配置属性是否要用作开放式并发标记。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="concurrencyToken"> 指示属性是否为并发标记的值。指定 null 将从属性中移除并发标记方面。指定 null 将形成与指定 false 相同的运行时行为。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration.IsOptional">
      <summary> 将属性配置为可选属性。用于存储此属性的数据库列将可以为 null。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration.IsRequired">
      <summary> 将属性配置为必需属性。用于存储此属性的数据库列将不可以为 null。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.PrimitivePropertyConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.RequiredNavigationPropertyConfiguration`2">
      <summary> 通过实体类型配置必需关系。</summary>
      <typeparam name="TEntityType">关系源自的实体类型。</typeparam>
      <typeparam name="TTargetEntityType">作为关系目标的实体类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.RequiredNavigationPropertyConfiguration`2.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.RequiredNavigationPropertyConfiguration`2.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.RequiredNavigationPropertyConfiguration`2.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.RequiredNavigationPropertyConfiguration`2.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.RequiredNavigationPropertyConfiguration`2.WithMany">
      <summary> 将关系配置为 required:many，且在关系的另一端无导航属性。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.RequiredNavigationPropertyConfiguration`2.WithMany(System.Linq.Expressions.Expression{System.Func{`1,System.Collections.Generic.ICollection{`0}}})">
      <summary> 将关系配置为 required:many，且在关系的另一端有导航属性。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
      <param name="navigationPropertyExpression"> 一个 lambda 表达式，表示关系另一端的导航属性。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .Net 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.RequiredNavigationPropertyConfiguration`2.WithOptional">
      <summary> 将关系配置为 required:optional，且在关系的另一端无导航属性。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.RequiredNavigationPropertyConfiguration`2.WithOptional(System.Linq.Expressions.Expression{System.Func{`1,`0}})">
      <summary> 将关系配置为 required:optional，且在关系的另一端有导航属性。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
      <param name="navigationPropertyExpression"> 一个 lambda 表达式，表示关系另一端的导航属性。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .Net 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.RequiredNavigationPropertyConfiguration`2.WithRequiredDependent">
      <summary> 将关系配置为 required:required，且在关系的另一端无导航属性。要配置的实体类型将成为依赖对象，且包含主体的外键。作为关系目标的实体类型将成为关系中的主体。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.RequiredNavigationPropertyConfiguration`2.WithRequiredDependent(System.Linq.Expressions.Expression{System.Func{`1,`0}})">
      <summary> 将关系配置为 required:required，且在关系的另一端有导航属性。要配置的实体类型将成为依赖对象，且包含主体的外键。作为关系目标的实体类型将成为关系中的主体。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
      <param name="navigationPropertyExpression"> 一个 lambda 表达式，表示关系另一端的导航属性。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .Net 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.RequiredNavigationPropertyConfiguration`2.WithRequiredPrincipal">
      <summary> 将关系配置为 required:required，且在关系的另一端无导航属性。要配置的实体类型将成为关系中的主体。作为关系目标的实体类型将成为依赖对象，且包含主体的外键。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.RequiredNavigationPropertyConfiguration`2.WithRequiredPrincipal(System.Linq.Expressions.Expression{System.Func{`1,`0}})">
      <summary> 将关系配置为 required:required，且在关系的另一端有导航属性。要配置的实体类型将成为关系中的主体。作为关系目标的实体类型将成为依赖对象，且包含主体的外键。</summary>
      <returns>可用于进一步配置关系的配置对象。</returns>
      <param name="navigationPropertyExpression"> 一个 lambda 表达式，表示关系另一端的导航属性。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .Net 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration">
      <summary> 配置用于存储字符串值的数据库列。此配置功能由 Code First Fluent API 公开。有关更多信息，请参见 <see cref="T:System.Data.Entity.DbModelBuilder" />。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration.HasColumnOrder(System.Nullable{System.Int32})">
      <summary> 配置数据库列的顺序。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnOrder">此列应在数据库表中出现的顺序。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration.HasColumnType(System.String)">
      <summary> 配置数据库列的数据类型。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnType">特定于数据库提供程序的数据类型的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration.HasMaxLength(System.Nullable{System.Int32})">
      <summary>将属性配置为具有指定的最大长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="value">属性的最大长度。将值设置为 null 会使得将默认长度用于列。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration.IsFixedLength">
      <summary> 将列配置为固定长度。使用 HasMaxLength 可设置属性的固定长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration.IsMaxLength">
      <summary> 将列配置为允许使用数据库提供程序支持的最大长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration.IsOptional">
      <summary> 将列配置为可选列。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration.IsRequired">
      <summary> 将列配置为必需。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration.IsUnicode">
      <summary> 将列配置为支持 Unicode 字符串内容。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration.IsUnicode(System.Nullable{System.Boolean})">
      <summary> 配置列是否支持 Unicode 字符串内容。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="unicode"> 指示列是否支持 Unicode 字符串内容的值。指定 null 将从列中移除 Unicode 方面。指定 null 将形成与指定 false 相同的运行时行为。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration.IsVariableLength">
      <summary> 将列配置为可变长度。列在默认情况下为可变长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringColumnConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration">
      <summary>用于配置实体类型或复杂类型的 <see cref="T:System.string" /> 属性。此配置功能由 Code First Fluent API 公开，请参见 <see cref="T:System.Data.Entity.DbModelBuilder" />。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.HasColumnName(System.String)">
      <summary>配置用于存储属性的数据库列的名称。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnName">列的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.HasColumnOrder(System.Nullable{System.Int32})">
      <summary> 配置用于存储属性的数据库列的顺序。如果实体类型具有组合键，则此方法还用于指定键顺序。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnOrder">此列应在数据库表中出现的顺序。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.HasColumnType(System.String)">
      <summary> 配置用于存储属性的数据库列的数据类型。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="columnType">特定于数据库提供程序的数据类型的名称。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.HasDatabaseGeneratedOption(System.Nullable{System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption})">
      <summary>     配置数据库如何生成属性的值。</summary>
      <returns>同一个 StringPropertyConfiguration 实例，以便多个调用可以链接在一起。</returns>
      <param name="databaseGeneratedOption">     用于在数据库中生成属性值的模式。设置“null”将从属性中移除数据库生成的模式方面。设置“null”将导致与指定“None”相同的运行时行为。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.HasMaxLength(System.Nullable{System.Int32})">
      <summary>将属性配置为具有指定的最大长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="value">属性的最大长度。将此属性设置为 null 将从属性中移除任何最大长度限制，并且会将默认长度用于数据库列。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.IsConcurrencyToken">
      <summary> 将属性配置为用作开放式并发标记。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.IsConcurrencyToken(System.Nullable{System.Boolean})">
      <summary> 配置属性是否要用作开放式并发标记。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="concurrencyToken"> 指定属性是否为并发标记。指定 null 将从属性中移除并发标记方面。指定 null 将形成与指定 false 相同的运行时行为。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.IsFixedLength">
      <summary>将属性配置为固定长度。使用 HasMaxLength 可设置属性的固定长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.IsMaxLength">
      <summary>将属性配置为允许使用数据库提供程序支持的最大长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.IsOptional">
      <summary>将属性配置为可选属性。用于存储此属性的数据库列将可以为 null。<see cref="T:System.string" /> 属性在默认情况下是可选的。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.IsRequired">
      <summary> 将属性配置为必需属性。用于存储此属性的数据库列将不可以为 null。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.IsUnicode">
      <summary>将属性配置为支持 Unicode 字符串内容。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.IsUnicode(System.Nullable{System.Boolean})">
      <summary>配置属性是否支持 Unicode 字符串内容。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
      <param name="unicode"> 指示属性是否支持 Unicode 字符串内容的值。指定 null 将从属性中移除 Unicode 方面。指定 null 将形成与指定 false 相同的运行时行为。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.IsVariableLength">
      <summary>将属性配置为可变长度。<see cref="T:System.string" /> 属性在默认情况下为可变长度。</summary>
      <returns>同一个 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration" /> 实例，以便多个调用可以链接在一起。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StringPropertyConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1">
      <summary>允许为模型中的类型执行配置。</summary>
      <typeparam name="TStructuralType">要配置的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.Ignore``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary> 从模型中排除某个属性，使该属性不会映射到数据库。</summary>
      <param name="propertyExpression"> 表示要配置的属性的 lambda 表达式。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .NET 中为 Function(t) t.MyProperty。</param>
      <typeparam name="TProperty">要忽略的属性的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.Property(System.Linq.Expressions.Expression{System.Func{`0,System.Byte[]}})">
      <summary> 配置在此类型上定义的 <see cref="T:System.byte[]" /> 属性。</summary>
      <returns>可用于配置属性的配置对象。</returns>
      <param name="propertyExpression"> 表示要配置的属性的 lambda 表达式。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .NET 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.Property(System.Linq.Expressions.Expression{System.Func{`0,System.Data.Spatial.DbGeography}})">
      <summary>   配置在此类型上定义的 <see cref="T:DbGeography" /> 属性。</summary>
      <returns>可用于配置属性的配置对象。</returns>
      <param name="propertyExpression">   一个 lambda 表达式，表示要配置的属性。C#: t =&amp;gt; t.MyProperty      VB.Net: Function(t) t.MyProperty</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.Property(System.Linq.Expressions.Expression{System.Func{`0,System.Data.Spatial.DbGeometry}})">
      <summary>   配置在此类型上定义的 <see cref="T:DbGeometry" /> 属性。</summary>
      <returns>可用于配置属性的配置对象。</returns>
      <param name="propertyExpression">   一个 lambda 表达式，表示要配置的属性。C#: t =&amp;gt; t.MyProperty      VB.Net: Function(t) t.MyProperty</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.Property(System.Linq.Expressions.Expression{System.Func{`0,System.DateTime}})">
      <summary> 配置在此类型上定义的 <see cref="T:System.DateTime" /> 属性。</summary>
      <returns>可用于配置属性的配置对象。</returns>
      <param name="propertyExpression"> 表示要配置的属性的 lambda 表达式。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .NET 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.Property(System.Linq.Expressions.Expression{System.Func{`0,System.DateTimeOffset}})">
      <summary> 配置在此类型上定义的 <see cref="T:System.DateTimeOffset" /> 属性。</summary>
      <returns>可用于配置属性的配置对象。</returns>
      <param name="propertyExpression"> 表示要配置的属性的 lambda 表达式。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .NET 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.Property(System.Linq.Expressions.Expression{System.Func{`0,System.Decimal}})">
      <summary> 配置在此类型上定义的 <see cref="T:System.decimal" /> 属性。</summary>
      <returns>可用于配置属性的配置对象。</returns>
      <param name="propertyExpression"> 表示要配置的属性的 lambda 表达式。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .NET 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.Property(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.DateTime}}})">
      <summary> 配置在此类型上定义的 Nullable(<see cref="T:System.DateTime" />) 属性。</summary>
      <returns>可用于配置属性的配置对象。</returns>
      <param name="propertyExpression"> 一个 lambda 表达式，表示要配置的属性。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .NET 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.Property(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.DateTimeOffset}}})">
      <summary> 配置在此类型上定义的 Nullable(<see cref="T:System.DateTimeOffset" />) 属性。</summary>
      <returns>可用于配置属性的配置对象。</returns>
      <param name="propertyExpression"> 一个 lambda 表达式，表示要配置的属性。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .NET 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.Property(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Decimal}}})">
      <summary> 配置在此类型上定义的 Nullable(<see cref="T:System.Decimal" />) 属性。</summary>
      <returns>可用于配置属性的配置对象。</returns>
      <param name="propertyExpression"> 一个 lambda 表达式，表示要配置的属性。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .NET 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.Property(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.TimeSpan}}})">
      <summary> 配置在此类型上定义的 Nullable(<see cref="T:System.TimeSpan" />) 属性。</summary>
      <returns>可用于配置属性的配置对象。</returns>
      <param name="propertyExpression"> 一个 lambda 表达式，表示要配置的属性。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .NET 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.Property``1(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{``0}}})">
      <summary> 配置在此类型上定义的 Nullable(<see cref="T:System.struct" />) 属性。</summary>
      <returns>可用于配置属性的配置对象。</returns>
      <param name="propertyExpression">表示要配置的属性的 lambda 表达式。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .NET 中为 Function(t) t.MyProperty。</param>
      <typeparam name="T">要配置的属性的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.Property(System.Linq.Expressions.Expression{System.Func{`0,System.String}})">
      <summary> 配置在此类型上定义的 <see cref="T:System.string" /> 属性。</summary>
      <returns>可用于配置属性的配置对象。</returns>
      <param name="propertyExpression">表示要配置的属性的 lambda 表达式。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .NET 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.Property(System.Linq.Expressions.Expression{System.Func{`0,System.TimeSpan}})">
      <summary> 配置在此类型上定义的 <see cref="T:System.TimeSpan" /> 属性。</summary>
      <returns>可用于配置属性的配置对象。</returns>
      <param name="propertyExpression"> 表示要配置的属性的 lambda 表达式。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .NET 中为 Function(t) t.MyProperty。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.Property``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary> 配置在此类型上定义的 <see cref="T:System.struct" /> 属性。</summary>
      <returns>可用于配置属性的配置对象。</returns>
      <param name="propertyExpression"> 表示要配置的属性的 lambda 表达式。例如，在 C# 中为 t =&gt; t.MyProperty，在 Visual Basic .NET 中为 Function(t) t.MyProperty。</param>
      <typeparam name="T">要配置的属性的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.StructuralTypeConfiguration`1.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Configuration.ValueConditionConfiguration">
      <summary> 配置用于区分继承层次结构中各个类型的鉴别器列。此配置功能由 Code First Fluent API 公开。有关更多信息，请参见 <see cref="T:System.Data.Entity.DbModelBuilder" />。</summary>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ValueConditionConfiguration.Equals(System.Object)">
      <summary>确定指定的对象是否等于当前对象。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>如果对象相等，则为 true；否则为 false。</returns>
      <param name="obj">一个对象。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ValueConditionConfiguration.GetHashCode">
      <summary>用作特定类型的哈希函数。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前类型的哈希代码。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ValueConditionConfiguration.GetType">
      <summary>获取当前实例的类型。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>当前实例的准确运行时类型。</returns>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ValueConditionConfiguration.HasValue``1(System.Nullable{``0})">
      <summary>配置用于标识通过继承层次结构中其他类型配置的实体类型的鉴别器值。</summary>
      <returns>用来配置用于存储鉴别器值的列的配置对象。</returns>
      <param name="value">用于标识实体类型的值。</param>
      <typeparam name="T">鉴别器值的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ValueConditionConfiguration.HasValue(System.String)">
      <summary>配置用于标识通过继承层次结构中其他类型配置的实体类型的鉴别器值。</summary>
      <returns>用来配置用于存储鉴别器值的列的配置对象。</returns>
      <param name="value">用于标识实体类型的值。</param>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ValueConditionConfiguration.HasValue``1(``0)">
      <summary> 配置用于标识通过继承层次结构中其他类型配置的实体类型的鉴别器值。</summary>
      <returns>用来配置用于存储鉴别器值的列的配置对象。</returns>
      <param name="value">用于标识实体类型的值。</param>
      <typeparam name="T">鉴别器值的类型。</typeparam>
    </member>
    <member name="M:System.Data.Entity.ModelConfiguration.Configuration.ValueConditionConfiguration.ToString">
      <summary>返回表示当前对象的字符串。此方法对于 IntelliSense 是隐藏的。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.AssociationInverseDiscoveryConvention">
      <summary>用于在相关类型之间只存在一对导航属性时，将导航属性检测为相互反向的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.ColumnAttributeConvention">
      <summary>用于处理在模型中属性上发现的 <see cref="T:System.ComponentModel.DataAnnotations.ColumnAttribute" /> 实例的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.ColumnTypeCasingConvention">
      <summary> 用于将显式指定（使用数据批注或 <see cref="T:System.Data.Entity.DbModelBuilder" /> API）的任何数据类型转换为小写的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.ComplexTypeAttributeConvention">
      <summary>用于处理在模型中类型上发现的 <see cref="T:System.ComponentModel.DataAnnotations.ComplexTypeAttribute" /> 实例的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.ComplexTypeDiscoveryConvention">
      <summary>用于在类型没有主键、没有映射的基类型且没有导航属性的情况下将其配置为复杂类型的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.ConcurrencyCheckAttributeConvention">
      <summary>用于处理在模型中属性上发现的 <see cref="T:System.ComponentModel.DataAnnotations.ConcurrencyCheckAttribute" /> 实例的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.DatabaseGeneratedAttributeConvention">
      <summary> 用于处理在模型中属性上发现的 <see cref="T:System.ComponentModel.DataAnnotations.DatabaseGeneratedAttribute" /> 实例的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.DecimalPropertyConvention">
      <summary>用于为小数属性将精度设置为 18 并将小数位数设置为 2 的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.DeclaredPropertyOrderingConvention">
      <summary>用于移动主键属性使其首先出现的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.ForeignKeyAssociationMultiplicityConvention">
      <summary>用于基于外键属性的 CLR 可空性区分可选和必需关系的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.ForeignKeyNavigationPropertyAttributeConvention">
      <summary>用于处理在模型中导航属性上发现的 <see cref="T:System.ComponentModel.DataAnnotations.ForeignKeyAttribute" /> 实例的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.ForeignKeyPrimitivePropertyAttributeConvention">
      <summary>用于处理在模型中的外键属性上发现的 <see cref="T:System.ComponentModel.DataAnnotations.ForeignKeyAttribute" /> 的实例的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.IConvention">
      <summary>标识可以从 <see cref="T:System.Data.Entity.DbModelBuilder" /> 实例中移除的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.IdKeyDiscoveryConvention">
      <summary>用于检测主键属性的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.InversePropertyAttributeConvention">
      <summary>用于处理在模型中属性上发现的 <see cref="T:System.ComponentModel.DataAnnotations.InversePropertyAttribute" /> 实例的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.KeyAttributeConvention">
      <summary>用于处理在模型中属性上发现的 <see cref="T:System.ComponentModel.DataAnnotations.KeyAttribute" /> 实例的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.ManyToManyCascadeDeleteConvention">
      <summary>用于将级联删除从多对多关系中涉及的两个表添加到联接表的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.MappingInheritedPropertiesSupportConvention">
      <summary>确保映射继承属性不会创建无效或不支持的映射</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.MaxLengthAttributeConvention">
      <summary>用于处理在模型中属性上发现的 <see cref="T:System.ComponentModel.DataAnnotations.MaxLengthAttribute" /> 实例的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.NavigationPropertyNameForeignKeyDiscoveryConvention">
      <summary>用于发现名称是从属导航属性名称和主体类型主键属性名称组合的外键属性的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.NotMappedPropertyAttributeConvention">
      <summary>用于处理在模型中属性上发现的 <see cref="T:System.ComponentModel.DataAnnotations.NotMappedAttribute" /> 实例的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.NotMappedTypeAttributeConvention">
      <summary>用于处理在模型中类型上发现的 <see cref="T:System.ComponentModel.DataAnnotations.NotMappedAttribute" /> 实例的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.OneToManyCascadeDeleteConvention">
      <summary>用于为任何必需关系启用级联删除的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.OneToOneConstraintIntroductionConvention">
      <summary>用于将从属实体类型的主键配置为一对一关系中的外键的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.PluralizingEntitySetNameConvention">
      <summary>用于将实体集名称设置为实体类型名称的复数版本的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.PluralizingTableNameConvention">
      <summary>用于将表名设置为实体类型名称的复数版本的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.PrimaryKeyNameForeignKeyDiscoveryConvention">
      <summary>用于发现名称与主体类型主键属性名称匹配的外键属性的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.PropertyMaxLengthConvention">
      <summary>用于为支持长度方面的属性类型设置默认最大长度的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.RequiredNavigationPropertyAttributeConvention">
      <summary>用于处理在模型中导航属性上发现的 <see cref="T:System.ComponentModel.DataAnnotations.RequiredAttribute" /> 实例的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.RequiredPrimitivePropertyAttributeConvention">
      <summary>用于处理在模型中基元属性上发现的 <see cref="T:System.ComponentModel.DataAnnotations.RequiredAttribute" /> 实例的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.SqlCePropertyMaxLengthConvention">
      <summary>为其类型在 SqlCe 为提供程序时支持长度数据的属性设置最大长度为 4000 的默认值。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.StoreGeneratedIdentityKeyConvention">
      <summary>用于将整数主键配置为标识的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.StringLengthAttributeConvention">
      <summary>用于处理在模型中属性上发现的 <see cref="T:System.ComponentModel.DataAnnotations.StringLengthAttribute" /> 实例的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.TableAttributeConvention">
      <summary>用于处理在模型中类型上发现的 <see cref="T:System.ComponentModel.DataAnnotations.TableAttribute" /> 实例的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.TimestampAttributeConvention">
      <summary>用于处理在模型中属性上发现的 <see cref="T:System.ComponentModel.DataAnnotations.TimestampAttribute" /> 实例的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.ModelConfiguration.Conventions.TypeNameForeignKeyDiscoveryConvention">
      <summary>用于发现名称是主体类型名称和主体类型主键属性名称组合的外键属性的约定。</summary>
    </member>
    <member name="T:System.Data.Entity.Validation.DbEntityValidationException">
      <summary>在实体验证失败时，从 <see cref="M:System.Data.Entity.DbContext.SaveChanges" /> 引发的异常。</summary>
    </member>
    <member name="M:System.Data.Entity.Validation.DbEntityValidationException.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Validation.DbEntityValidationException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.Validation.DbEntityValidationException.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Data.Entity.Validation.DbEntityValidationException" /> 类的新实例。</summary>
      <param name="message">异常消息。</param>
    </member>
    <member name="M:System.Data.Entity.Validation.DbEntityValidationException.#ctor(System.String,System.Collections.Generic.IEnumerable{System.Data.Entity.Validation.DbEntityValidationResult})">
      <summary>初始化 <see cref="T:System.Data.Entity.Validation.DbEntityValidationException" /> 类的新实例。</summary>
      <param name="message">异常消息。</param>
      <param name="entityValidationResults">验证结果。</param>
    </member>
    <member name="M:System.Data.Entity.Validation.DbEntityValidationException.#ctor(System.String,System.Collections.Generic.IEnumerable{System.Data.Entity.Validation.DbEntityValidationResult},System.Exception)">
      <summary>初始化 <see cref="T:System.Data.Entity.Validation.DbEntityValidationException" /> 类的新实例。</summary>
      <param name="message">异常消息。</param>
      <param name="entityValidationResults">验证结果。</param>
      <param name="innerException">内部异常。</param>
    </member>
    <member name="M:System.Data.Entity.Validation.DbEntityValidationException.#ctor(System.String,System.Exception)">
      <summary>初始化 <see cref="T:System.Data.Entity.Validation.DbEntityValidationException" /> 类的新实例。</summary>
      <param name="message">异常消息。</param>
      <param name="innerException">内部异常。</param>
    </member>
    <member name="P:System.Data.Entity.Validation.DbEntityValidationException.EntityValidationErrors">
      <summary>返回所有验证错误。</summary>
      <returns>验证错误（如果有）。</returns>
    </member>
    <member name="T:System.Data.Entity.Validation.DbEntityValidationResult">
      <summary>表示单个实体的验证结果。</summary>
    </member>
    <member name="M:System.Data.Entity.Validation.DbEntityValidationResult.#ctor(System.Data.Entity.Infrastructure.DbEntityEntry,System.Collections.Generic.IEnumerable{System.Data.Entity.Validation.DbValidationError})">
      <summary>创建 <see cref="T:System.Data.Entity.Validation.DbEntityValidationResult" /> 类的实例。</summary>
      <param name="entry">应用结果的实体项。绝不会为 null。</param>
      <param name="validationErrors">
        <see cref="T:System.Data.Entity.Validation.DbValidationError" /> 实例的列表。这绝不会为 null，但是可以为空（这表示实体有效）。</param>
    </member>
    <member name="P:System.Data.Entity.Validation.DbEntityValidationResult.Entry">
      <summary>获取应用结果的 <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" /> 的实例。</summary>
      <returns>
        <see cref="T:System.Data.Entity.Infrastructure.DbEntityEntry" />。</returns>
    </member>
    <member name="P:System.Data.Entity.Validation.DbEntityValidationResult.IsValid">
      <summary>获取指示实体是否有效的指示器。</summary>
      <returns>如果实体有效，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Data.Entity.Validation.DbEntityValidationResult.ValidationErrors">
      <summary>获取验证错误。此属性绝不会为 null。</summary>
      <returns>验证错误。</returns>
    </member>
    <member name="T:System.Data.Entity.Validation.DbUnexpectedValidationException">
      <summary>在从验证代码引发异常时，从 <see cref="M:System.Data.Entity.DbContext.GetValidationErrors" /> 引发的异常。</summary>
    </member>
    <member name="M:System.Data.Entity.Validation.DbUnexpectedValidationException.#ctor">
      <summary>初始化 <see cref="T:System.Data.Entity.Validation.DbUnexpectedValidationException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Entity.Validation.DbUnexpectedValidationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>用指定的序列化信息和上下文初始化 <see cref="T:System.Data.Entity.Validation.DbUnexpectedValidationException" /> 类的新实例。</summary>
      <param name="info">序列化信息。</param>
      <param name="context">流上下文。</param>
    </member>
    <member name="M:System.Data.Entity.Validation.DbUnexpectedValidationException.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Data.Entity.Validation.DbUnexpectedValidationException" /> 类的新实例。</summary>
      <param name="message">异常消息。</param>
    </member>
    <member name="M:System.Data.Entity.Validation.DbUnexpectedValidationException.#ctor(System.String,System.Exception)">
      <summary>初始化 <see cref="T:System.Data.Entity.Validation.DbUnexpectedValidationException" /> 类的新实例。</summary>
      <param name="message">异常消息。</param>
      <param name="innerException">内部异常。</param>
    </member>
    <member name="T:System.Data.Entity.Validation.DbValidationError">
      <summary>验证错误。可以是实体级别或属性级别验证错误。</summary>
    </member>
    <member name="M:System.Data.Entity.Validation.DbValidationError.#ctor(System.String,System.String)">
      <summary>创建 <see cref="T:System.Data.Entity.Validation.DbValidationError" /> 的实例。</summary>
      <param name="propertyName">无效属性的名称。可以为 null。</param>
      <param name="errorMessage">验证错误消息。可以为 null。</param>
    </member>
    <member name="P:System.Data.Entity.Validation.DbValidationError.ErrorMessage">
      <summary>获取验证错误消息。</summary>
      <returns>验证错误消息。</returns>
    </member>
    <member name="P:System.Data.Entity.Validation.DbValidationError.PropertyName">
      <summary>获取无效属性的名称。</summary>
      <returns>无效属性的名称。</returns>
    </member>
  </members>
</doc>