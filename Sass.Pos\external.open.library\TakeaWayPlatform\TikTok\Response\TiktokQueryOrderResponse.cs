﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace external.open.library.TakeaWayPlatform.TikTok.Response
{
    public class TiktokQueryOrderResponse
    {
        public string description { get; set; }
        public int error_code { get; set; }

        public List<OrderInfo> orders { get; set; }

        public PageInfo page { get; set; }

        public object search_after { get; set; }
    }
    public class OrderInfo
    {
        /// <summary>
        /// 金额信息
        /// </summary>
        public AmountInfo amount_info { get; set; }
        /// <summary>
        /// 卡券信息
        /// </summary>
        public List<CertificateInfo> certificate { get; set; }

        /// <summary>
        /// 该笔订单包含的券数量
        /// </summary>
        public int count { get; set; }

        public string order_id { get; set; }

        public int order_status { get; set; }

        public int original_amount { get; set; }

        public int pay_amount { get; set; }
        public long pay_time { get; set; }
        /// <summary>
        /// 订单类型51,70为次卡
        /// </summary>
        public int order_type { get; set; }
    }

    public class AmountInfo
    {
        public long activities_fee_amount { get; set; }
        public long commission_amount { get; set; }
        public long estimated_order_income { get; set; }
        public long freight_pay_amount { get; set; }
        public long merchant_deliver_freight_fee { get; set; }
        public long merchant_discount_amount { get; set; }
        public long origin_amount { get; set; }
        public long pay_amount { get; set; }
        public long pay_discount_amount { get; set; }
        public long platform_deliver_freight_fee { get; set; }
        public long platform_discount_amount { get; set; }
        public long product_origin_amount { get; set; }
        public long provider_discount_amount { get; set; }
        public long sale_price { get; set; }
    }

    public class CertificateInfo
    {
        public string certificate_id { get; set; }
        public string combination_id { get; set; }
        public int item_status { get; set; }
        public long item_update_time { get; set; }
        public string order_item_id { get; set; }
        public int refund_amount { get; set; }
        public long refund_time { get; set; }
    }

    public class PageInfo
    {
        public int page_num { get; set; }
        public int page_size { get; set; }
        public int total { get; set; }
    }

    public class SearchAfter
    {
        public int Size { get; set; }

        public List<List<string>> CursorValue { get; set; }
    }
}
