﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Saas.Pos.Common.Factory
{
    /// <summary>
    /// APP工厂类
    /// </summary>
    public static class AppFactory
    {




        /// <summary>
        /// 创建反射实例
        /// </summary>
        /// <param name="assemblyName"></param>
        /// <param name="nameSpace"></param>
        /// <param name="className"></param>
        /// <returns></returns>
        static T CreateInstance<T>(string assemblyName, string nameSpace, string className)
        {
            try
            {
                string fullName = nameSpace + "." + className;//命名空间.类型名
                object ect = Assembly.Load(assemblyName).CreateInstance(fullName, false, BindingFlags.Default, null, null, null, null);//加载程序集，创建程序集里面的 命名空间.类型名 实例
                if (ect == null)
                    throw new Exception("初始失败，未找到传入模块，请检查！");

                return (T)ect;//类型转换并返回
            }
            catch (Exception ex) { Console.WriteLine(ex.Message + "===" + ex.StackTrace); return default(T); }
        }
    }
}
