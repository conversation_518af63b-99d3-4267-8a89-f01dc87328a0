# 业务流程分析总结：在线预订与特权支付

## 会员混合支付与扣款流程

本节详细解析当会员使用多种支付方式（如会员卡余额 + 现金）进行结账时的后端处理逻辑。

### 业务场景

一个典型的会员混合支付场景如下：

> 会员张三在K01包房消费后，来到前台结账。他的账单总额为500元。他告诉收银员，使用会员卡里的钱支付300元，剩余的200元使用现金支付。收银员在POS机上完成相应操作，会员输入密码确认。

此操作会触发对核心业务驱动 `MemBerDeductMoneyDrive` 的调用。

### 核心处理流程 (`MemBerDeductMoneyDrive`)

所有会员结账逻辑的核心都位于 `Sass.Pos.Drive.MIMS.MemberManage.MemBerDeductMoneyDrive.cs` 的 `DeductMoney` 方法中。该方法负责协调处理整个复杂的混合支付流程。

其执行逻辑分为三个主要部分：

1.  **现金与预付款处理**：
    *   首先，方法会处理本次支付中包含的现金部分 (`context.CashFee`) 以及任何历史预付款 (`context.PreRecord`)。
    *   **关键逻辑**：它会调用 `Bill_AddWxPayInfo` 方法，将现金支付的部分也记录到 `wxPayInfo` 表中。此时，`PayType` 被硬编码为 `5`（代表现金支付），`PayName` 为 `0`。这表明 `wxPayInfo` 表被用作一个统一的支付流水记录中心，而不仅仅是微信支付。

2.  **会员账户扣款**：
    *   **安全校验链**：在扣款前，系统会执行一系列严格的校验，包括会员卡状态（是否冻结/挂失）、账户余额、风控规则等，确保交易的安全性。
    *   **执行扣款**：校验通过后，调用 `app.MIMS.MemberInfo.MemBerDeductMoney(acc)`，进入仓储层执行数据库操作。
    *   **线程安全**：使用 `lock` 关键字确保扣款操作的原子性，防止在多线程高并发场景下出现数据不一致的问题。

3.  **记录消费与发送通知**：
    *   扣款成功后，会调用 `infoDrive.AddDbMemberChechOutData` 将本次消费的详细信息（如各账户支付金额、剩余待付金额等）记录到数据库。
    *   最后，系统会调用短信接口，向会员发送一条详细的扣款及余额变动通知。

### 数据库操作 (`MemberInfoRepository`)

会员账户的实际扣款操作位于 `Sass.Pos.Repository.Lib.MIMS.MemberInfoRepository.cs` 的 `MemBerDeductMoney` 方法中。其核心机制是**动态构建并执行一个SQL事务脚本**。

*   **原子性保证**：整个SQL脚本被 `BEGIN TRANSACTION` 和 `COMMIT/ROLLBACK TRANSACTION` 包裹，确保所有数据库写入操作要么全部成功，要么全部失败回滚，保证了数据的绝对一致性。
*   **操作的数据表**：
    *   `MemBerSccountRecord`: 写入一条总的扣款记录，作为本次交易的“头信息”。
    *   `RechargeInfo` / `ReturnInfo`: 根据扣款类型（充值账户或返利账户），写入详细的消费流水。消费金额会以**负数**形式记录。
*   **余额更新机制**：
    *   代码中并未直接 `UPDATE MemberInfo` 表来更新会员的总余额。
    *   推断这是通过**数据库触发器 (Trigger)** 实现的。当 `RechargeInfo` 或 `ReturnInfo` 表中插入新的流水记录时，数据库内的触发器会自动计算并更新 `MemberInfo` 主表中对应的余额字段。这是一种常见且可靠的设计模式。

### 数据流图

```mermaid
flowchart TD
    A[客户端发起结账请求<br/>(含会员支付)] --> B{MemBerDeductMoneyDrive.DeductMoney};
    B --> C{处理现金/预付款};
    C -- 金额 > 0 --> D[调用 Bill_AddWxPayInfo<br/>(PayType=5, 写入wxPayInfo)];
    B --> E{处理会员账户支付};
    E -- 金额 > 0 --> F[执行安全与风控校验];
    F -- 校验通过 --> G[调用 app.MIMS.MemberInfo.MemBerDeductMoney];
    G --> H{MemberInfoRepository.MemBerDeductMoney};
    H --> I[
        执行SQL事务:
        1. INSERT INTO MemBerSccountRecord
        2. INSERT INTO RechargeInfo/ReturnInfo
    ];
    I -- 数据库触发器 --> J[UPDATE MemberInfo 表余额];
    B -- 成功后 --> K[记录会员消费信息<br/>(AddDbMemberChechOutData)];
    B -- 成功后 --> L[发送短信通知];
```

本文档旨在分析 `Sass.Pos` 项目中与 `wxPayInfo` 表相关的“在线预订”和“特权支付”业务流程。

## 核心发现

`wxPayInfo` 表在系统中扮演着**统一支付记录中心**的角色。它不仅用于记录微信支付，还通过 `PayType` 字段来区分和记录包括现金支付在内的多种支付方式。这种设计将不同的支付渠道整合到了一个统一的数据模型中。

## 业务流程梳理

### 1. 在线预订与支付流程

在线预订的支付环节最终由结账流程触发，其核心逻辑如下：

**流程图**
```mermaid
graph TD
    A[客户端发起结账请求] --> B{Sass.Pos.Drive.MIMS.MemberManage.MemBerDeductMoneyDrive.DeductMoney};
    B --> C{包含现金支付？};
    C -- 是 --> D[调用 WCF 服务: PosService.Bill_AddWxPayInfo];
    C -- 否 --> G[处理其他支付逻辑];
    D --> E{Sass.Pos.Repository.Lib.DbFood.wxPayInfoRepository.AddwxPayInfo};
    E --> F[执行存储过程 AddWxPayInfo];
    F --> H[wxPayInfo 表插入记录];
```

**详细步骤**：

1.  **入口点**：所有支付和结账请求最终由 `Sass.Pos.Drive.MIMS.MemberManage.MemBerDeductMoneyDrive` 类中的 `DeductMoney` 方法统一处理。
2.  **支付方式判断**：`DeductMoney` 方法首先判断支付方式。如果包含现金支付（或需要记录到 `wxPayInfo` 的其他类型），则会启动支付信息记录流程。
3.  **服务调用**：通过 WCF 调用 `Sass.Pos.Service.Pos.DbOrderService` 中的 `Bill_AddWxPayInfo` 方法，并传递支付上下文信息（如门店ID、房间号、金额、支付类型等）。
4.  **业务驱动**：`DbOrderService` 将请求转发给 `Sass.Pos.Drive.DbFood.OrderServiceDrive`。
5.  **应用层处理**：`OrderServiceDrive` 进一步调用 `Sass.Pos.Application.Lib.DbFood.wxPayInfoApp`。
6.  **仓储层执行**：`wxPayInfoApp` 调用 `Sass.Pos.Repository.Lib.DbFood.wxPayInfoRepository` 中的 `AddwxPayInfo` 方法。
7.  **数据持久化**：仓储层构建 SQL 参数，并执行名为 `AddWxPayInfo` 的数据库存储过程，将支付信息（包括交易号、订单号、金额、支付类型等）插入到 `wxPayInfo` 表中。

### 2. 特权支付（会员支付）流程

特权支付主要指使用会员账户余额进行支付，该流程同样在 `DeductMoney` 方法中处理，并与普通支付流程紧密结合。

**流程图**
```mermaid
graph TD
    A[客户端发起结账请求<br/>(含会员支付)] --> B{MemBerDeductMoneyDrive.DeductMoney};
    B --> C{处理混合支付中的现金部分};
    C --> D[按上述流程记录到 wxPayInfo 表];
    B --> E{处理会员账户支付部分};
    E --> F[校验会员状态、余额、风控];
    F -- 校验通过 --> G[执行会员账户扣款<br/>(app.MIMS.MemberInfo.MemBerDeductMoney)];
    G --> H[记录会员消费信息];
    H --> I[发送扣款短信通知];
    I --> J[完成结账];
```

**详细步骤**：

1.  **统一入口**：会员支付请求同样进入 `DeductMoney` 方法。
2.  **混合支付处理**：如果支付是“会员余额 + 现金”的组合，系统会先处理现金部分，并按照上述流程将其记录到 `wxPayInfo` 表。
3.  **会员校验**：系统对会员进行一系列严格的校验，包括：
    *   会员卡状态（是否冻结、挂失）。
    *   账户余额是否充足。
    *   风控规则检查。
4.  **执行扣款**：校验通过后，调用 `app.MIMS.MemberInfo.MemBerDeductMoney` 方法，在会员数据库（MIMS）中直接扣除相应金额（包括充值账户和返利账户）。
5.  **记录与通知**：
    *   在 `DbFood` 数据库中记录会员的详细消费信息。
    *   通过短信平台向会员发送扣款成功的通知。
6.  **完成**：整个结账流程结束。

## 涉及的关键文件和方法

-   **核心业务驱动**:
    -   `Sass.Pos.Drive.MIMS.MemberManage.MemBerDeductMoneyDrive`: `DeductMoney` 方法是所有支付逻辑的 центральный узел (central hub)。
-   **支付信息记录**:
    -   `Sass.Pos.Service.Pos.DbOrderService`: `Bill_AddWxPayInfo` 方法。
    -   `Sass.Pos.Repository.Lib.DbFood.wxPayInfoRepository`: `AddwxPayInfo` 方法，执行数据库操作。
-   **数据库交互**:
    -   `AddWxPayInfo` 存储过程：最终将数据写入 `wxPayInfo` 表。

## 总结

该系统的支付架构设计体现了良好的分层和业务隔离思想。通过将 `wxPayInfo` 表作为统一的支付记录表，简化了支付数据的管理。同时，特权支付（会员支付）流程被封装在独立的会员管理模块中，与主支付流程解耦，但又能在统一的入口点 (`DeductMoney`) 进行协调，保证了业务的灵活性和可扩展性。
