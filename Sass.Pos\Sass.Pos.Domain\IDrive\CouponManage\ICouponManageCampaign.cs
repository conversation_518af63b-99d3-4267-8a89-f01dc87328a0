﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.CouponManage
{
    /// <summary>
    /// 优惠卷活动
    /// </summary>
    public interface ICouponManageCampaign
    {
        /// <summary>
        /// 查询优惠卷活动信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<CamCouponDataModel> GetCamCouponData(CamCouponDataContext context);

        /// <summary>
        /// 根据活动规则派发优惠卷
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<CamCouponDisByRuleModel> CamCouponDisByRule(CamCouponDataExContext context);

        /// <summary>
        /// 根据活动规则派发优惠卷Ex
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<CamCouponDisByRuleModel> CamCouponDisByRuleEx(CamCouponDataExContext context);
    }
}
