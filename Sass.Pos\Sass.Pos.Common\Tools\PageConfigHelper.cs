﻿using MiddlewareLibrary.NoSql;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common
{
    /// <summary>
    /// 获取redis页面配置帮助类
    /// </summary>
    public static class PageConfigHelper
    {
        /// <summary>
        /// 中间件初始化
        /// </summary>
        static MiddlewareProxy.MiddlewareProxy Middleware = new MiddlewareProxy.MiddlewareProxy();

        /// <summary>
        /// 获取当前配置
        /// </summary>
        /// <returns></returns>
        public static PageConfigModel GetPageConfig()
        {
            PageConfigModel pageConfig = null;
            //获取调用当前方法的上一级方法名称
            StackTrace stack = new StackTrace();
            var stackInfo = stack.GetFrame(1);
            var name = stackInfo.GetMethod().Name;

            var configKey = GlobalConfig.Global.RedisKey.PageConfig + name;
            if (Middleware.DB_KV.KeyExists(configKey))
                pageConfig = Middleware.DB_KV.GetVal<PageConfigModel>(configKey);

            return pageConfig;
        }

        public static PageConfigModel GetPageConfig(string methodName)
        {
            PageConfigModel pageConfig = null;
            var configKey = GlobalConfig.Global.RedisKey.PageConfig + methodName;
            if (Middleware.DB_KV.KeyExists(configKey))
                pageConfig = Middleware.DB_KV.GetVal<PageConfigModel>(configKey);

            return pageConfig;
        }
    }

    public class PageConfigModel
    {
        /// <summary>
        /// 当前活动
        /// </summary>
        public string NowActivity { get; set; }
    }
}
