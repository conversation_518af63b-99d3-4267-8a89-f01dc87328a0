using Saas.Pos.Domain.IRepository.DbFood;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.Enum;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace Saas.Pos.Repository.Lib.DbFood
{
    public partial class FoodRepository : BaseRepository<Food>, IFoodRepository
    {
        public List<GetStoreFtTypeReportModel> GetDetailReport(GetStoreFtTypeReportContext context)
        {
            var sql = @"SELECT a.InvNo,a.FdNo,a.FdCName,a.FdPrice,a.FdQty,a.CashTime,a.CashType,a.CashUserName,b.* FROM [dbo].[FdCashBak] as a
                        JOIN FoodLabel as b on a.FdNo = b.FdNo JOIN Food as c ON a.FdNo = c.FdNo JOIN FdType as d on c.FtNo = d.FtNo JOIN FdInv AS e ON a.InvNo = e.InvNo WHERE d.FtNo = @FtNo AND e.WorkDate >= @StartTime AND e.WorkDate <= @EndTime";

            var param = new List<SqlParameter>()
            {
                new SqlParameter("@FtNo",context.FtType),
                new SqlParameter("@StartTime",context.StartTime.ToString("yyyyMMdd")),
                new SqlParameter("@EndTime",context.EndTime.ToString("yyyyMMdd")),
            };

            var list = dbcontext.Database.SqlQuery<GetStoreFtTypeReportModel>(sql, param.ToArray()).ToList();

            return list;
        }

        public List<HeadCountModel> GetHeadCountReport(GetStoreReportContext context)
        {
            string sql = @"SELECT lable.Category1,lable.Category2,(lable.PeoNumber * item.FdQty) as PeoNumber,lable.Type
                            FROM FdCashBak AS item
                            JOIN FdInv AS fd ON item.InvNo = fd.InvNo
                            JOIN Food AS food ON item.FdNo = food.FdNo
                            JOIN FdType AS fdtype ON food.FtNo = fdtype.FtNo
                            LEFT JOIN FoodLabel AS lable ON item.FdNo = lable.FdNo
                            WHERE lable.PeoNumber > 0 AND item.CashType = 'N'
                            AND fd.WorkDate >= @StartTime AND fd.WorkDate <= @EndTime";

            var param = new List<SqlParameter>()
            {
                new SqlParameter("@StartTime",context.StartTime.ToString("yyyyMMdd")),
                new SqlParameter("@EndTime",context.EndTime.ToString("yyyyMMdd")),
            };

            var data = dbcontext.Database.SqlQuery<HeadCountModel>(sql, param.ToArray()).ToList();
            return data;
        }

        /// <summary>
        /// 成本核算
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<StoreReportModel> GetStoreReport(GetStoreReportContext context)
        {
            string sql = @"SELECT
	                        a.invno AS InvNo,
	                        a.fdno AS FdNo,
	                        a.fdcname AS FdCName,
	                        fdprice AS FdPrice,
	                        a.fdqty AS FdQty,
	                        a.cashtype AS CashType,
	                        a.cashtime AS CashTime,
	                        CASE WHEN a.CashUserName IS NULL THEN '' ELSE a.CashUserName END CashUserName,
	                        b.workdate AS WorkDate,
                            CASE WHEN ( ( SELECT MAX ( ShareFdNo ) FROM sharesetinfo WHERE ShareFdNo = a.FdNo ) IS NULL ) THEN 0 ELSE 1 
	                        END AS IsPackage,
	                        d.FtNo AS FtNo,
	                        d.FtCName AS FtCName,
	                        d.PrnType AS PrnType,
                            e.Category2
                            FROM fdcashbak AS a
	                        JOIN fdinv AS b ON a.invno= b.invno
	                        JOIN Food AS c ON a.FdNo = c.FdNo
	                        JOIN FdType AS d ON c.FtNo = d.FtNo
                            JOIN FoodLabel AS e ON a.FdNo = e.FdNo
                            WHERE workdate >=@StartTime 
	                        AND WorkDate <= @EndTime
                            AND a.CashType != 'X'";

            var param = new List<SqlParameter>()
            {
                new SqlParameter("@StartTime",context.StartTime.ToString("yyyyMMdd")),
                new SqlParameter("@EndTime",context.EndTime.ToString("yyyyMMdd")),
            };

            //查出所有数据
            var data = dbcontext.Database.SqlQuery<StoreReportModel>(sql, param.ToArray()).ToList();
            return data;
        }

        public List<WaitCreateModel> GetWaitCreate()
        {
            var data = (from food in db.Food
                        join label in db.FoodLabel on food.FdNo equals label.FdNo into intoData
                        from da in intoData.DefaultIfEmpty()
                        where da == null
                        select new { food.FdNo, food.FdCName }).ToList();

            var result = data.Select(x => new WaitCreateModel()
            {
                FdNo = x.FdNo,
                FdCName = x.FdCName,
            }).ToList();

            return result;
        }

        public GetFoodModel GetFood(string fdNo)
        {
            var sql = "Select FdNo,FdCName from Food where FdNo = @FdNo";

            var param = new List<SqlParameter>()
            {
                new SqlParameter("@FdNo",fdNo),
            };

            return dbcontext.Database.SqlQuery<GetFoodModel>(sql, param.ToArray()).FirstOrDefault();
        }

        /// <summary>
        /// 查询天王商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetFoodDataModel> GetFoodData(GetFoodDataContext context)
        {
            var list = (from f in db.Food
                        join ft in db.FdType
                        on f.FtNo equals ft.FtNo
                        select new GetFoodDataModel()
                        {
                            FtNo = ft.FtNo,
                            ChangePrice1 = f.ChangePrice1,
                            FdCName = f.FdCName,
                            FdLack = f.FdLack,
                            FdNo = f.FdNo,
                            FdPrice1 = f.FdPrice1,
                            FdSPrice1 = f.FdSPrice1,
                            FtCName = ft.FtCName,
                            InRmCost1 = f.InRmCost1,
                            MiniDisc1 = f.MiniDisc1,
                        });
            if (!string.IsNullOrEmpty(context.FdCName))
                list = list.Where(i => i.FdCName.Contains(context.FdCName));
            if (!string.IsNullOrEmpty(context.FtNo))
                list = list.Where(i => i.FtNo == context.FtNo);
            if (context.FdLack != null)
                list = list.Where(i => i.FdLack == context.FdLack);

            context.Pagination.records = list.Count();
            var data = list.ToList().OrderByDescending(i => i.FdNo)
                           .Skip(context.Pagination.rows * (context.Pagination.page - 1))
                           .Take(context.Pagination.rows).ToList();

            return data;
        }

        /// <summary>
        /// 查询天王商品分类信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetFdTypeDataModel> GetFdTypeData(GetFdTypeDataContext context)
        {
            var list = (from ft in db.FdType
                        join pr in db.FPrn
                        on ft.PrnType equals pr.PrnType into fp
                        from prr in fp.DefaultIfEmpty()
                        select new GetFdTypeDataModel()
                        {
                            PrnType = prr.PrnType,
                            FtCName = ft.FtCName,
                            FtNo = ft.FtNo,
                            PrnName = prr.PrnName
                        });
            if (!string.IsNullOrEmpty(context.FtCName))
                list = list.Where(i => i.FtCName.Contains(context.FtCName));
            if (!string.IsNullOrEmpty(context.PrnType))
                list = list.Where(i => i.PrnType == context.PrnType);

            context.Pagination.records = list.Count();
            var data = list.ToList().OrderByDescending(i => i.FtNo)
                           .Skip(context.Pagination.rows * (context.Pagination.page - 1))
                           .Take(context.Pagination.rows).ToList();

            return data;
        }

        public List<GetUserScenceFdNoModel> GetUserSceFdNoData(GetSceneRoleConfigModel context)
        {
            //做分页
            var month = DateTime.Now.Month.ToString();
            var day = DateTime.Now.Day.ToString();

            var query = (from food in db.Food
                         join ft in db.FdType on food.FtNo equals ft.FtNo
                         join date in db.SDate on new { SMonth = month, SDay = day }
                         equals new { date.SMonth, date.SDay } into sDate
                         from sdateDefault in sDate.DefaultIfEmpty()
                         select new GetUserScenceFdNoExModel
                         {
                             FdNo = food.FdNo,
                             FdPrice = sdateDefault == null ? food.FdPrice1 : food.FdSPrice1,
                             //FdCName = food.FdCName,
                             FtNo = food.FtNo,
                             //FtCName = ft.FtCName,
                         });

            //额外附加数据
            IQueryable<GetUserScenceFdNoExModel> additionalQuery = null;
            if (context != null)
            {
                if (context.ConfigExtend != null && context.ConfigExtend.Count > 0)
                {
                    foreach (var item in context.ConfigExtend.OrderBy(w => w.ConfigType))
                    {
                        var fdNos = item.FdNos.Split(',');
                        //为了防止附加数据被后续的查询影响，提前查出来作为单独的数据
                        if (item.ConfigType == (int)SceneRoleConfigTypeEnum.Add)
                        {
                            if (additionalQuery == null)
                                additionalQuery = query.Where(w => fdNos.Contains(w.FdNo));
                            else
                                additionalQuery.Union(query.Where(w => fdNos.Contains(w.FdNo)));
                        }
                        else
                            query = query.Where(w => !fdNos.Contains(w.FdNo));
                    }
                }

                if (context.MinPrice.HasValue)
                    query = query.Where(w => w.FdPrice >= context.MinPrice.Value);
                if (context.MaxPrice.HasValue)
                    query = query.Where(w => w.FdPrice <= context.MaxPrice.Value);
                if (!string.IsNullOrEmpty(context.Category))
                {
                    var categorys = context.Category.Split(',');
                    query = query.Where(w => categorys.Contains(w.FtNo));
                }
            }

            //附加数据添加进入查询数据里面
            if (additionalQuery != null)
                query = query.Union(additionalQuery);

            var list = query.OrderBy(w => w.FdPrice).Select(w => new GetUserScenceFdNoModel()
            {
                //FdCName = w.FdCName,
                FdNo = w.FdNo,
                //FdPrice = w.FdPrice,
                //FtCName = w.FtCName
            }).ToList();

            return list;
        }
    }
}
