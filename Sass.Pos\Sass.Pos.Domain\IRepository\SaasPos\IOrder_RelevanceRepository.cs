﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.SaasPos
{
    public partial interface IOrder_RelevanceRepository : IRepositoryBase<Order_Relevance>
    {
        List<GetOrderPaymentInfoModel> GetPayInfo(List<int> orderId);
    }
}
