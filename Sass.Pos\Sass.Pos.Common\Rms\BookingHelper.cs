﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Rms
{
    public class BookingHelper
    {
        public static DateTime ConvertTimeDate(DateTime date, string hour)
        {
            if (hour.Contains(":"))
                hour = hour.Replace(":", "");

            var dateStr = date.ToString("yyyyMMdd");
            if (hour.Length < 4)
            {
                if (hour.Length == 1)
                {
                    date = date.AddDays(1);
                    hour = "000" + hour;
                }
                else
                    hour = "0" + hour;
            }


            return DateTime.ParseExact(dateStr + hour, "yyyyMMddHHmm", null);
        }

        /// <summary>
        /// 获取指定时间段的时间间隔
        /// </summary>
        /// <returns></returns>
        public static List<string> GetIntervalTime(DateTime date, int begHour, int endHour,string startTime,string enddate)
        {
            int count = 8;
            var nowDate = DateTime.Now;
            var timeLines = new List<string>();
            var startDate = ConvertTimeDate(date, begHour.ToString());
            var endDate = ConvertTimeDate(date, endHour.ToString());
            if (startDate.Date > nowDate.Date || (startDate.Date == nowDate.Date && startDate > nowDate))
            {
                count = 7;
                timeLines.Add(startDate.ToString("HH:mm"));
            }

            //需要一个随之增加的时间
            var currentDate = startDate;
            //模除三十不等于0说明不是整点
            if (startDate.Minute % 30 != 0)
            {
                currentDate = startDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);
                if (currentDate.Minute > GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval)
                {
                    currentDate = currentDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);
                    if (currentDate.Minute - GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval <= 10)
                        currentDate = DateTime.Parse(currentDate.ToString("yyyy-MM-dd HH:00:00"));
                    else
                        currentDate = DateTime.Parse(currentDate.ToString("yyyy-MM-dd HH:30:00"));
                }
                else
                {
                    currentDate = currentDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);
                    if (currentDate.Minute - GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval <= 10)
                        currentDate = DateTime.Parse(currentDate.ToString("yyyy-MM-dd HH:00:00"));
                    else
                        currentDate = DateTime.Parse(currentDate.ToString("yyyy-MM-dd HH:30:00"));
                }
            }
            else
                currentDate = currentDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);

            for (int i = 0; i < count; i++)
            {
                //判断当前时间是否超过，超过就不返回了
                if (currentDate.Date <= nowDate.Date && currentDate < nowDate)
                {
                    currentDate = currentDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);
                    continue;
                }

                //正常情况，没有跨天
                if (begHour < endHour)
                {
                    if (currentDate >= endDate)
                        break;

                    timeLines.Add(currentDate.ToString("HH:mm"));
                }
                else
                    //跨天，非正常情况，只循环7次
                    timeLines.Add(currentDate.ToString("HH:mm"));

                currentDate = currentDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);
            }

            return timeLines;
        }

        /// <summary>
        /// 获取选中时间日期格式
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public static string GetDateStr(DateTime date)
        {
            return date.ToString("MM-dd");
        }

        /// <summary>
        /// 获取选中时间小时格式
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public static string GetHourStr(DateTime date)
        {
            return date.ToString("HH:mm");
        }

        public static bool CheckRefund(string comeDate, string hour)
        {
            var comeDateStr = comeDate + hour.Replace(":", "");
            var date = DateTime.ParseExact(comeDateStr, "yyyyMMddHHmm", null);

            //预计到达时间三十分钟以外的才可以退款，三十分钟以内的不给退款
            if (date <= DateTime.Now.AddMinutes(30))
                return false;

            return true;
        }
    }
}
