﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;

namespace Saas.Pos.Common.Tools
{
    /// <summary>
    /// I/O文件操作扩展帮助类
    /// </summary>
    public static class FileHelper
    {
        /// <summary>
        /// 当文件夹不存在时，创建文件夹
        /// </summary>
        /// <param name="folderPath">文件夹地址</param>
        public static void CreateFolder(this string folderPath)
        {
            if (!Directory.Exists(folderPath))
                Directory.CreateDirectory(folderPath);
        }

        /// <summary>
        /// 当文件不存在时，创建文件
        /// </summary>
        /// <param name="filePath"></param>
        public static void CreateFile(this string filePath)
        {
            if (!filePath.FileExists())
                File.Create(filePath).Dispose();
        }

        /// <summary>
        /// 判断文件地址是否存在
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static bool FileExists(this string filePath)
        {
            return File.Exists(filePath);
        }

        /// <summary>
        /// 将文件移动到新的位置
        /// </summary>
        /// <param name="sourcePath">源文件路径</param>
        /// <param name="newFilePath">新文件路径</param>
        /// <returns></returns>
        public static void MoveFile(this string sourcePath, string newFilePath)
        {
            File.Move(sourcePath, newFilePath);
        }
    }
}
