﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace external.open.library.TakeaWayPlatform.MeiTuan.Request
{
    public class ReverseConsumeRequest : MeituanRequestBase
    {
        /// <summary>
        /// 套餐id，注意：对应deal_id，非dealgroup_id
        /// </summary>
        public string app_deal_id { get; set; }
        /// <summary>
        /// 团购券码
        /// </summary>
        public string receipt_code { get; set; }
        /// <summary>
        /// 美团点评店铺id，必须是团购的适用门店
        /// </summary>
        public string open_shop_uuid { get; set; }
        /// <summary>
        /// 商家在第三方系统登录的帐号
        /// </summary>
        public string app_shop_account { get; set; }
        /// <summary>
        /// 商家在第三方系统登录的用户名
        /// </summary>
        public string app_shop_accountname { get; set; }
    }
}
