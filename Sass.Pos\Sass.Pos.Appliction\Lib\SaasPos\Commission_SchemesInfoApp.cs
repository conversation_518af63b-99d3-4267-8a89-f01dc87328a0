﻿using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Application.Lib.SaasPos
{
    public partial class Commission_SchemesInfoApp : AppBase<Commission_SchemesInfo>
    {
        public Commission_SchemesInfo GetBestSchemes(GetBestSchemesContext context)
        {
            var result = Repository.Commission_SchemesInfo.GetSchemesList(context);

            //根据到期时间做一个排序返回最近时间的一个方案
            return result.OrderBy(w => w.EndTime).FirstOrDefault();
        }

        public List<GetCommissionSchemesModel> GetSchemesList(GetSchemesListContext context)
        {
            return Repository.Commission_SchemesInfo.GetSchemesPageList(context);
        }

        public List<GetPermitSchemesConfigsModel> GetPermitSchemesConfigs(GetPermitSchemesConfigsContext context)
        {
            return Repository.Commission_SchemesInfo.GetPermitSchemesConfigs(context);
        }

        /// <summary>
        /// 查询提成商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetPermitItemModel> GetPermitItem(GetPermitItemContext context)
        {
            return Repository.Commission_SchemesInfo.GetPermitItem(context);
        }

        /// <summary>
        /// 获取所有在线预订商品信息
        /// </summary>
        /// <returns></returns>
        public List<GetPermitDataModel> GetPermitData(GetPermitDataContext context)
        {
            return Repository.Commission_SchemesInfo.GetPermitData(context);
        }
    }
}
