﻿<?xml version="1.0" encoding="utf-8" ?>
<connectionStrings>
  <!--<add name="MIMSEntities" connectionString="metadata=res://*/MIMS.MIMS_DB.csdl|res://*/MIMS.MIMS_DB.ssdl|res://*/MIMS.MIMS_DB.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=************;initial catalog=MIMS;user id=sa;password=***;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
  <add name="MIMSEntities" connectionString="metadata=res://*/MIMS.MIMS_DB.csdl|res://*/MIMS.MIMS_DB.ssdl|res://*/MIMS.MIMS_DB.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=***********;initial catalog=MIMS;user id=sa;password=***********;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
	<add name="rms2019Entities" connectionString="metadata=res://*/Rms.RMS_DB.csdl|res://*/Rms.RMS_DB.ssdl|res://*/Rms.RMS_DB.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=yy.tang-hui.com.cn;initial catalog=rms2019;user id=sa;password=Musicbox@***;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  <!--<add name="rms2019Entities" connectionString="metadata=res://*/Rms.RMS_DB.csdl|res://*/Rms.RMS_DB.ssdl|res://*/Rms.RMS_DB.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=************;initial catalog=Rms2023;user id=sa;password=***;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
  <add name="Saas_PosEntities" connectionString="metadata=res://*/SaasPos.SaasPos_DB.csdl|res://*/SaasPos.SaasPos_DB.ssdl|res://*/SaasPos.SaasPos_DB.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=************;initial catalog=saas.pos;user id=sa;password=***;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
	<add name="dbfoodEntities" connectionString="metadata=res://*/DbFood.DbFood_DB.csdl|res://*/DbFood.DbFood_DB.ssdl|res://*/DbFood.DbFood_DB.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=************;initial catalog=dbfood;user id=sa;password=***;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
	<add name="GrouponBase_20200516Entities" connectionString="metadata=res://*/GrouponBase.GrouponBase_DB.csdl|res://*/GrouponBase.GrouponBase_DB.ssdl|res://*/GrouponBase.GrouponBase_DB.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=***********;initial catalog=GrouponBase;user id=sa;password=***********;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
</connectionStrings>