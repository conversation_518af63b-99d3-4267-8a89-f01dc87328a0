﻿using Saas.Pos.Model.Rms;
using Saas.Pos.Model.Rms.Context;
using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Appliction.Lib.Rms
{
    public partial class printrecordApp : AppBase<printrecord>
    {
        public List<GetFoodHeaderDataModel> GetFoodHeaderData(GetFoodHeaderDataContext context)
        {
            return Repository.printrecord.GetFoodHeaderData(context);
        }
    }
}
