<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <EnableMSTestV2CopyResources Condition="$(EnableMSTestV2CopyResources) == ''">true</EnableMSTestV2CopyResources>
  </PropertyGroup>

  <Target Name="GetMSTestV2CultureHierarchy">
    <!-- Only traversing 5 levels in the culture hierarchy. This is the maximum lenght for all cultures and should be sufficient to get to a culture name that maps to a resource folder we package. 
    The root culture name for all cultures is invariant whose name is ''(empty) and the parent for invariant culture is invariant itself.(https://msdn.microsoft.com/en-us/library/system.globalization.cultureinfo.parent(v=vs.110).aspx.) 
    So the below code should not break build in any case. -->
    <ItemGroup>
      <CurrentUICultureHierarchy Include="$([System.Globalization.CultureInfo]::CurrentUICulture.Name)" />
      <CurrentUICultureHierarchy Include="$([System.Globalization.CultureInfo]::CurrentUICulture.Parent.Name)"/>
      <CurrentUICultureHierarchy Include="$([System.Globalization.CultureInfo]::CurrentUICulture.Parent.Parent.Name)"  Condition="$([System.Globalization.CultureInfo]::CurrentUICulture.Parent.Parent.Name) != ''"/>
      <CurrentUICultureHierarchy Include="$([System.Globalization.CultureInfo]::CurrentUICulture.Parent.Parent.Parent.Name)"  Condition="$([System.Globalization.CultureInfo]::CurrentUICulture.Parent.Parent.Parent.Name) != ''"/>
      <CurrentUICultureHierarchy Include="$([System.Globalization.CultureInfo]::CurrentUICulture.Parent.Parent.Parent.Parent.Name)"  Condition="$([System.Globalization.CultureInfo]::CurrentUICulture.Parent.Parent.Parent.Parent.Name) != ''"/>
    </ItemGroup>
  </Target> 

  <!-- Copy resources over to $(TargetDir) if this is a localized build. -->
  <Target Name="CopyMSTestV2Resources" BeforeTargets="PrepareForBuild" Condition="$(EnableMSTestV2CopyResources) == 'true'" DependsOnTargets="GetMSTestV2CultureHierarchy">
  
    <PropertyGroup>
      <CurrentUICultureHierarchy>%(CurrentUICultureHierarchy.Identity)</CurrentUICultureHierarchy>
    </PropertyGroup>
  
    <ItemGroup>
      <MSTestV2Files Include="$(MSBuildThisFileDirectory)..\_common\*.dll" />
    </ItemGroup>
	
	<ItemGroup>
      <Content Include="@(MSTestV2Files->'%(RootDir)%(Directory)$(CurrentUICultureHierarchy)\%(FileName).resources.dll')" 
               Condition="Exists('%(RootDir)%(Directory)$(CurrentUICultureHierarchy)\%(FileName).resources.dll')">
         <Link>$(CurrentUICultureHierarchy)\%(FileName).resources.dll</Link>
         <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
         <BaseAssemblyFullPath>%(FullPath)</BaseAssemblyFullPath>
         <Visible>False</Visible>
      </Content>
    </ItemGroup>
  </Target>

</Project>