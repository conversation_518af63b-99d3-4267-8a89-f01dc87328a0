﻿
using ComponentApplicationServiceInterface.Web;
using Saas.Pos.Model.GrouponBase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.GrouponBase
{
 public partial class ActivityInfoApp : AppBase<ActivityInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ActivityInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.ActivityInfo;
        }
   
        
 
 }
  

 public partial class CacheTableApp : AppBase<CacheTable> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<CacheTable> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.CacheTable;
        }
   
        
 
 }
  

 public partial class CanChangeGrouponApp : AppBase<CanChangeGroupon> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<CanChangeGroupon> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.CanChangeGroupon;
        }
   
        
 
 }
  

 public partial class CodeInfoChangeLogApp : AppBase<CodeInfoChangeLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<CodeInfoChangeLog> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.CodeInfoChangeLog;
        }
   
        
 
 }
  

 public partial class CouponUseInfoApp : AppBase<CouponUseInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<CouponUseInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.CouponUseInfo;
        }
   
        
 
 }
  

 public partial class CouponUsersFreezeApp : AppBase<CouponUsersFreeze> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<CouponUsersFreeze> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.CouponUsersFreeze;
        }
   
        
 
 }
  

 public partial class ElectronCouponApp : AppBase<ElectronCoupon> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ElectronCoupon> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.ElectronCoupon;
        }
   
        
 
 }
  

 public partial class EntityCouponLogApp : AppBase<EntityCouponLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<EntityCouponLog> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.EntityCouponLog;
        }
   
        
 
 }
  

 public partial class FdOrderNoApp : AppBase<FdOrderNo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdOrderNo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.FdOrderNo;
        }
   
        
 
 }
  

 public partial class GrouponCodeInfoApp : AppBase<GrouponCodeInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GrouponCodeInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.GrouponCodeInfo;
        }
   
        
 
 }
  

 public partial class GrouponCodeInfobakApp : AppBase<GrouponCodeInfobak> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GrouponCodeInfobak> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.GrouponCodeInfobak;
        }
   
        
 
 }
  

 public partial class GrouponCodeInfobak1App : AppBase<GrouponCodeInfobak1> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GrouponCodeInfobak1> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.GrouponCodeInfobak1;
        }
   
        
 
 }
  

 public partial class GrouponCommentInfoApp : AppBase<GrouponCommentInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GrouponCommentInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.GrouponCommentInfo;
        }
   
        
 
 }
  

 public partial class GrouponConsumeInfoApp : AppBase<GrouponConsumeInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GrouponConsumeInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.GrouponConsumeInfo;
        }
   
        
 
 }
  

 public partial class GrouponGiveCodeInfoApp : AppBase<GrouponGiveCodeInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GrouponGiveCodeInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.GrouponGiveCodeInfo;
        }
   
        
 
 }
  

 public partial class GrouponGiveInfoApp : AppBase<GrouponGiveInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GrouponGiveInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.GrouponGiveInfo;
        }
   
        
 
 }
  

 public partial class GrouponGiveMsgAlertApp : AppBase<GrouponGiveMsgAlert> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GrouponGiveMsgAlert> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.GrouponGiveMsgAlert;
        }
   
        
 
 }
  

 public partial class GrouponInfoApp : AppBase<GrouponInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GrouponInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.GrouponInfo;
        }
   
        
 
 }
  

 public partial class GrouponInfoApplyLogApp : AppBase<GrouponInfoApplyLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GrouponInfoApplyLog> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.GrouponInfoApplyLog;
        }
   
        
 
 }
  

 public partial class GrouponInfoLogApp : AppBase<GrouponInfoLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GrouponInfoLog> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.GrouponInfoLog;
        }
   
        
 
 }
  

 public partial class GrouponLockApp : AppBase<GrouponLock> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GrouponLock> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.GrouponLock;
        }
   
        
 
 }
  

 public partial class GrouponLockBakApp : AppBase<GrouponLockBak> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GrouponLockBak> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.GrouponLockBak;
        }
   
        
 
 }
  

 public partial class GrouponTeamApp : AppBase<GrouponTeam> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GrouponTeam> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.GrouponTeam;
        }
   
        
 
 }
  

 public partial class illness_ChangeCodeApp : AppBase<illness_ChangeCode> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<illness_ChangeCode> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.illness_ChangeCode;
        }
   
        
 
 }
  

 public partial class illness_CodedelayApp : AppBase<illness_Codedelay> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<illness_Codedelay> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.illness_Codedelay;
        }
   
        
 
 }
  

 public partial class InputPhoneInfoApp : AppBase<InputPhoneInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<InputPhoneInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.InputPhoneInfo;
        }
   
        
 
 }
  

 public partial class LogApp : AppBase<Log> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Log> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.Log;
        }
   
        
 
 }
  

 public partial class NApplyNoAndCodeApp : AppBase<NApplyNoAndCode> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NApplyNoAndCode> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NApplyNoAndCode;
        }
   
        
 
 }
  

 public partial class NApplyStatusInfoApp : AppBase<NApplyStatusInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NApplyStatusInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NApplyStatusInfo;
        }
   
        
 
 }
  

 public partial class NAreaInfoApp : AppBase<NAreaInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NAreaInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NAreaInfo;
        }
   
        
 
 }
  

 public partial class NBaseCodeInfoApp : AppBase<NBaseCodeInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NBaseCodeInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NBaseCodeInfo;
        }
   
        
 
 }
  

 public partial class NBuildModelApp : AppBase<NBuildModel> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NBuildModel> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NBuildModel;
        }
   
        
 
 }
  

 public partial class NCardLableApp : AppBase<NCardLable> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NCardLable> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NCardLable;
        }
   
        
 
 }
  

 public partial class NCardModelApp : AppBase<NCardModel> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NCardModel> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NCardModel;
        }
   
        
 
 }
  

 public partial class NCardTypeApp : AppBase<NCardType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NCardType> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NCardType;
        }
   
        
 
 }
  

 public partial class NCardUseModelApp : AppBase<NCardUseModel> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NCardUseModel> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NCardUseModel;
        }
   
        
 
 }
  

 public partial class NCodeRefundApp : AppBase<NCodeRefund> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NCodeRefund> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NCodeRefund;
        }
   
        
 
 }
  

 public partial class NCodeStatusApp : AppBase<NCodeStatus> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NCodeStatus> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NCodeStatus;
        }
   
        
 
 }
  

 public partial class NDistributeSetGrouponApp : AppBase<NDistributeSetGroupon> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NDistributeSetGroupon> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NDistributeSetGroupon;
        }
   
        
 
 }
  

 public partial class NForceVerificationApp : AppBase<NForceVerification> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NForceVerification> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NForceVerification;
        }
   
        
 
 }
  

 public partial class NGrouponActivationInfoApp : AppBase<NGrouponActivationInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponActivationInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponActivationInfo;
        }
   
        
 
 }
  

 public partial class NGrouponApplyInfoApp : AppBase<NGrouponApplyInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponApplyInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponApplyInfo;
        }
   
        
 
 }
  

 public partial class NGrouponBillApp : AppBase<NGrouponBill> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponBill> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponBill;
        }
   
        
 
 }
  

 public partial class NGrouponBillStatusApp : AppBase<NGrouponBillStatus> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponBillStatus> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponBillStatus;
        }
   
        
 
 }
  

 public partial class NGrouponCodeConsumeInfoApp : AppBase<NGrouponCodeConsumeInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponCodeConsumeInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponCodeConsumeInfo;
        }
   
        
 
 }
  

 public partial class NGrouponCodeInfoApp : AppBase<NGrouponCodeInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponCodeInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponCodeInfo;
        }
   
        
 
 }
  

 public partial class NGrouponCodeRecoveryInfoApp : AppBase<NGrouponCodeRecoveryInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponCodeRecoveryInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponCodeRecoveryInfo;
        }
   
        
 
 }
  

 public partial class NGrouponConsumeBatchTypeApp : AppBase<NGrouponConsumeBatchType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponConsumeBatchType> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponConsumeBatchType;
        }
   
        
 
 }
  

 public partial class NGrouponGiveCodeInfoApp : AppBase<NGrouponGiveCodeInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponGiveCodeInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponGiveCodeInfo;
        }
   
        
 
 }
  

 public partial class NGrouponInfoApp : AppBase<NGrouponInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponInfo;
        }
   
        
 
 }
  

 public partial class NGrouponProjectApp : AppBase<NGrouponProject> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponProject> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponProject;
        }
   
        
 
 }
  

 public partial class NGrouponSaleStatisticsApp : AppBase<NGrouponSaleStatistics> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponSaleStatistics> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponSaleStatistics;
        }
   
        
 
 }
  

 public partial class NGrouponSelTypeInfoApp : AppBase<NGrouponSelTypeInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponSelTypeInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponSelTypeInfo;
        }
   
        
 
 }
  

 public partial class NGrouponSendInfoApp : AppBase<NGrouponSendInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponSendInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponSendInfo;
        }
   
        
 
 }
  

 public partial class NGrouponTemplateApp : AppBase<NGrouponTemplate> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponTemplate> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponTemplate;
        }
   
        
 
 }
  

 public partial class NGrouponTransactionApp : AppBase<NGrouponTransaction> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponTransaction> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponTransaction;
        }
   
        
 
 }
  

 public partial class NGrouponUseModelApp : AppBase<NGrouponUseModel> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NGrouponUseModel> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NGrouponUseModel;
        }
   
        
 
 }
  

 public partial class NHtmlTemplateApp : AppBase<NHtmlTemplate> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NHtmlTemplate> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NHtmlTemplate;
        }
   
        
 
 }
  

 public partial class NProjectTypeInfoApp : AppBase<NProjectTypeInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NProjectTypeInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NProjectTypeInfo;
        }
   
        
 
 }
  

 public partial class NSaleModelApp : AppBase<NSaleModel> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NSaleModel> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NSaleModel;
        }
   
        
 
 }
  

 public partial class NSendRecordApp : AppBase<NSendRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NSendRecord> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NSendRecord;
        }
   
        
 
 }
  

 public partial class NSetGrouponApp : AppBase<NSetGroupon> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NSetGroupon> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NSetGroupon;
        }
   
        
 
 }
  

 public partial class NSubSetGrouponApp : AppBase<NSubSetGroupon> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NSubSetGroupon> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NSubSetGroupon;
        }
   
        
 
 }
  

 public partial class NValidModelApp : AppBase<NValidModel> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NValidModel> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.NValidModel;
        }
   
        
 
 }
  

 public partial class PhoneChangeOpenidApp : AppBase<PhoneChangeOpenid> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<PhoneChangeOpenid> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.PhoneChangeOpenid;
        }
   
        
 
 }
  

 public partial class ProductOrderInfoApp : AppBase<ProductOrderInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ProductOrderInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.ProductOrderInfo;
        }
   
        
 
 }
  

 public partial class ProductSpecApp : AppBase<ProductSpec> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ProductSpec> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.ProductSpec;
        }
   
        
 
 }
  

 public partial class ReceiveFillInfoApp : AppBase<ReceiveFillInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ReceiveFillInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.ReceiveFillInfo;
        }
   
        
 
 }
  

 public partial class ReceiveRecordApp : AppBase<ReceiveRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ReceiveRecord> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.ReceiveRecord;
        }
   
        
 
 }
  

 public partial class SeasonCode2019_TestApp : AppBase<SeasonCode2019_Test> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<SeasonCode2019_Test> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.SeasonCode2019_Test;
        }
   
        
 
 }
  

 public partial class SendBrithdayCertificate_SearchApp : AppBase<SendBrithdayCertificate_Search> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<SendBrithdayCertificate_Search> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.SendBrithdayCertificate_Search;
        }
   
        
 
 }
  

 public partial class SpecialCardApplyInfoApp : AppBase<SpecialCardApplyInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<SpecialCardApplyInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.SpecialCardApplyInfo;
        }
   
        
 
 }
  

 public partial class SpecialCardDynamicInfoApp : AppBase<SpecialCardDynamicInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<SpecialCardDynamicInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.SpecialCardDynamicInfo;
        }
   
        
 
 }
  

 public partial class SpecialCardStateInfoApp : AppBase<SpecialCardStateInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<SpecialCardStateInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.SpecialCardStateInfo;
        }
   
        
 
 }
  

 public partial class SpecialOperatorStateInfoApp : AppBase<SpecialOperatorStateInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<SpecialOperatorStateInfo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.SpecialOperatorStateInfo;
        }
   
        
 
 }
  

 public partial class TBLOGApp : AppBase<TBLOG> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<TBLOG> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.TBLOG;
        }
   
        
 
 }
  

 public partial class WeChatCouponOrderNoApp : AppBase<WeChatCouponOrderNo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<WeChatCouponOrderNo> SetRepository(RepositoryFactory.T4.GrouponBase.RepositorySession Session)
        {
            return Session.WeChatCouponOrderNo;
        }
   
        
 
 }
  

}
