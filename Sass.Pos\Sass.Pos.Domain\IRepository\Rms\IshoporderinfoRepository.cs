﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.Rms;
using Saas.Pos.Model.Rms.Context;
using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.Rms
{
    public partial interface IshoporderinfoRepository : IRepositoryBase<shoporderinfo>
    {
        GetRoomOrderItemModel GetOrderItem(GetRoomOrderItemContext context);
    }
}
