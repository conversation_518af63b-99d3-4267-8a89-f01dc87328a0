﻿using Saas.Pos.Common.Attributes.ValidationFilter.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Attributes.ValidationFilter
{
    /// <summary>
    /// 字符串必填项校验，写上该标签则该内容必填
    /// </summary>
    public class RequiredAttribute : ValidationFilterAttribute
    {
        public override void Inspect(object currentValue, ValidationContext context)
        {
            if (currentValue == null || string.IsNullOrEmpty(currentValue.ToString().Trim()))
                throw new Exception(string.IsNullOrEmpty(ErrorMessage) ? string.Format(AppInterface.dat.lang["RequiredMsg"], context.PropertyName) : ErrorMessage);
        }
    }
}
