using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Common.Factory;
using Saas.Pos.Domain.IService;
using Saas.Pos.Domain.IService.SaasPos;
using Saas.Pos.Drive.SaasPos.PosFoodManage;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Service.SaasPos
{
    public partial class SaasPosService : IPosFoodManageService
    {
        /// <summary>
        /// 查询商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<RespPaginationModel<GetPosFdDataModel>> GetPosFdData(GetPosFdDataContext context)
        {
            var drive = new PosFoodManageDrive();
            return drive.GetPosFdData(context);
        }

        /// <summary>
        /// 新增商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<FdDataRetBool> InsertPosFdData(InsertPosFdDataContext context)
        {
            var drive = new PosFoodManageDrive();
            return drive.InsertPosFdData(context);
        }

        /// <summary>
        /// 查询商品分类下拉信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<List<GetPosFtSelectDataModel>> GetPosFtSelectData(GetPosFtSelectDataContext context)
        {
            var drive = new PosFoodManageDrive();
            return drive.Pft.GetPosFtSelectData(context);
        }

        /// <summary>
        /// 删除商品信息
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public ResponseContext<FdDataRetBool> DeletePosFdData(DeletePosFdDataContext context)
        {
            var drive = new PosFoodManageDrive();
            return drive.DeletePosFdData(context);
        }

        /// <summary>
        /// 编辑商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<FdDataRetBool> EditPosFoodData(EditPosFoodDataContext context)
        {
            var drive = new PosFoodManageDrive();
            return drive.EditPosFoodData(context);
        }

        /// <summary>
        /// 查询分类信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<RespPaginationModel<GetPosFtDataModel>> GetPosFtData(GetPosFtDataContext context)
        {
            var drive = new PosFoodManageDrive();
            return drive.Pft.GetPosFtData(context);
        }

        /// <summary>
        /// 新增分类信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<FdDataRetBool> InsertPosFtData(InsertFtDataContext context)
        {
            var drive = new PosFoodManageDrive();
            return drive.Pft.InsertPosFtData(context);
        }

        /// <summary>
        /// 删除分类信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<FdDataRetBool> DeletePosFtData(DeletePosFtDataContext context)
        {
            var drive = new PosFoodManageDrive();
            return drive.Pft.DeletePosFtData(context);
        }

        /// <summary>
        /// 编辑商品分类信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<FdDataRetBool> EditPosFtData(EditPosFtDataContext context)
        {
            var drive = new PosFoodManageDrive();
            return drive.Pft.EditPosFtData(context);
        }
    }
}
