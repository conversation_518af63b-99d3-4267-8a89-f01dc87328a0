﻿using Saas.Pos.Model.Rms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Extend
{
    public static class ShopTimeListExtend
    {
        public static shoptimeinfo GetFirst(this List<shoptimeinfo> shoptimeinfos, int shopId, string timeNo)
        {
            return shoptimeinfos.FirstOrDefault(w => w.ShopId == shopId && w.TimeNo == timeNo);
        }
    }
}
