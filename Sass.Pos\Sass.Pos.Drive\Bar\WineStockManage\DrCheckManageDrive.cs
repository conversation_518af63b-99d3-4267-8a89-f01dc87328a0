﻿using ComponentApplicationServiceInterface.Context.Response;
using Newtonsoft.Json;
using Saas.Pos.Application.Lib.Bar;
using Saas.Pos.Common.Log;
using Saas.Pos.Domain.IDrive.WineStockManage;
using Saas.Pos.Drive.Lib;
using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.Bar.WineStockManage
{
    /// <summary>
    /// 授权管理
    /// </summary>
    public class DrCheckManageDrive : WineStorkManageSubDriveBase<WineStockManageDriveBase>, IDrCheckManage
    {
        public DrCheckManageDrive(WineStockManageDriveBase imi, AppSession app) : base(imi, app)
        {
        }

        /// <summary>
        /// 取酒授权
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<SaveDrCheckDataModel> SaveDrCheckData(SaveDrCheckDataContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (string.IsNullOrEmpty(context.iKeyMsg))
                        throw new ExMessage("存酒key不能为空!");
                    if (string.IsNullOrEmpty(context.Wkno) || string.IsNullOrEmpty(context.Name))
                        throw new ExMessage("用户信息不能为空!");

                    WineStockApp w = new WineStockApp();
                    var getCount = w.GetSelectDataCount(context.iKeyMsg);
                    if (getCount.Count <= 0)
                        throw new ExMessage("找不到存酒信息!");

                    var saveCount = w.EditDataBySql(context);
                    if (saveCount <= 0)
                        throw new ExMessage("授权失败!");

                    return new SaveDrCheckDataModel();
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "授权管理-取酒授权\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage("授权失败!");
                }
            });
        }

        /// <summary>
        /// 查询授权信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<RespPaginationModel<GetDrCheckDataModel>> GetDrCheckData(GetDrCheckDataContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    WineStockApp w = new WineStockApp();
                    var data = w.GetDrCheckData(context);

                    return RespPaginationModel<GetDrCheckDataModel>.Package(context.Pagination, data);
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "授权管理-查询授权信息\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage("查询授权信息失败!");
                }
            });
        }

        /// <summary>
        /// 查询授权明细数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<List<GetDrCheckDetailDataModel>> GetDrCheckDetailData(GetDrCheckDetailDataContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    WineStockApp w = new WineStockApp();
                    var data = w.GetDrCheckDetailData(context);

                    return data;
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "授权管理-查询授权明细数据\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage("查询授权明细数据失败!");
                }
            });
        }

    }
}
