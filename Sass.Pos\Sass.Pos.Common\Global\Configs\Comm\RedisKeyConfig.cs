﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Global.Configs.Comm
{
    public class RedisKeyConfig
    {
        /// <summary>
        /// Rms房型Key
        /// </summary>
        public string RtInfoKey = "BookData:RtInfo";
        /// <summary>
        /// Rms房间Key
        /// </summary>
        public string RmsInfoKey = "BookData:RmInfo";
        /// <summary>
        /// 门店信息Key
        /// </summary>
        public string ShopInfoKey = "BookData:ShopInfo";
        /// <summary>
        /// 时段信息Key
        /// </summary>
        public string TimeInfoKey = "BookData:TimeInfo";
        /// <summary>
        /// 门店时段信息Key
        /// </summary>
        public string ShopTimeInfoKey = "BookData:ShopTimeInfo";
        /// <summary>
        /// Rms消费模式信息Key
        /// </summary>
        public string ContypeKey = "BookData:ConTypeInfo";

        public string ShopBookRtKey = "BookData:ShopBookRtInfo";

        public string RoomDataKey = "BookData:RoomDataInfo";

        public string BookCacheInfoKey = "BookData:BookCacheInfo";

        public string WorkBookOutInfoKey = "BookData:WorkBookOutInfo";

        public string WorkNotShopInfoKey = "BookData:WorkNotShopInfo";

        public string WorkNotShopTimeKey = "BookData:WorkNotShopTimeInfo";

        public string WorkNotRtInfoKey = "BookData:WorkNotRtInfoInfo";


        public string BookGoodKey = "BookGoodInfo_";

        /// <summary>
        /// 预约号记录持久化保存地址
        /// </summary>
        public string BookNumberKey = "BookNumber:";

        /// <summary>
        /// 同步开房时间Key
        /// </summary>
        public string SyncOpenTimeKey = "SyncOpeningTime";

        /// <summary>
        /// 页面内容配置固定前缀
        /// </summary>
        public string PageConfig = "Page:MiniApp:Config:";

        /// <summary>
        /// 门店下单数据缓存key
        /// </summary>
        public string OrderMenuShopKey = "OrderMenuShop";
    }
}
