﻿using Saas.Pos.Common.MemberInfo.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.MemberInfo.Benefits
{
    /// <summary>
    /// 会员福利基类
    /// </summary>
    /// <typeparam name="T">泛型为数据库实体</typeparam>
    public abstract class BenefitsBase<T>
    {
        public BenefitsBase(BenefitConfigContext<T> Config)
        {
            this.Config = Config;
        }

        public BenefitConfigContext<T> Config { get; set; }

        public abstract bool Check(GetMemberConfigContext context);
    }
}
