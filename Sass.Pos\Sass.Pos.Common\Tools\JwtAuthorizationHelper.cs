﻿using JWT;
using JWT.Algorithms;
using JWT.Exceptions;
using JWT.Serializers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Tools
{
    /// <summary>
    /// Jwt授权鉴权帮助类
    /// </summary>
    public class JwtAuthorizationHelper
    {
        /// <summary>
        /// 授权鉴权密钥
        /// </summary>
        private static readonly string SecretKey = "Saas.Pos";

        /// <summary>
        /// 授权生成JwtToken
        /// </summary>
        /// <param name="payload">加密内容</param>
        /// <param name="header">加密头部</param>
        /// <param name="expireMinutes">过期时间（分钟）</param>
        /// <returns></returns>
        public static string Encode(object payload, Dictionary<string, object> header = null, int expireMinutes = 1)
        {
            var symmetricKey = Encoding.ASCII.GetBytes(SecretKey);
            var provider = GetDateTimeProvider();
            var now = provider.GetNow();
            var unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var secondsSinceEpoch = ((int)Math.Round((now - unixEpoch).TotalSeconds)) + (expireMinutes * 60);

            var subject = new Dictionary<string, object>()
            {
                { "custom",payload },
                { "exp" , secondsSinceEpoch }
            };

            var algorithm = GetJwtAlgorithm();
            var serializer = GetJsonSerializer();
            var urlEncoder = GetBase64UrlEncoder();

            IJwtEncoder encoder = new JwtEncoder(algorithm, serializer, urlEncoder);
            //加密生成token
            return encoder.Encode(header, subject, symmetricKey);
        }

        /// <summary>
        /// 解密Token，返回加密内容
        /// </summary>
        /// <param name="token">token</param>
        /// <returns></returns>
        public static object DecodeToObject(string token)
        {
            var serializer = GetJsonSerializer();
            var provider = GetDateTimeProvider();
            var validator = GetJwtValidator(serializer, provider);
            var urlEncoder = GetBase64UrlEncoder();
            var algorithm = GetJwtAlgorithm();

            IJwtDecoder decoder = new JwtDecoder(serializer, validator, urlEncoder, algorithm);
            try
            {
                var data = decoder.DecodeToObject(token, SecretKey, true);
                return data["custom"];
            }
            catch (TokenExpiredException ex)
            {
                throw new Exception("Token已过期！" + ex.Message);
            }
            catch (SignatureVerificationException ex)
            {
                throw new Exception("Token被篡改，无效Token！" + ex.Message);
            }
        }

        /// <summary>
        /// 解密token，返回加密头部
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        public static Dictionary<string, object> DecodeHeader(string token)
        {
            var serializer = GetJsonSerializer();
            var provider = GetDateTimeProvider();
            var validator = GetJwtValidator(serializer, provider);
            var urlEncoder = GetBase64UrlEncoder();
            var algorithm = GetJwtAlgorithm();

            IJwtDecoder decoder = new JwtDecoder(serializer, validator, urlEncoder, algorithm);
            try
            {
                var data = decoder.DecodeHeader<Dictionary<string, object>>(token);
                return data;
            }
            catch (TokenExpiredException ex)
            {
                throw new Exception("Token已过期！" + ex.Message);
            }
            catch (SignatureVerificationException ex)
            {
                throw new Exception("Token可能被篡改，无效Token！" + ex.Message);
            }
        }

        private static IJsonSerializer GetJsonSerializer()
        {
            return new JsonNetSerializer();
        }

        private static IDateTimeProvider GetDateTimeProvider()
        {
            return new UtcDateTimeProvider();
        }

        private static IJwtValidator GetJwtValidator(IJsonSerializer serializer, IDateTimeProvider provider)
        {
            return new JwtValidator(serializer, provider);
        }

        private static IBase64UrlEncoder GetBase64UrlEncoder()
        {
            return new JwtBase64UrlEncoder();
        }

        private static IJwtAlgorithm GetJwtAlgorithm()
        {
            return new HMACSHA256Algorithm();
        }
    }
}
