﻿using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
    public partial class EmpGift_RecordApp : AppBase<EmpGift_Record>
    {
        public List<GetEmpGiftRecordModel> GetGiftRecordList(GetEmpGiftRecordContext context)
        {
            return Repository.EmpGift_Record.GetGiftRecordList(context);
        }

        public GetEmpGiftRecordInfoModel GetGiftRecordInfo(GetEmpGiftRecordInfoContext context)
        {
            return Repository.EmpGift_Record.GetGiftRecordInfo(context);
        }

        public List<GetMonthEmpGiftRecordModel> GetGiftSummary(GetMonthGiftSummaryContext context)
        {
            return Repository.EmpGift_Record.GetGiftSummary(context);
        }
    }
}
