﻿using Saas.Pos.Common.MiddlewareProxy;
using Saas.Pos.Drive.Common.Objects;
using Saas.Pos.Drive.Common.Proxy.DatResource;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.Common.Factory
{

    /// <summary>
    /// 对象创建工厂类
    /// </summary>
    public static class ObjectsFactory
    {
       

        /// <summary>
        /// 创建中间件代理
        /// </summary>
        /// <returns></returns>
        public static MiddlewareProxy CreateMiddleware()
        {
            return new MiddlewareProxy();
        }
        /// <summary>
        /// 创建缓存代理
        /// </summary>
        /// <returns></returns>
        public static StorageProxy CreateStorage()
        {
            ///缓存代理
            var storage = new StorageProxy();
            return storage;

        }
    }
}
