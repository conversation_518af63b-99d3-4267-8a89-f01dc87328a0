﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.OrderManage
{
    /// <summary>
    /// 订单退款管理
    /// </summary>
    public interface IOrderManageRefund
    {
        /// <summary>
        /// 创建退款单
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<OrderRefundModel> CreateOrderRefundData(CreateOrderRefundContext context);

        /// <summary>
        /// 回调退款
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<ReturnInt> CallbackOrderRefund(CallbackOrderRefund context);

        /// <summary>
        /// 验证退款状态
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<bool> VerifyOrderRefundCash(VerifyOrderRefundCashContext context);

        /// <summary>
        /// 退款校验交易中心状态
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        void TradingCenterOrderRefundCash(User_Order OrderData);
    }
}
