﻿using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Proxy.BookData
{
    /// <summary>
    /// 日期代理基类
    /// </summary>
    public abstract class DateProxyBase
    {
        public int MaxNumber = 0;
        public List<GetBookDateModel> List { get; set; }

        /// <summary>
        /// 生成日期信息
        /// </summary>
        /// <returns></returns>
        internal virtual List<GetBookDateModel> GetDate()
        {
            var rspList = new List<GetBookDateModel>();
            for (int i = 0; i < MaxNumber; i++)
            {
                rspList.Add(new GetBookDateModel()
                {
                    Id = DateTime.Now.AddDays(i).ToString("yyyy-MM-dd"),
                    Name = DateTime.Now.AddDays(i).ToString("MM-dd"),
                    CnCal = i > 0 ? CultureInfo.GetCultureInfo("zh-CN").DateTimeFormat.GetDayName(DateTime.Now.AddDays(i).DayOfWeek) : "今天"
                });
            }
            return rspList;
        }
    }
}
