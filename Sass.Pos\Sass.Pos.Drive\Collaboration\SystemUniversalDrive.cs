﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Rms.Context;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.Collaboration
{
    /// <summary>
    /// SaasPos服务系统通用驱动
    /// </summary>
    public class SystemUniversalDrive
    {
        /// <summary>
        /// MQ消息队列通用发送服务
        /// </summary>
        /// <returns></returns>
        public ResponseContext<bool> QueueSend(QueueSendContext context)
        {
            return ActionFun.Run(context, () =>
            {
                if (string.IsNullOrEmpty(context.Exchange) && string.IsNullOrEmpty(context.QueueName))
                    throw new ExMessage("交换机名称与队列名称至少要有一个！");
                if (string.IsNullOrEmpty(context.Body))
                    throw new ExMessage("不可发送空内容！");

                return AppSingle.App.Middleware.MQ.Server.Publish(context.Exchange ?? string.Empty, context.QueueName, context.Body);
            });
        }

        /// <summary>
        /// 获取Redis中指定key的数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<object> GetKeysValue(GetValueContext context)
        {
            return ActionFun.Run(context, () =>
            {
                if (!AppSingle.App.Middleware.DB_KV.KeyExists(context.Key))
                    throw new ExMessage("查找Key不存在！");

                return AppSingle.App.Middleware.DB_KV.GetVal<object>(context.Key);
            });
        }

        /// <summary>
        /// 设置Redis中指定key的数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<bool> SetKeysValue(SetValueContext context)
        {
            return ActionFun.Run(context, () =>
            {
                if (!AppSingle.App.Middleware.DB_KV.KeyExists(context.Key))
                    throw new ExMessage("查找Key不存在！");

                return AppSingle.App.Middleware.DB_KV.SetVal(context.Key, context.Value, context.ExpireTime);
            });
        }

        public ResponseContext<bool> RefreshBasicData(RefreshBasicDataContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    AppSingle.App.Storage.Refresh();
                    return true;
                }
                catch (Exception ex)
                {
                    throw new Exception("刷新失败！" + ex.Message);
                }
            });
        }
    }
}
