﻿using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.SaasPos
{
    public partial class Way_VerifyItemApp : AppBase<Way_VerifyItem>
    {
        public List<GetRecordListModel> GetRecordList(GetRecordContext context)
        {
            return Repository.Way_VerifyItem.GetRecordList(context);
        }

        public GetVerifyRecordInfoModel GetVerifyInfo(string code)
        {
            return Repository.Way_VerifyItem.GetVerifyInfo(code);
        }

        public VerifyRecordDataModel GetVerifyData(QuickOrderContext context)
        {
            return Repository.Way_VerifyItem.GetVerifyData(context);
        }

        public GetRecordDetailModel GetVerifyItem(GetVerifyItemContext context)
        {
            return Repository.Way_VerifyItem.GetVerifyItem(context);
        }

        public List<List<string>> ExportVerifyItem(GetRecordContext context)
        {
            return Repository.Way_VerifyItem.ExportVerifyItem(context);
        }

        public VerifyItemModel GetVerifyInfo(GetVerifyInfoContext context) 
        {
            return Repository.Way_VerifyItem.GetVerifyInfo(context);
        }
    }
}
