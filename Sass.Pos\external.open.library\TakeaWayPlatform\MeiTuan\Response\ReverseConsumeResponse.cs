﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace external.open.library.TakeaWayPlatform.MeiTuan.Response
{
    public class ReverseConsumeResponse 
    {
        /// <summary>
        /// 验证券码
        /// </summary>
        public string receipt_code { get; set; }
        /// <summary>
        /// 套餐id
        /// </summary>
        public long deal_id { get; set; }
        /// <summary>
        /// 团购id
        /// </summary>
        public long dealgroup_id { get; set; }
        /// <summary>
        /// 商品名称
        /// </summary>
        public string deal_title { get; set; }
        /// <summary>
        /// 商品售卖价格
        /// </summary>
        public double deal_price { get; set; }
        /// <summary>
        /// 商品市场价
        /// </summary>
        public double deal_marketprice { get; set; }
        /// <summary>
        /// 用户手机号,形如:185****1212
        /// </summary>
        public string mobile { get; set; }
        /// <summary>
        /// 第三方的店铺id，基于商户授权时需要填写
        /// </summary>
        public string app_shop_id { get; set; }
        /// <summary>
        /// 开放平台加密店铺id，基于客户授权时需要填写
        /// </summary>
        public string open_shop_uuid { get; set; }
    }
}
