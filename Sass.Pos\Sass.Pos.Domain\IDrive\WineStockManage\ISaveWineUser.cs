﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.WineStockManage
{
    public interface ISaveWineUser
    {
        /// <summary>
        /// 存酒
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<SaveWineUserExModel> SaveWineUser(SaveWineUserContext context);

        /// <summary>
        /// 续存酒
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<SaveWineUserExModel> ContinueSaveWineUser(ContinueSaveWineUserContext context);
    }
}
