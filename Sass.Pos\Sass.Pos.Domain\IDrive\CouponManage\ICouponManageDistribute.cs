﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.CouponManage
{
    public interface ICouponManageDistribute
    {
        /// <summary>
        /// 优惠卷派发
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<ReturnInt> CouponDataDistribute(CouponDataDistribute context);

        /// <summary>
        /// 管理平台优惠卷派发
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<ReturnInt> CouponDataDistributeEx(CouponDataDistributeEx context);

        /// <summary>
        /// 大转盘活动派发
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<EventsDistributeModel> EventsDistribute(EventsDistributeContext context);

        /// <summary>
        /// 撤销赠送优惠券
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<CancelCamCouponDisModel> CancelCamCouponDis(CancelCamCouponDisContext context);
    }
}
