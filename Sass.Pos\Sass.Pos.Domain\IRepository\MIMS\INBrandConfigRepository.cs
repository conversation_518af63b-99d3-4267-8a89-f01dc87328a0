﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.MIMS;
using Saas.Pos.Model.MIMS.Context;
using Saas.Pos.Model.MIMS.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.MIMS
{
    public partial interface INBrandConfigRepository : IRepositoryBase<NBrandConfig>
    {
        List<GetNBrandConfigInfoModel> GetNBrandConfig(GetNUseDataContext context);
    }
}
