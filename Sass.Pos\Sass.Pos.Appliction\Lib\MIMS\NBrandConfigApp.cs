﻿using Saas.Pos.Model.MIMS;
using Saas.Pos.Model.MIMS.Context;
using Saas.Pos.Model.MIMS.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.MIMS
{
    public partial class NBrandConfigApp : AppBase<NBrandConfig>
    {
        public List<GetNBrandConfigInfoModel> GetNBrandConfig(GetNUseDataContext context)
        {
            return Repository.NBrandConfig.GetNBrandConfig(context);
        }
    }
}
