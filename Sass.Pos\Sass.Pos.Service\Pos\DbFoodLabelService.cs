using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Common.Factory;
using Saas.Pos.Domain.IService.Pos;
using Saas.Pos.Drive.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Service.Pos
{
    public partial class PosService : IDbFoodLabelService
    {
        public ResponseContext<int> BatchImport(List<BatchImportContext> context)
        {
            var drive = new DbFoodLabelDrive();
            return drive.BatchImport(context);
        }

        public ResponseContext<int> CreateLabel(CreateLabelContext context)
        {
            var drive = new DbFoodLabelDrive();
            return drive.CreateLabel(context);
        }

        public ResponseContext<int> Delete(DeleteFoodLabelContext context)
        {
            var drive = new DbFoodLabelDrive();
            return drive.Delete(context);
        }

        public ResponseContext<List<KeyValuePair<int, string>>> GetCates(GetFtDropDownContext context)
        {
            var drive = new DbFoodLabelDrive();
            return drive.GetCates(context);
        }

        public ResponseContext<RespPaginationModel<GetFoodLabelListModel>> GetFoodLabelList(FoodLabelContext context)
        {
            var drive = new DbFoodLabelDrive();
            return drive.GetFoodLabelList(context);
        }

        public ResponseContext<List<KeyValuePair<string, string>>> GetFtDropDown(GetFtDropDownContext context)
        {
            var drive = new DbFoodLabelDrive();
            return drive.GetFtDropDown(context);
        }

        public ResponseContext<GetFoodLabelInfoModel> GetLabelInfo(GetFoodLabelInfoContext context)
        {
            var drive = new DbFoodLabelDrive();
            return drive.GetLabelInfo(context);
        }

        public ResponseContext<List<KeyValuePair<int, string>>> GetTypes(GetFtDropDownContext context)
        {
            var drive = new DbFoodLabelDrive();
            return drive.GetTypes(context);
        }

        public ResponseContext<List<GetCheckOutDataModel>> GetCheckOutData(GetCheckOutDataContext context)
        {
            var drive = new DbFoodLabelDrive();
            return drive.GetCheckOutData(context);
        }

        public ResponseContext<bool> CheckOutOrder(CheckOutOrderContext context)
        {
            var drive = new DbFoodLabelDrive();
            return drive.CheckOutOrder(context);
        }

        public ResponseContext<List<GetCheckOutDataModel>> GetCheOutList(GetCheckOutDataContext context) 
        {
            var drive = new DbFoodLabelDrive();
            return drive.GetCheOutList(context);
        }

        public ResponseContext<bool> CheckOut(CheckOutOrderContext context) 
        {
            var drive = new DbFoodLabelDrive();
            return drive.CheckOut(context);
        }
    }
}
