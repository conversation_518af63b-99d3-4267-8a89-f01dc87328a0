﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.OrderManage
{
    public interface IOrderShopMakeDayData
    {
        /// <summary>
        /// 获取每日使用数据汇总导出
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<UserOrderReturnModel> GetShopMakeData(GetShopMakeDataExContext context);

        /// <summary>
        /// 查询每日数据汇总
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<List<GetMakeDataByDay>> GetShopMakeExData(GetShopMakeDataContextByDay context);

        /// <summary>
        /// 查询个人导出报表数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<RespPaginationModel<GetMakeFileDataModel>> GetMakeFileData(GetMakeFileDataContext context);

        /// <summary>
        /// 更新下载次数
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        ResponseContext<EditFileNumberModel> EditFileNumber(EditFileNumberContext context);

        /// <summary>
        /// 查询文件数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<GetFileDataModel> GetFileData(EditFileNumberContext context);

        }
}
