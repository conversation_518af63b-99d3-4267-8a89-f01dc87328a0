﻿using ComponentApplicationServiceInterface.Context.Response;
using Newtonsoft.Json;
using Saas.Pos.Application.Lib.Bar;
using Saas.Pos.Appliction.Lib.Bar;
using Saas.Pos.Common.Log;
using Saas.Pos.Domain.IDrive.WineStockManage;
using Saas.Pos.Drive.Lib;
using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using Saas.Pos.Model.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.Bar.WineStockManage
{
    /// <summary>
    /// 存酒
    /// </summary>
    public class SaveWineUserDrive : WineStorkManageSubDriveBase<WineStockManageDriveBase>, ISaveWineUser
    {
        public SaveWineUserDrive(WineStockManageDriveBase imi, AppSession app) : base(imi, app)
        {
        }

        /// <summary>
        /// 存酒
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<SaveWineUserExModel> SaveWineUser(SaveWineUserContext context)
        {
            try
            {
                return ActionFun.Run(context, () =>
                {
                    //var result = new SaveWineUserExModel() { IsSave = true };
                    if (string.IsNullOrEmpty(context.CustTel) || context.DrinksData.Count <= 0)
                        throw new ExMessage("用户电话和存酒数据不能为空!");

                    WineStockApp w = new WineStockApp();

                    var saveCount = w.InsertWineUser(context, WineUserStatusEnum.存酒未取并没发短信);
                    if (saveCount <= 0)
                        throw new ExMessage("存酒失败!");

                    return new SaveWineUserExModel();
                });
            }
            catch (Exception ex)
            {
                LogHelper.Info(DateTime.Now + "用户存酒\n参数:" + JsonConvert.SerializeObject(context) + "错误原因:" + ex.Message);
                throw new ExMessage("存酒失败!" + ex.Message);
            }
        }

        /// <summary>
        /// 续存酒
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<SaveWineUserExModel> ContinueSaveWineUser(ContinueSaveWineUserContext context)
        {
            try
            {
                return ActionFun.Run(context, () =>
                {
                    //var result = new SaveWineUserExModel() { IsSave = true };
                    if (context == null || string.IsNullOrEmpty(context.MsgPassWord) || string.IsNullOrEmpty(context.RmNo))
                        throw new ExMessage("取酒编号和Key不能为空!");
                    if (context.ShopId <= 0)
                        throw new ExMessage("门店ID不能为空!");
                    WineStockApp w = new WineStockApp();
                    //用户名下存酒明细
                    var drinksData = w.GetUserCustData(new UserCustDataContext() { MsgPassWord = context.MsgPassWord }).FirstOrDefault();
                    if (drinksData == null || drinksData.DrinksDatas.Count() <= 0)
                        throw new ExMessage("名下暂无存酒!");

                    if (w.UpdateMsgInfoState(context.MsgPassWord, context.BarName, $"续存取酒{drinksData.CustData.ReNew}", context.ShopId, context.RmNo) <= 0)
                        throw new ExMessage("修改状态失败!");

                    var count = w.InsertWineUser(new SaveWineUserContext()
                    {
                        CustName = drinksData.CustData.CustName,
                        CustTel = drinksData.CustData.CustTel,
                        DeBarName = context.BarName,
                        OpenId = drinksData.CustData.OpenId,
                        ShopId = context.ShopId,
                        RmNo = context.RmNo,
                        ReNew = drinksData.CustData.ReNew + 1,
                        DrinksData = drinksData.DrinksDatas.Select(i => new UserDrinksExData()
                        {
                            DrinksName = i.DrinksName,
                            DrinksQty = i.DrinksQty,
                            Unit = i.Unit
                        }).ToList()
                    }, WineUserStatusEnum.存酒未取);
                    if (count <= 0)
                        throw new ExMessage("续存酒失败!");

                    //result.IsSave = count > 0;
                    return new SaveWineUserExModel();
                });
            }
            catch (Exception ex)
            {
                LogHelper.Info(DateTime.Now + "用户存酒\n参数:" + JsonConvert.SerializeObject(context) + "错误原因:" + ex.Message);
                throw new ExMessage("存酒失败!" + ex.Message);
            }
        }

    }
}
