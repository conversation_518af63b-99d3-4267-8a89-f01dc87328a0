﻿using Saas.Pos.Application.Lib.MIMS;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.Enum;
using Saas.Pos.Model.MIMS;
using Saas.Pos.Model.MIMS.Context;
using Saas.Pos.Model.MIMS.Model;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.MIMS
{
    public partial class MemberInfoApp : AppBase<MemberInfo>
    {
        public int MemBerDeductMoney(MemBerAcc context)
        {
            return Repository.MemberInfo.MemBerDeductMoney(context);
        }

        /// <summary>
        /// 获取在线预订每日新关数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetBookingNewAttDayDataModel> GetBookingNewAttDayData(GetBookingNewAttDayDataContext context)
        {
            return Repository.MemberInfo.GetBookingNewAttDayData(context);
        }

        /// <summary>
        /// 导出获取在线预订每日新关数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<ExportBookingData> ExportGetBookingNewAttDayData(ExportBookingNewAttDayDataContext context)
        {
            return Repository.MemberInfo.ExportGetBookingNewAttDayData(context);
        }

        /// <summary>
        /// 调用提成绑定存储过程
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public MemberTopUpEditModel MemberTopUpEdit(MemberTopUpEditContext context)
        {
            return Repository.MemberInfo.MemberTopUpEdit(context);
        }

        public List<GetMemberCardRecordDataModel> GetMemberCardRecordData(GetMemberCardRecordDataContext context)
        {
            var data = Repository.MemberInfo.GetMemberCardRecordData(context);

            data.Add(new GetMemberCardRecordDataModel()
            {
                ShopName = "总和",
                MemberNum = data.Sum(i => i.MemberNum),
                TwNum = data.Sum(i => i.TwNum),
                MtNum = data.Sum(i => i.MtNum),
                ThNum = data.Sum(i => i.ThNum),
                DiffNum = data.Sum(i => i.DiffNum)
            });

            return data;
        }

        public bool IntegralOreder(IntegralOrederContext context)
        {
            var data = Repository.MemberInfo.IntegralOreder(context);
            return data;
        }

        public BindMemberInfo GetBindMemberInfo(string cardNumber)
        {
            return Repository.MemberInfo.GetBindMemberInfo(cardNumber);
        }

        public int InsertMember(MemberInfo member)
        {
            return Repository.MemberInfo.InsertMember(member);
        }

        public int InsertMemberCard(MemberCardInfo cardInfo)
        {
            return Repository.MemberInfo.InsertMemberCard(cardInfo);
        }

        public int UpdateMemberPhone(string phone, Guid memberKye)
        {
            return Repository.MemberInfo.UpdateMemberPhone(phone, memberKye);
        }

        public int InsertRecord(MemberGiftRecord memberGift)
        {
            return Repository.MemberInfo.InsertRecord(memberGift);
        }
    }
}
