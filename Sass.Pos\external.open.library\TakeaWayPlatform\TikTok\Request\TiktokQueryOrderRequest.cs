﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace external.open.library.TakeaWayPlatform.TikTok.Request
{
    public class TiktokQueryOrderRequest
    {
        public string account_id { get; set; }
        public int page_num { get; set; }
        public int page_size { get; set; }
        public long create_order_end_time { get; set; }
        public long create_order_start_time { get; set; }
        public string order_id { get; set; }
        public int order_status { get; set; }
        public string open_id { get; set; }
        public long update_order_end_time { get; set; }
        public long update_order_start_time { get; set; }
    }
}
